<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\MessageRecipient;

class Messages extends Component
{
    public $messages = [];
    public $unreadCount = 0;
    public $showInstallCard = false;
    public $showNotificationCard = false;

    public function mount()
    {
        $this->loadMessages();
        $this->checkPWAStatus();
    }

    public function loadMessages()
    {
        $user = Auth::user();

        // Get all messages for the current user, ordered by sent date
        $this->messages = MessageRecipient::with('message')
            ->where('user_id', $user->id)
            ->orderBy('read', 'asc') // Unread first
            ->orderBy('sent_date', 'desc')
            ->get();

        // Count unread messages
        $this->unreadCount = $this->messages->where('read', false)->count();
    }

    public function checkPWAStatus()
    {
        // These will be checked on the client side via JavaScript
        // We set them to true by default, and JavaScript will hide them if not needed
        $this->showInstallCard = true;
        $this->showNotificationCard = true;
    }

    public function viewMessage($messageRecipientId)
    {
        return redirect()->route('mobile.message-detail', $messageRecipientId);
    }

    public function render()
    {
        return view('livewire.mobile.messages');
    }
}

