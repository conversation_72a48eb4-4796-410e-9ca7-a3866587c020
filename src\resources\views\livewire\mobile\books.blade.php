<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white px-4 py-6 border-b border-gray-100">
        <h1 class="text-2xl font-bold text-purple-800 mb-4">{{ __('mobile.my_books') }}</h1>
    </div>

    <div class="p-4">

        <!-- Success Message -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        <!-- Reading Section -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">{{ __('mobile.reading') }}</h2>

            <div class="flex space-x-4 overflow-x-auto pb-4">
                <!-- Add New Book Card -->
                <div class="flex-shrink-0">
                    <a href="{{ route('mobile.books.add') }}" class="block">
                        <div class="w-24 h-36 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center bg-white hover:border-purple-400 transition-colors">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mb-2">
                                <img src="/images/plus-icon.png" alt="Add" class="w-5 h-5 filter brightness-0 invert">
                            </div>
                            <span class="text-xs font-semibold text-gray-600 text-center px-1">{{ __('mobile.add_new') }}</span>
                        </div>
                    </a>
                </div>

                <!-- Current Books -->
                @if($currentBooks && $currentBooks->count() > 0)
                    @foreach($currentBooks as $userBook)
                        <div class="flex-shrink-0">
                            <a href="{{ route('mobile.books.log', $userBook->book->id) }}" class="block">
                                <div class="w-24 h-36 relative hover:scale-105 transition-transform">
                                    @if ($userBook->book->cover_image)
                                        <img src="{{ asset('storage/' . $userBook->book->cover_image) }}"
                                             alt="{!! $userBook->book->name !!}"
                                             class="w-full h-full object-cover rounded-lg shadow-md">
                                    @else
                                    <div class="relative w-full h-full rounded-lg shadow-md overflow-hidden">
                                        <img src="/images/default-book-cover.png"   
                                            alt="{!! $userBook->book->name !!}"
                                            class="w-full h-full object-cover rounded-lg shadow-md">
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-white text-xs text-center px-2">
                                                {!! $userBook->book->name !!}
                                            </span>
                                        </div>
                                    </div>
                                    @endif    
                                </div>
                            </a>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Completed Section -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">{{ __('mobile.completed') . ' (' . $completedBooks->count() . ')'}}</h2>

            <!-- Congratulatory Message for Top 3 Readers -->
            @if($classRankingData)
                <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl p-4 mb-3 shadow-lg">
                    <div class="flex items-center mb-3">
                        <div class="flex-1">
                            <h3 class="text-white font-bold text-lg">
                                @if($classRankingData['is_first_place'])
                                    🏆 {{ __('mobile.congratulations_first_place') }}
                                @else
                                    🎉 {{ __('mobile.congratulations_top_reader') }}
                                @endif
                            </h3>
                        </div>
                    </div>

                    <div class="text-white text-sm mb-1">
                        @if($classRankingData['is_first_place'])
                            {{ __('mobile.first_place_message', [
                                'books' => $classRankingData['completed_books'],
                                'class' => $classRankingData['class_name']
                            ]) }}
                        @else
                            {{ __('mobile.top_reader_message', [
                                'rank' => $classRankingData['current_rank'],
                                'books' => $classRankingData['completed_books'],
                                'class' => $classRankingData['class_name']
                            ]) }}
                        @endif
                    </div>

                    @if(!$classRankingData['is_first_place'] && $classRankingData['books_needed_for_next_rank'] > 0)
                        <div class="rounded-xl p-1 bg-yellow-50">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-red mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                <span class="text-yellow-900 text-sm font-medium">
                                    {{ __('mobile.books_needed_next_rank', [
                                        'books' => $classRankingData['books_needed_for_next_rank'],
                                        'rank' => $classRankingData['next_rank_position']
                                    ]) }}
                                </span>
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Search Bar -->
            @if($completedBooks && $completedBooks->count() > 10)
            <div class="mb-4">
                <input
                    type="text"
                    wire:model.live="searchQuery"
                    placeholder="{{ __('mobile.type_book_name_to_search') }}"
                    class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
            </div>
            @endif

            <!-- Completed Books -->
            @if($completedBooks && $completedBooks->count() > 0)
                <div class="flex flex-wrap space-x-3 space-y-3 pb-4">
                    @foreach($completedBooks as $userBook)
                        <div class="flex-shrink-0 relative">
                            <a href="{{ route('mobile.books.activities', $userBook->book->id) }}" class="block">
                                <div class="w-24 h-36 relative hover:scale-105 transition-transform">
                                    @if ($userBook->book->cover_image)
                                        <img src="{{ asset('storage/' . $userBook->book->cover_image) }}"
                                             alt="{!! $userBook->book->name !!}"
                                             class="w-full h-full object-cover rounded-lg shadow-md">
                                    @else
                                    <div class="relative w-full h-full rounded-lg shadow-md overflow-hidden">
                                        <img src="/images/default-book-cover.png"   
                                            alt="{{ $userBook->book->name }}"
                                            class="w-full h-full object-cover rounded-lg shadow-md">
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-white text-xs text-center px-2">
                                                {!! $userBook->book->name !!}
                                            </span>
                                        </div>
                                    </div>
                                    @endif
                                        
                                    @if($this->hasIncompleteRequiredActivities($userBook->book))
                                        <div class="absolute top-0 right-0 transform translate-x-2 translate-y-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-xs font-bold">!</span>
                                        </div>
                                    @endif
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- No Completed Books -->
                <div class="bg-white rounded-xl p-8 text-center shadow-sm">
                    <div class="w-16 h-16 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                        <img src="/images/checkmark-icon.png" alt="Completed" class="w-8 h-8 opacity-50">
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('mobile.no_completed_books') }}</h3>
                    <p class="text-gray-600 text-sm">{{ __('mobile.complete_first_book') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>

