<div class="min-h-screen bg-base-200">
    <x-mobile-page-header route="{{ route('mobile.books') }}" header="Book Details" />

    <div class="p-4">
        <!-- Book Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm relative">
            @php
                $progress = $this->getReadingProgress();
                $totalPagesRead = $this->getTotalPagesRead();
            @endphp

            @if($progress > 70)
                <div class="absolute top-1 right-1 px-2 py-1 text-xs font-semibold text-black bg-green-200 rounded-full z-10">
                    {{ __('mobile.almost_there')  }}
                </div>
            @endif               
            <div class="flex items-start space-x-4">
                <img src="{{ $book->cover_image ? asset('storage/' . $book->cover_image) : '/images/default-book-cover.png' }}"
                     alt="{!! $book->name !!}"
                     class="mobile-book-cover">

                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{!! $book->name !!}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $book->authors->pluck('name')->join(', ') }}</p>

                    <!-- Progress Stats -->
                    <div class="grid grid-cols-3 gap-4 mt-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary">{{ $totalPagesRead }}</div>
                            <div class="text-xs text-gray-600"> {{ __('mobile.pages_read') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-500">{{ number_format($progress, 0) }}%</div>
                            <div class="text-xs text-gray-600">{{ __('mobile.progress') }}</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-500">{{ $this->getReadingStreak() }}</div>
                            <div class="text-xs text-gray-600">{{ __('mobile.day_streak') }}</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    @if($book->page_count)
                        <div class="mobile-progress-bar mt-4">
                            <div class="mobile-progress-fill" style="width: {{ $this->getReadingProgress() }}%"></div>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600 mt-1">
                            <span>{{ $totalPagesRead }} / {{ $book->page_count }} pages</span>
                            <span>{{ number_format($progress, 0) }}%</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Reading Log Form -->
        <div class="bg-purple-100 rounded-2xl p-6 mb-6 shadow-sm">
            <h3 class="font-semibold text-gray-900 mb-4">{{ __('mobile.log_reading') }}</h3>

            <form wire:submit="addReadingLog" class="space-y-4">
                <!-- Date Toggle -->
                <div>
                    <div class="flex bg-white rounded-2xl p-1 shadow-sm">
                        <button
                            type="button"
                            wire:click="$set('dateToggle', 'today')"
                            class="flex-1 py-1 px-1 rounded-xl font-semibold transition-all {{ $dateToggle === 'today' ? 'bg-purple-400 text-white' : 'text-gray-600' }}"
                        >
                            {{ __('mobile.today') }}
                        </button>
                        <button
                            type="button"
                            wire:click="$set('dateToggle', 'yesterday')"
                            class="flex-1 py-1 px-1 rounded-xl font-semibold transition-all {{ $dateToggle === 'yesterday' ? 'bg-purple-400 text-white' : 'text-gray-600' }}"
                        >
                            {{ __('mobile.yesterday') }}
                        </button>
                    </div>
                    @error('dateToggle')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Pages Read -->
                <div>
                    <input
                        type="number"
                        wire:model="pagesRead"
                        class="w-full text-4xl font-light text-center border-0 bg-white focus:ring-0 @error('pagesRead') text-red-500 @enderror"
                        placeholder="0"
                        min="1"
                    >
                    <div class="text-center text-gray-600 text-lg"> {{ __('mobile.pages') }} </div>
                    @error('pagesRead')
                        <p class="text-red-500 text-sm mt-1 text-center">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Minutes Read -->
                <div>
                    <input
                        type="number"
                        wire:model="minutesRead"
                        class="w-full text-4xl font-light text-center border-0 bg-white focus:ring-0"
                        placeholder="0"
                        min="1"
                    >
                    <div class="text-center text-gray-600 text-lg">{{ __('mobile.minutes') }}</div>
                    @error('minutesRead')
                        <p class="text-red-500 text-sm mt-1 text-center">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Checkbox -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="promise"
                            wire:model="promiseChecked"
                            class="w-5 h-5 text-purple-600 rounded @error('promiseChecked') border-red-500 @enderror"
                        >
                        <label for="promise" class="text-sm text-red-700 font-semibold">{{ __('mobile.promise_info') }}</label>
                    </div>
                    @error('promiseChecked')
                        <p class="text-red-500 text-sm mt-1 text-center">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <button
                        type="submit"
                        class="flex-1 bg-purple-600 text-white py-3 px-6 rounded-xl font-semibold flex items-center justify-center {{ $isLoading ? 'opacity-50' : '' }}"
                        wire:loading.attr="disabled"
                    >
                        <span wire:loading.remove> {{ __('mobile.save_log_button') }}</span>
                        <span wire:loading class="flex items-center justify-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {{ __('mobile.saving') }}...
                        </span>
                    </button>
                    <button
                        type="button"
                        wire:click="completeBook"
                        class="flex-1 bg-green-600 text-white py-3 px-6 rounded-xl font-semibold {{ $isLoading ? 'opacity-50' : '' }}"
                        wire:loading.attr="disabled"
                    >
                        {{ __('mobile.complete_the_book_button') }}
                    </button>
                </div>
            </form>
        </div>

        <!-- Reading Logs List -->
        <div class="bg-white rounded-2xl p-4 shadow-sm">
            <h3 class="font-semibold text-gray-900 mb-4">{{ __('mobile.reading_history') }}</h3>

            @if($readingLogs->count() > 0)
                <div class="space-y-3">
                    @foreach($readingLogs as $log)
                        <div class="border border-gray-200 rounded-xl p-4">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ $log->log_date->format('M d, Y') }}</h4>
                                    <p class="text-sm text-primary font-semibold">{{ $log->pages_read }} {{ __('mobile.pages_read') }} ({{ $log->reading_duration }} {{ __('mobile.minutes') }})</p>
                                </div>
                                <button
                                    wire:click="deleteLog({{ $log->id }})"
                                    wire:confirm="Are you sure you want to delete this reading log?"
                                    class="text-red-500 hover:text-red-700 text-sm"
                                >
                                    🗑️
                                </button>
                            </div>

                            @if($log->notes)
                                <div class="bg-gray-50 rounded-lg p-3 mt-2">
                                    <p class="text-sm text-gray-700">{{ $log->notes }}</p>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2"> {{ __('mobile.no_reading_logs') }}</h3>
                    <p class="text-gray-600">{{ __('mobile.start_tracking_progress') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>
