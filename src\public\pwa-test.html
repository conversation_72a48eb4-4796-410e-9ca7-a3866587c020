<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Test - Okumobil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .icon-test {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon-test img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
        }
        .status {
            margin-top: 10px;
            padding: 5px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .manifest-test {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>PWA Icon and Manifest Test</h1>
    
    <div class="test-section">
        <h2>Manifest File Test</h2>
        <div class="manifest-test">
            <p><strong>Manifest URL:</strong> <a href="/manifest.json" target="_blank">/manifest.json</a></p>
            <button onclick="testManifest()">Test Manifest</button>
            <div id="manifest-result"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>Icon Files Test</h2>
        <div class="icon-grid" id="icon-grid">
            <!-- Icons will be loaded here -->
        </div>
    </div>

    <div class="test-section">
        <h2>PWA Features Test</h2>
        <div id="pwa-features">
            <p><strong>Service Worker:</strong> <span id="sw-status">Testing...</span></p>
            <p><strong>Install Prompt:</strong> <span id="install-status">Testing...</span></p>
            <p><strong>Standalone Mode:</strong> <span id="standalone-status">Testing...</span></p>
        </div>
    </div>

    <script>
        const icons = [
            { src: '/icons/icon-48x48.png', size: '48x48' },
            { src: '/icons/icon-72x72.png', size: '72x72' },
            { src: '/icons/icon-96x96.png', size: '96x96' },
            { src: '/icons/icon-128x128.png', size: '128x128' },
            { src: '/icons/icon-144x144.png', size: '144x144' },
            { src: '/icons/icon-152x152.png', size: '152x152' },
            { src: '/icons/icon-192x192.png', size: '192x192' },
            { src: '/icons/icon-384-384.png', size: '384x384' },
            { src: '/icons/icon-512x512.png', size: '512x512' }
        ];

        function testManifest() {
            fetch('/manifest.json')
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    }
                    throw new Error(`HTTP ${response.status}`);
                })
                .then(manifest => {
                    document.getElementById('manifest-result').innerHTML = `
                        <div class="status success">✅ Manifest loaded successfully</div>
                        <pre>${JSON.stringify(manifest, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    document.getElementById('manifest-result').innerHTML = `
                        <div class="status error">❌ Manifest failed to load: ${error.message}</div>
                    `;
                });
        }

        function testIcon(iconSrc, iconSize) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ src: iconSrc, size: iconSize, status: 'success' });
                img.onerror = () => resolve({ src: iconSrc, size: iconSize, status: 'error' });
                img.src = iconSrc;
            });
        }

        function loadIconTests() {
            const iconGrid = document.getElementById('icon-grid');
            
            icons.forEach(async (icon) => {
                const result = await testIcon(icon.src, icon.size);
                
                const iconDiv = document.createElement('div');
                iconDiv.className = 'icon-test';
                iconDiv.innerHTML = `
                    <h4>${icon.size}</h4>
                    <img src="${icon.src}" alt="${icon.size} icon" style="width: 64px; height: 64px;">
                    <div class="status ${result.status}">
                        ${result.status === 'success' ? '✅ Loaded' : '❌ Failed'}
                    </div>
                    <small>${icon.src}</small>
                `;
                
                iconGrid.appendChild(iconDiv);
            });
        }

        function testPWAFeatures() {
            // Test Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration()
                    .then(registration => {
                        document.getElementById('sw-status').innerHTML = 
                            registration ? '✅ Registered' : '⚠️ Not registered';
                    })
                    .catch(() => {
                        document.getElementById('sw-status').innerHTML = '❌ Error';
                    });
            } else {
                document.getElementById('sw-status').innerHTML = '❌ Not supported';
            }

            // Test Install Prompt
            document.getElementById('install-status').innerHTML = 
                window.deferredPrompt ? '✅ Available' : '⚠️ Not available';

            // Test Standalone Mode
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                                window.navigator.standalone === true;
            document.getElementById('standalone-status').innerHTML = 
                isStandalone ? '✅ Running as PWA' : '⚠️ Running in browser';
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', () => {
            testManifest();
            loadIconTests();
            testPWAFeatures();
        });
    </script>
</body>
</html>
