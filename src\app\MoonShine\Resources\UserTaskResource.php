<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserTask;
use App\Models\ChallengeTask;
use App\Models\Task;
use App\Models\User;
use App\Models\Team;
use App\Models\SchoolClass;
use App\Models\Reward;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\UI\Fields\Select;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder;
use MoonShine\Laravel\QueryTags\QueryTag;
use MoonShine\UI\Fields\Date;

#[Icon('clipboard-document-check')]
class UserTaskResource extends BaseResource
{
    //TODO: challenge tasks display problem in detail fields
    
    protected string $model = UserTask::class;

    protected string $column = 'display_name';

    protected array $with = [
        'challengeTask.task',
        'challengeTask.challenge',
        'user',
        'team',
        'schoolClass',
        'reward'
    ];

    public function getTitle(): string
    {
        return __('admin.user_tasks');
    }

    public function queryTags(): array
    {
        $tags = [];
        $tags[] = QueryTag::make(
            __('admin.standalone_tasks'),
            static fn(Builder $query) => $query->where('task_type', UserTask::TASK_TYPE_STANDALONE)
        );
        $tags[] = QueryTag::make(
            __('admin.challenge_tasks'),
            static fn(Builder $query) => $query->where('task_type', UserTask::TASK_TYPE_CHALLENGE)
        );

        return $tags;
    }
    

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            Select::make(__('admin.task_type'), 'task_type')
                ->options([
                    UserTask::TASK_TYPE_STANDALONE => __('admin.standalone_task'),
                    UserTask::TASK_TYPE_CHALLENGE => __('admin.challenge_task'),
                ])
                ->sortable(),

            Text::make(__('admin.task_name'), 'display_name'),

            Text::make(__('admin.assignee'), 'assignee_display'),

            Text::make(__('admin.progress'), 'progress_display'),

            Number::make(__('admin.progress_percentage'), 'progress_percentage')
                ->sortable(),

            Switcher::make(__('admin.completed'), 'completed')
                ->sortable(),

            Date::make(__('admin.complete_date'), 'complete_date')
                ->format('d.m.Y H:i'),

            Text::make(__('admin.status'), 'status_display'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.task_assignment'), [
                Flex::make([
                    Select::make(__('admin.task_type'), 'task_type')
                        ->options([
                            UserTask::TASK_TYPE_STANDALONE => __('admin.standalone_task'),
                            UserTask::TASK_TYPE_CHALLENGE => __('admin.challenge_task'),
                        ])
                        ->required(),
                    BelongsTo::make(
                        __('admin.task'),
                        'task',
                        formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                        resource: TaskResource::class
                    )
                        ->nullable()
                        ->searchable()
                        ->showWhen('task_type', UserTask::TASK_TYPE_STANDALONE),

                    BelongsTo::make(
                        __('admin.challenge_task'),
                        'challengeTask',
                        formatted: fn(?ChallengeTask $challengeTask) => $challengeTask ? $challengeTask->challenge->name . ' - ' . $challengeTask->task->name : '-',
                        resource: ChallengeTaskResource::class
                    )
                        ->nullable()
                        ->searchable()
                        ->showWhen('task_type', UserTask::TASK_TYPE_CHALLENGE),
                ]),

                Flex::make([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: UserResource::class
                    )
                        ->required()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.team'),
                        'team',
                        formatted: fn(?Team $team) => $team ? $team->name : '-',
                        resource: TeamResource::class
                    )
                        ->nullable()
                        ->searchable(),
                ]),

                Flex::make([
                    BelongsTo::make(
                        __('admin.assigned_by'),
                        'assignedBy',
                        formatted: fn(?User $user) => $user ? $user->name : '-',
                        resource: UserResource::class
                    )
                        ->nullable()
                        ->searchable(),

                    Date::make(__('admin.assign_date'), 'assign_date')
                        ->format('d.m.Y H:i'),

                    Date::make(__('admin.start_date'), 'start_date')
                        ->format('d.m.Y H:i'),

                    Date::make(__('admin.due_date'), 'due_date')
                        ->format('d.m.Y H:i')
                        ->nullable(),
                ]),

                Flex::make([
                    BelongsTo::make(
                        __('admin.class'),
                        'schoolClass',
                        'name',
                        resource: SchoolClassResource::class
                    )
                        ->nullable()
                        ->searchable(),

                    BelongsTo::make(
                        __('admin.reward'),
                        'reward',
                        'name',
                        resource: RewardResource::class
                    )
                        ->nullable()
                        ->searchable(),
                ]),
            ]),

            Box::make(__('admin.completion_status'), [
                Flex::make([
                    Switcher::make(__('admin.completed'), 'completed'),

                    Date::make(__('admin.complete_date'), 'complete_date')
                        ->format('d.m.Y H:i')
                        ->showWhen('completed', true),
                ]),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        $task_type = $this->item->task_type ?? UserTask::TASK_TYPE_STANDALONE;
        $challenge_fields = $task_type == UserTask::TASK_TYPE_CHALLENGE ? [
            BelongsTo::make(
                __('admin.challenge_task'),
                'challengeTask',
                formatted: fn(?ChallengeTask $challengeTask) => $challengeTask ? $challengeTask->challenge->name . ' - ' . $challengeTask->task->name : '-',
                resource: ChallengeTaskResource::class
            ),

        ] : [];

        return [
            Select::make(__('admin.task_type'), 'task_type')
                ->options([
                    UserTask::TASK_TYPE_STANDALONE => __('admin.standalone_task'),
                    UserTask::TASK_TYPE_CHALLENGE => __('admin.challenge_task'),
                ]),
            BelongsTo::make(
                __('admin.task'),
                'task',
                formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                resource: TaskResource::class
            ),
            ...$challenge_fields,    

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.team'),
                'team',
                formatted: fn(?Team $team) => $team ? $team->name : '-',
                resource: TeamResource::class
            ),

            BelongsTo::make(
                __('admin.assigned_by'),
                'assignedBy',
                formatted: fn(?User $user) => $user ? $user->name : '-',
                resource: UserResource::class
            ),

            Text::make(__('admin.assignee'), 'assignee_display'),
            Text::make(__('admin.progress'), 'progress_display'),
            Number::make(__('admin.progress_percentage'), 'progress_percentage'),
            Text::make(__('admin.status'), 'status_display'),

            Date::make(__('admin.assign_date'), 'assign_date')->format('d.m.Y H:i'),
            Date::make(__('admin.start_date'), 'start_date')->format('d.m.Y H:i'),
            Switcher::make(__('admin.completed'), 'completed'),
            Date::make(__('admin.complete_date'), 'complete_date')->format('d.m.Y H:i'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'task_type' => ['required', 'integer', 'in:0,1,2'],
            'user_id' => ['required', 'exists:users,id'],
            'team_id' => ['nullable', 'exists:teams,id'],
            'assigned_by' => ['nullable', 'exists:users,id'],
            'assign_date' => ['nullable', 'date'],
            'start_date' => ['nullable', 'date'],
            'completed' => ['boolean'],
            'complete_date' => ['nullable', 'date'],
            ...parent::getCommonRules($item),
        ];

        // Add conditional validation based on task type
        $taskType = request('task_type') ?? ($item ? $item->task_type : null);
        
        if ($taskType == UserTask::TASK_TYPE_CHALLENGE) {
            $rules['challenge_task_id'] = ['required', 'exists:challenge_tasks,id'];
        }

        return $rules;
    }

    protected function search(): array
    {
        return [
            'challengeTask.task.name',
            'challengeTask.challenge.name',
            'user.name', 
            'team.name'
        ];
    }

    protected function getDefaultSort(): array
    {
        return ['assign_date' => 'desc'];
    }

    /**
     * Modify query builder for role-based access control.
     */
    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        if (!$builder instanceof UseEloquentBuilder) {
            return $builder;
        }
        return $builder->forCurrentUser();
    }
}
