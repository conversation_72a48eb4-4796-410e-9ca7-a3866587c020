<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\UserReward;
use App\Models\Reward;
use App\Models\User;
use MoonShine\UI\Fields\Text;
use MoonShine\UI\Fields\Switcher;
use MoonShine\UI\Fields\Number;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\UI\Fields\Date;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder;

#[Icon('star')]
class UserRewardResource extends BaseResource
{
    protected string $model = UserReward::class;

    protected string $column = 'id';

    protected array $with = [
        'user', 
        'reward', 
        'awarder', 
        'readingLog', 
        'userActivity'
    ];

    public function getTitle(): string
    {
        return __('admin.user_rewards');
    }

    protected function indexFields(): iterable
    {
        return [
            ...parent::getCommonIndexFields(),

            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->sortable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            )
                ->sortable(),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display')
                ->sortable(),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : __('admin.system'),
                resource: UserResource::class
            )
                ->sortable(),

            Date::make(__('admin.awarded_date'), 'awarded_date')
                ->sortable(),

            Text::make(__('admin.award_type'), 'award_type'),

            Text::make(__('admin.trigger_source'), 'trigger_source'),

            Text::make(__('admin.award_age'), 'award_age_text'),
        ];
    }

    protected function formFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name . ' (' . $reward->reward_type_display . ')',
                resource: RewardResource::class
            )
                ->required()
                ->searchable(),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : null,
                resource: UserResource::class
            )
                ->nullable()
                ->searchable()
                ->default(auth('moonshine')->id())
                ->hint(__('admin.leave_empty_for_automatic_award')),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.user'),
                'user',
                formatted: fn(User $user) => $user->name,
                resource: UserResource::class
            ),

            BelongsTo::make(
                __('admin.reward'),
                'reward',
                formatted: fn(Reward $reward) => $reward->name,
                resource: RewardResource::class
            ),

            Text::make(__('admin.reward_type'), 'reward.reward_type_display'),
            Text::make(__('admin.reward_description'), 'reward.description'),

            BelongsTo::make(
                __('admin.awarded_by'),
                'awarder',
                formatted: fn(?User $user) => $user ? $user->name : __('admin.system'),
                resource: UserResource::class
            ),

            Text::make(__('admin.awarded_date'), 'awarded_date'),
            Text::make(__('admin.award_type'), 'award_type'),
            Text::make(__('admin.trigger_source'), 'trigger_source'),
            Text::make(__('admin.award_age'), 'award_age_text'),

            BelongsTo::make(
                __('admin.reading_log'),
                'readingLog',
                formatted: fn($log) => $log ? 'Reading Log #' . $log->id : '-',
                resource: UserReadingLogResource::class
            ),

            BelongsTo::make(
                __('admin.user_activity'),
                'userActivity',
                formatted: fn($activity) => $activity ? 'Activity #' . $activity->id : '-',
                resource: UserActivityResource::class
            ),

            Text::make(__('admin.summary'), 'summary'),

            ...parent::getCommonDetailFields(),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'user_id' => ['required', 'exists:users,id'],
            'reward_id' => ['required', 'exists:rewards,id'],
            'awarded_by' => ['nullable', 'exists:users,id'],
            ...parent::getCommonRules($item),
        ];

        return $rules;
    }

    protected function search(): array
    {
        return [
            'user.name', 
            'reward.name', 
            'awarder.name'
        ];
    }

    protected function getDefaultSort(): array
    {
        return ['awarded_date' => 'desc'];
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        if (!$builder instanceof UseEloquentBuilder) {
            return $builder;
        }
        return $builder->forCurrentUser();
    }
}
