<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ $title ?? __('mobile.app_name') }}</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#7c3aed">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ __('mobile.app_name') }}">   

    <!-- PWA Icons -->     
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    <link rel="apple-touch-icon-precomposed" href="/icons/icon-192x192.png">
    <link rel="mask-icon" href="/icons/safari-pinned-tab.svg" color="#5bbad5">
    <link rel="icon" type="image/png" sizes="192x192" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/icons/icon-512x512.png">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
    
    <!-- Additional head content -->
    {{ $head ?? '' }}
</head>
<body class="font-quicksand bg-base-200 overflow-x-hidden">
    <div class="mobile-container">
        <!-- Main Content -->
        <main class="min-h-screen {{ isset($hasBottomNav) && $hasBottomNav ? 'pb-20' : '' }}">
            {{ $slot }}
        </main>

        <!-- Bottom Navigation (if enabled) -->
        @if(isset($hasBottomNav) && $hasBottomNav)
            <nav class="mobile-bottom-nav">
                <a href="{{ route('mobile.home') }}" class="mobile-nav-item {{ request()->routeIs('mobile.home') ? 'active' : '' }}">
                    <img src="/images/home-icon.png" alt="Home" class="w-6 h-6">
                    <span class="text-xs font-semibold mt-1">{{ __('mobile.home') }}</span>
                </a>

                <a href="{{ route('mobile.books') }}" class="mobile-nav-item {{ request()->routeIs('mobile.books*') ? 'active' : '' }}">
                    <img src="/images/book-icon.png" alt="Books" class="w-6 h-6">
                    <span class="text-xs font-semibold mt-1">{{ __('mobile.my_books') }}</span>
                </a>

                <a href="{{ route('mobile.friends') }}" class="mobile-nav-item {{ request()->routeIs('mobile.friends') ? 'active' : '' }}">
                    <img src="/images/friends-icon.png" alt="Friends" class="w-6 h-6">
                    <span class="text-xs font-semibold mt-1">{{ __('mobile.friends') }}</span>
                </a>

                <a href="{{ route('mobile.me') }}" class="mobile-nav-item {{ request()->routeIs('mobile.me') ? 'active' : '' }}">
                    <img src="/images/user-icon.png" alt="Me" class="w-6 h-6">
                    <span class="text-xs font-semibold mt-1">{{ __('mobile.me') }}</span>
                </a>
            </nav>
        @endif
    </div>

    @livewireScripts
    
    <!-- Additional scripts -->
    {{ $scripts ?? '' }}
</body>
</html>
