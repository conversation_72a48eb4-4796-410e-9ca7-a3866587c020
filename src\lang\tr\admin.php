<?php

return [
	// System
	'system' => 'Sistem',
	'users' => 'Kullanıcılar',
	'roles' => 'Roller',
	'dashboard' => 'Ana Sayfa',
	'parent_dashboard' => 'Ebeveyn Paneli',
	'school_admin_dashboard' => 'Kurum Yöneticisi Paneli',
	'system_admin_dashboard' => 'Sistem Yöneticisi Paneli',
	'system_overview' => 'Sistem Genel Bakış',
	'unauthorized_action' => 'Bu eylem için yetkiniz yok',

	// Academic
	'class_levels' => 'Sınıf Seviyeleri',
	'school_types' => 'Kurum Türleri',
	'school_type' => 'Kurum Türü',
	'class_level' => 'Sınıf Seviyesi',
	'schools' => 'Kurumlar',
	'school_overview' => 'Kurum Genel Bakış',

	'school_classes' => 'Sınıflar',

	// Books
	'book_types' => 'Kitap Türleri',
	'book_type' => 'Kitap Türü',
	'books' => 'Kitaplar',
	'books_localizable' => 'Kitap',
	'authors' => 'Yazarlar',
	'categories' => 'Kategoriler',
	'categories_localizable' => 'Kategori',
	'publishers' => 'Yayınevleri',
	'class_books' => 'Sınıf Kitaplığı',
	'page_points' => 'Sayfa Puanları',
	'reading' => 'Okuma',
	'user_reading_logs' => 'Okuma Kayıtları',
	'user_points' => 'Kullanıcı Puanları',
	'log_date' => 'Kayıt Tarihi',
	'start_page' => 'Başlangıç Sayfası',
	'end_page' => 'Bitiş Sayfası',
	'pages_read' => 'Okunan Sayfa',
	'reading_duration' => 'Okuma Süresi (dk)',
	'book_completed' => 'Kitap Tamamlandı',
	'point_date' => 'Puan Tarihi',
	'point_type' => 'Puan Türü',
	'point_type_page' => 'Sayfa',
	'point_type_activity' => 'Aktivite',
	'point_type_task' => 'Görev',
	'point_type_manual' => 'Manuel',
	'point_type_unknown' => 'Bilinmeyen',
	'start_page_hint' => 'İsteğe bağlı: Başlangıç sayfa numarası',
	'end_page_hint' => 'İsteğe bağlı: Bitiş sayfa numarası',
	'pages_read_hint' => 'Bu oturumda okunan sayfa sayısı',
	'reading_duration_hint' => 'İsteğe bağlı: Okuma için harcanan süre (dakika)',
	'add_to_class_books' => 'Sınıf Kitaplığına Ekle',
	'confirm_add_to_class_books' => 'Bu kitabı sınıf kitaplığına eklemek istediğinizden emin misiniz?',
	'book_added_to_class_books_successfully' => 'Kitap sınıf kitaplığına başarıyla eklendi',
	'book_added_to_class_books_error' => 'Kitap sınıf kitaplığına eklenirken hata oluştu',
	'book_not_found' => 'Kitap bulunamadı',
	'remove_from_class_books' => 'Sınıf Kitaplığından Kaldır',
	'confirm_remove_from_class_books' => 'Bu kitabı sınıf kitaplığından kaldırmak istediğinizden emin misiniz?',
	'book_removed_from_class_books_successfully' => 'Kitap sınıf kitaplığından başarıyla kaldırıldı',
	'book_removed_from_class_books_error' => 'Kitap sınıf kitaplığından kaldırılırken hata oluştu',
	'book_not_in_class_books' => 'Kitap sınıf kitaplığında yok',

	// Görevler
	'task_management' => 'Görev Yönetimi',
	'tasks' => 'Görevler',
	'task' => 'Görev',
	'task_name' => 'Görev Adı',
	'task_types' => 'Görev Türleri',
	'task_type' => 'Görev Türü',
	'task_cycles' => 'Görev Periyotları',
	'task_cycle' => 'Görev Periyodu',
	'task_value' => 'Görev Değeri',
	'task_value_with_unit' => 'Görev Değeri',
	'task_details' => 'Görev Detayları',
	'task_configuration' => 'Görev Yapılandırması',
	'task_books' => 'Görev Kitapları',
	'task_categories' => 'Görev Kategorileri',
	'configuration_summary' => 'Yapılandırma Özeti',
	'task_type_hint' => 'Gerçekleştirilecek görev türünü seçin',
	'task_cycle_hint' => 'Bu görevin periyodik tekrarını  seçin',
	'task_value_hint' => 'Nicel görevler için hedef değeri girin',
	'task_activity_hint' => 'Aktivite tabanlı görevler için belirli aktiviteyi seçin',
	'task_books_hint' => 'Bu görev için tamamlanması gereken kitapları seçin',
	'task_categories_hint' => 'Kitapların ait olması gereken kategorileri seçin',
	'task_description_hint' => 'Bu görevin ne içerdiğinin isteğe bağlı detaylı açıklaması',
	'assign_to_class' => 'Sınıfa Ata',
	'assign_to_team' => 'Takıma Ata',
	'assign_to_students' => 'Öğrencilere Ata',
	'task_assigned_to_class_students' => 'Görev sınıftaki :count öğrenciye atandı',
	'task_assigned_to_team_students' => 'Görev takımdaki :count öğrenciye atandı',
	'task_assigned_to_students' => 'Görev :count öğrenciye atandı',
	'no_students_in_class' => 'Seçilen sınıfta öğrenci bulunamadı',
	'no_students_in_team' => 'Seçilen takımda öğrenci bulunamadı',
	'no_students_selected' => 'Hiç öğrenci seçilmedi',
	'task_type_number_hint' => 'Bu görev türü için benzersiz numara tanımlayıcısı',
	'task_type_name_hint' => 'Görev türünün adı',
	'task_cycle_number_hint' => 'Bu görev periyodu için benzersiz numara tanımlayıcısı',
	'task_cycle_name_hint' => 'Görev periyodu adı',
	'standalone_task' => 'Bağımsız Görev',
	'standalone_tasks' => 'Bağımsız Görevler',
	'user_tasks' => 'Kullanıcı Görevleri',
	'complete_date' => 'Tamamlanma Tarihi',
	'due_date_in_past' => 'Bitiş tarihi geçmişte olamaz',
	'task_assignment' => 'Görev Ataması',
	'completion_status' => 'Tamamlanma Durumu',
	'assignee' => 'Atanan',
	'days_since_assignment' => 'Atamadan Bu Yana Geçen Gün',
	'estimated_completion' => 'Tahmini Tamamlanma',
	'motivational_message' => 'Motivasyon Mesajı',
	'duration_days' => 'Süre (Gün)',
	'current_value' => 'Mevcut Değer',
	'target_value' => 'Hedef Değer',
	'days_remaining' => 'Kalan Gün',
	'completion_time_days' => 'Tamamlanma Süresi (Gün)',
	'individual_assignment_hint' => 'Bireysel atama için kullanıcı seçin',
	'team_assignment_hint' => 'Takım ataması için takım seçin',
	'individual_task_assignment_hint' => 'Bireysel görev ataması için kullanıcı seçin',
	'team_task_assignment_hint' => 'Takım görev ataması için takım seçin',
	'enter_assignment_comment' => 'Atama yorumunu girin',
	'cannot_assign_to_both_user_and_team' => 'Hem kullanıcıya hem takıma atanamaz',
	'must_assign_to_user_or_team' => 'Kullanıcıya veya takıma atanmalıdır',
	'task_already_assigned_to_assignee' => ':task görevi zaten :assignee kişisine atanmış',
	'users' => 'Kullanıcılar',
	'teams' => 'Takımlar',
	'active_tasks' => 'Aktif Görevler',
	'no_active_tasks' => 'Aktif Görev Yok',

	// Challenge Management
	'challenges' => 'Yarışmalar',
	'challenge' => 'Yarışma',
	'challenge_name' => 'Yarışma Adı',
	'challenge_description' => 'Yarışma Açıklaması',
	'challenge_image' => 'Yarışma Görseli',
	'challenge_image_hint' => 'Yarışma için banner veya afiş görseli yükleyin',
	'enter_challenge_name' => 'Yarışma adını girin',
	'enter_challenge_description' => 'Yarışma açıklamasını girin',
	'enter_prize_description' => 'Ödül açıklamasını girin',
	'prize' => 'Ödül',
	'duration' => 'Süre (gün)',
	'participants' => 'Katılımcılar',
	'participating_schools' => 'Katılımcı Okullar',
	'participating_classes' => 'Katılımcı Sınıflar',
	'participating_teams' => 'Katılımcı Takımlar',
	'select_schools_for_challenge' => 'Bu yarışmaya katılacak okulları seçin',
	'select_classes_for_challenge' => 'Bu yarışmaya katılacak sınıfları seçin',
	'select_teams_for_challenge' => 'Bu yarışmaya katılacak takımları seçin',
	'participants_count' => 'Katılımcılar',
	'completion_count' => 'Tamamlanan',
	'upcoming' => 'Yaklaşan',
	'completed' => 'Tamamlandı',
	'pending' => 'Beklemede',
	'individual' => 'Bireysel',
	'team' => 'Takım',

	// Challenge Tasks
	'challenge_tasks' => 'Yarışma Görevleri',
	'challenge_task' => 'Yarışma Görevi',
	'user_challenge_tasks' => 'Kullanıcı Yarışma Görevleri',
	'user_challenge_task' => 'Kullanıcı Yarışma Görevi',
	'assignment_type' => 'Atama Türü',
	'days_since_assignment' => 'Atamadan Bu Yana Geçen Gün',
	'progress_percentage' => 'İlerleme %',
	'leave_empty_for_individual_assignment' => 'Bireysel atama için boş bırakın',
	'assignment_date' => 'Atama Tarihi',
	'completion_date' => 'Tamamlanma Tarihi',

	// Challenge Management Menu
	'challenge_management' => 'Yarışma Yönetimi',

	// Communication
	'communication' => 'İletişim',
	'messages' => 'Mesajlar',
	'message' => 'Mesaj',
	'message_recipients' => 'Mesaj Alıcıları',
	'message_recipient' => 'Mesaj Alıcısı',
	'title' => 'Başlık',
	'message_date' => 'Mesaj Tarihi',
	'message_preview' => 'Mesaj Önizleme',
	'default' => 'Varsayılan Mesaj',
	'default_message_hint' => 'Varsayılan mesajlar tüm yeni kullanıcılara otomatik olarak gönderilir',
	'total_recipients' => 'Toplam Alıcı',
	'read_count' => 'Okundu',
	'unread_count' => 'Okunmadı',
	'read' => 'Okundu',
	'unread' => 'Okunmadı',
	'sent_date' => 'Gönderim Tarihi',
	'read_date' => 'Okunma Tarihi',
	'message_info' => 'Mesaj Bilgileri',
	'recipients' => 'Alıcılar',
	'recipient_info' => 'Alıcı Bilgileri',
	'enter_message_title' => 'Mesaj başlığını girin',
	'enter_message_content' => 'Mesaj içeriğini girin',
	'select_message_recipients' => 'Bu mesajı alacak kullanıcıları seçin',
	'all_messages' => 'Tüm Mesajlar',
	'default_messages' => 'Varsayılan Mesajlar',
	'custom_messages' => 'Özel Mesajlar',
	'all' => 'Tümü',

	// Challenge Actions
	'confirm_toggle_challenge_status' => 'Yarışma durumunu değiştirmek istediğinizden emin misiniz?',
	'challenge_activated_successfully' => 'Yarışma başarıyla etkinleştirildi ve atamalar oluşturuldu.',
	'challenge_deactivated_successfully' => 'Yarışma başarıyla devre dışı bırakıldı.',
	'challenge_not_found' => 'Yarışma bulunamadı.',

	// Reward System
	'rewards' => 'Ödüller',
	'reward' => 'Ödül',
	'reward_name' => 'Ödül Adı',
	'reward_type' => 'Ödül Türü',
	'reward_type_display' => 'Tür',
	'reward_description' => 'Açıklama',
	'reward_image' => 'Resim',
	'reward_tasks' => 'Ödül Görevleri',
	'user_rewards' => 'Kullanıcı Ödülleri',
	'team_rewards' => 'Takım Ödülleri',
	'badges' => 'Rozetler',
	'badge' => 'Rozet',
	'gifts' => 'Hediyeler',
	'gift' => 'Hediye',
	'trophies' => 'Kupalar',
	'trophy' => 'Kupa',
	'cards' => 'Kartlar',
	'card' => 'Kart',
	'items' => 'Eşyalar',
	'item' => 'Eşya',
	'unknown' => 'Bilinmeyen',
	'enter_reward_name' => 'Ödül adını girin',
	'enter_reward_description' => 'Ödül açıklamasını girin',
	'reward_image_hint' => 'Bu ödül için bir resim yükleyin (isteğe bağlı)',
	'select_tasks_for_reward' => 'Tamamlandığında bu ödülü verecek görevleri seçin',
	'associated_tasks' => 'İlişkili Görevler',
	'repeatable' => 'Tekrarlanabilir',
	'repeatable_reward_hint' => 'Bu ödülün aynı kullanıcı tarafından birden fazla kez kazanılmasına izin ver',
	'tasks_count' => 'Görev Sayısı',
	'users_earned_count' => 'Kazanan Kullanıcılar',
	'teams_earned_count' => 'Kazanan Takımlar',
	'basic_info' => 'Temel Bilgiler',
	'awarded_date' => 'Verilme Tarihi',
	'activity_trigger' => 'Aktivite Sebebiyle',
	'leave_empty_for_automatic_award' => 'Otomatik sistem ödülü için boş bırakın',
	'optional_reward_for_task_completion' => 'Bu görev tamamlandığında verilecek isteğe bağlı ödül',
	'awarding_type' => 'Verilme Türü',
	'my_rewards' => 'Oluşturduğum Ödüller',
	'system_rewards' => 'Sistem Ödülleri',
	'award_to_class' => 'Sınıfa Ver',
	'confirm_award_to_class' => 'Bu ödülü sınıfınızdaki tüm öğrencilerine vermek istediğinizden emin misiniz?',
	'reward_awarded_to_class' => 'Ödül başarıyla sınıfa verildi',
	'award_to_team' => 'Takıma Ver',
	'confirm_award_to_team' => 'Bu ödülü seçilen takıma vermek istediğinizden emin misiniz?',
	'reward_awarded_to_team' => 'Ödül başarıyla takıma verildi',
	'award_to_students' => 'Öğrencilere Ver',
	'confirm_award_to_students' => 'Bu ödülü seçilen öğrencilere vermek istediğinizden emin misiniz?',
	'reward_awarded_to_students' => 'Ödül başarıyla öğrencilere verildi',
	'reward_already_awarded' => 'Bu ödül zaten verildi',
	'reward_not_found' => 'Ödül bulunamadı',
	'reward_not_active' => 'Ödül aktif değil',
	

	// Görev Türü Açıklamaları
	'task_type_read_pages_desc' => 'Belirli sayıda sayfayı okumayı tamamla',
	'task_type_read_books_desc' => 'Belirli sayıda kitabı okumayı tamamla',
	'task_type_read_minutes_desc' => 'Belirli süre okuma yap',
	'task_type_read_days_desc' => 'Belirli sayıda gün okuma yap',
	'task_type_read_streak_desc' => 'Ardışık günlerde okuma serisini sürdür',
	'task_type_earn_reading_points_desc' => 'Belirli sayıda okuma puanı kazan',
	'task_type_earn_activity_points_desc' => 'Belirli sayıda aktivite puanı kazan',
	'task_type_complete_book_activity_desc' => 'Belirli bir kitap aktivitesini tamamla',
	'task_type_complete_book_list_desc' => 'Belirtilen listedeki tüm kitapları okumayı tamamla',

	// Görev Periyodu Açıklamaları
	'task_cycle_total_desc' => 'Tüm dönem boyunca kümülatif ilerleme',
	'task_cycle_daily_desc' => 'Görev her gün tamamlanmalı',
	'task_cycle_weekly_desc' => 'Görev her hafta tamamlanmalı',
	'task_cycle_monthly_desc' => 'Görev her ay tamamlanmalı',

	// Görev Birimleri ve Türleri
	'pages' => 'sayfa',
	'pages_localizable' => 'sayfa',
	'minutes' => 'dakika',
	'minutes_localizable' => 'dakika',
	'days' => 'gün',
	'days_localizable' => 'gün',
	'activities' => 'aktivite',
	'activities_localizable' => 'aktivite',
	'points' => 'puan',
	'points_localizable' => 'puan',
	'total' => 'toplam',
	'per_day' => 'günlük',
	'per_week' => 'haftalık',
	'per_month' => 'aylık',
	'quantitative' => 'Nicel',
	'qualitative' => 'Nitel',
	'time_based' => 'Zaman tabanlı',
	'cumulative' => 'Kümülatif',
	'number' => 'Numara',
	'unit' => 'Birim',
	'type' => 'Tür',
	'description' => 'Açıklama',
	'display_name' => 'Görünen Ad',
	'summary' => 'Özet',
	'activity' => 'Aktivite',
	'activity_categories' => 'Aktivite Kategorileri',
	'activities' => 'Aktiviteler',
	'user_activities' => 'Kullanıcı Aktiviteleri',
	'user_activity_reviews' => 'Aktivite İncelemeleri',
	'activity_types' => 'Aktivite Türleri',
	'activity_type' => 'Aktivite Türü',
	'activity_type_writing' => 'Yazma',
	'activity_type_rating' => 'Değerlendirme',
	'activity_type_media' => 'Medya',
	'activity_type_physical' => 'Fiziksel',
	'activity_type_game' => 'Oyun',
	'activity_type_quiz' => 'Quiz',
	'activity_type_vocabulary_test' => 'Kelime Testi',
	'activity_type_unknown' => 'Bilinmeyen',
	'overdue' => 'Gecikmiş',
	'class_activity_alert' => 'Sınıfınızda geçerli olmasını istediğiniz aktiviteleri var olan aktivite türlerinden seçebilir, sınıfınıza özgü değişiklikler yapabilirsiniz.',

	// Test Aktivite Alanları
	'question_count' => 'Soru Sayısı',
	'question_count_hint' => 'Quiz/kelime testleri için soru sayısı (1-10)',
	'choices_count' => 'Seçenek Sayısı',
	'choices_count_hint' => 'Her soru için cevap seçeneği sayısı (1-6)',
	'min_grade' => 'Minimum Not',
	'min_grade_hint' => '100 üzerinden minimum geçme notu',
	'required' => 'Zorunlu',
	'required_activity_hint' => 'Bu aktivitenin kitap tamamlama için zorunlu olup olmadığı',
	'allowed_tries' => 'İzin Verilen Deneme',
	'allowed_tries_hint' => 'İzin verilen tekrar deneme sayısı',

	// Test Aktivite Doğrulama
	'question_count_range_error' => 'Soru sayısı 1 ile 10 arasında olmalıdır',
	'choices_count_range_error' => 'Seçenek sayısı 1 ile 6 arasında olmalıdır',
	'min_grade_range_error' => 'Minimum not 0 ile 100 arasında olmalıdır',
	'allowed_tries_min_error' => 'İzin verilen deneme en az 1 olmalıdır',

	// Test Aktivite Sonuçları
	'test_score' => 'Test Puanı',
	'test_passed' => 'Test Geçildi',
	'attempt_count' => 'Deneme Sayısı',
	'yes' => 'Evet',
	'no' => 'Hayır',
	'min_word_count' => 'Min Kelime Sayısı',
	'min_rating' => 'Min Değerlendirme',
	'max_rating' => 'Max Değerlendirme',
	'media_url' => 'Medya URL',
	'need_approval' => 'Onay Gerekli',
	'need_approval_hint' => 'Gönderilerin onay gerektirip gerektirmediğini belirler',
	'activity_date' => 'Aktivite Tarihi',
	'content' => 'İçerik',
	'rating' => 'Değerlendirme',
	'status_pending' => 'Beklemede',
	'status_approved' => 'Onaylandı',
	'status_rejected' => 'Reddedildi',
	'status_completed' => 'Tamamlandı',
	'status_unknown' => 'Bilinmeyen',
	'review_date' => 'İnceleme Tarihi',
	'reviewer' => 'İnceleyici',
	'feedback' => 'Geri Bildirim',
	'review_status_waiting' => 'Bekliyor',
	'review_status_approved' => 'Onaylandı',
	'review_status_rejected' => 'Reddedildi',
	'review_status_unknown' => 'Bilinmeyen',
	'activity_count' => 'Aktivite Sayısı',
	'active_activity_count' => 'Aktif Aktivite Sayısı',
	'student' => 'Öğrenci',
	'user_activity' => 'Kullanıcı Aktivitesi',
	'activity_content' => 'Aktivite İçeriği',
	'activity_rating' => 'Aktivite Değerlendirmesi',
	'activity_media_url' => 'Aktivite Medya URL',
	'activity_category_hint' => 'Aktivitenin kategorisi',
	'activity_type_hint' => 'Aktivitenin türü',	
	'media_type_image' => 'Resim',
	'media_type_audio' => 'Ses',
	'media_type_hint' => 'Yüklenecek aktiviteler için medya türü',
	'activity_description_hint' => 'Öğrencilerin bu aktivite için ne yapması gerektiğini açıklayın',
	'min_word_count_hint' => 'Yazma aktiviteleri için gereken minimum kelime sayısı',
	'min_rating_hint' => 'Değerlendirme aktiviteleri için minimum değer',
	'max_rating_hint' => 'Değerlendirme aktiviteleri için maksimum değer',
	'media_url_hint' => 'Oyun veya medya içeriği için URL',
	'activity_content_hint' => 'Yazma aktiviteleri için yazılı içerik',
	'activity_rating_hint' => 'Değerlendirme değeri (1-10)',
	'activity_media_url_hint' => 'Gönderilen medya içeriği için URL',
	'review_feedback_hint' => 'Öğrenciye aktivite gönderimi hakkında geri bildirim',
	'user_books' => 'Kullanıcı Kitapları',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'reading_status' => 'Okuma Durumu',
	'reading_status_in_progress' => 'Devam Ediyor',
	'reading_status_completed' => 'Tamamlandı',
	'reading_duration' => 'Okuma Süresi',
	'progress_percentage' => 'İlerleme',
	'total_pages_read' => 'Toplam Okunan Sayfa',
	'total_reading_time' => 'Toplam Okuma Süresi (dakika)',
	'has_reading_logs' => 'Okuma Kayıtları Var',
	'start_date_hint' => 'Öğrencinin bu kitabı okumaya başladığı tarih',
	'end_date_hint' => 'Hala okuyorsa boş bırakın, tamamlandığında tarihi belirleyin',
	'same_day' => 'Aynı gün',
	'one_day' => '1 gün',
	'days_count' => '{count} gün',
	'session_info' => 'Oturum Bilgisi',
	'reading_history' => 'Okuma Geçmişi',
	'cannot_start_new_session_active_exists' => 'Bu kitap için aktif bir okuma oturumu varken yeni bir oturum başlatılamaz. Lütfen önce mevcut oturumu tamamlayın.',
	'user_avatars' => 'Kullanıcı Avatarları',
	'current_avatar_image' => 'Mevcut Avatar Resmi',
	'user_activity_points' => 'Kullanıcı Aktivite Puanı',
	'avatar_required_points' => 'Avatar Gereken Puan',
	'required_points' => 'Gerekli Puan',
	'selected_at' => 'Seçilme Tarihi',
	'selection_age' => 'Seçim Yaşı',
	'selected_at_hint' => 'Avatarın seçildiği tarih ve saat',
	'selected_today' => 'Bugün seçildi',
	'selected_yesterday' => 'Dün seçildi',
	'selected_days_ago' => '{days} gün önce seçildi',
	'insufficient_activity_points_for_avatar' => 'Kullanıcının bu avatar için yeterli aktivite puanı yok. Gerekli: {required}, Mevcut: {current}',
	'manual' => 'Manuel',
	'automatic' => 'Otomatik',
	'rule_type' => 'Kural Türü',
	'rule_value' => 'Kural Değeri',
	'rule_value_hint' => 'Bu kuralı sağlamak için gereken minimum değer',
	'media_url_invalid' => 'Medya URL geçersiz',
	'media_url_required' => 'Medya URL gerekli',
	'my_activities' => 'Benim Aktivitelerim',
	'rating_required' => 'Değerlendirme gerekli',
	'book_activities' => 'Kitap Aktiviteleri',

	'award_type' => 'Ödül Türü',
	'awarder' => 'Veren',
	'awarded_at' => 'Verilme Tarihi',
	'awarded_by' => 'Veren',
	'award_age' => 'Ödül Yaşı',
	'awarded_at_hint' => 'Ödülün verildiği tarih ve saat',
	'awarded_by_hint' => 'Otomatik ödüller için boş bırakın',
	'awarded_today' => 'Bugün verildi',
	'awarded_yesterday' => 'Dün verildi',
	'awarded_days_ago' => '{days} gün önce verildi',

	'system' => 'Sistem',
	'points_rule' => 'puan',
	'minutes_rule' => 'dakika',
	'days_rule' => 'gün',
	'consecutive_days_rule' => 'ardışık gün',
	'books_rule' => 'kitap',
	'rank_rule' => 'sıralama',
	'percentage_rule' => 'yüzde',
	'rules' => 'kural',
	'users_rule' => 'kullanıcı',
	'no_rules' => 'Kural tanımlanmamış',
	'unit' => 'Birim',

	'number' => 'Numara',
	'rule_type_number_hint' => 'Bu kural türü için benzersiz numara tanımlayıcısı',
	'rule_type_name_hint' => 'Bu kural türü için açıklayıcı isim',
	'trigger_source' => 'Ödül Kaynağı',
	'reading_log' => 'Okuma Kaydı',
	'reading_log_trigger' => 'Okuma Kaydı Sebebiyle',
	'manual_award' => 'Manuel Ödül',
	'automatic_award' => 'Otomatik Ödül',
	'no_rewards' => 'Ödül yok',
	'teams' => 'Takımlar',

	'user_teams' => 'Kullanıcı Takımları',
	'team_members' => 'Takım Üyeleri',
	'team' => 'Takım',
	'leader' => 'Lider',
	'members' => 'Üyeler',
	'total_points' => 'Toplam Puan',
	'logo' => 'Logo',
	'team_logo_hint' => 'Bu takım için bir logo yükleyin (isteğe bağlı)',
	'team_leader_hint' => 'Takım üyelerinden bir takım lideri seçin',
	'team_members_hint' => 'Bu takıma kullanıcılar ekleyin',
	'no_leader' => 'Lider Yok',
	'team_leader' => 'Takım Lideri',
	'team_member' => 'Takım Üyesi',
	'membership_role' => 'Üyelik Rolü',
	'team_status' => 'Takım Durumu',
	'inactive' => 'Pasif',
	'leader_must_be_team_member' => 'Lider {user}, {team} takımının üyesi olmalıdır',
	'team_not_found' => 'Takım bulunamadı',
	'user_already_in_team' => 'Kullanıcı {user} zaten {team} takımının üyesi',

	// Gamification
	'gamification' => 'Oyunlaştırma',
	'avatars' => 'Karakterler',
	'stories' => 'Hikayeler',
	'story_rules' => 'Hikaye Kuralları',
	'story_rule_details' => 'Hikaye Kural Detayları',
	'story_chapters' => 'Hikaye Bölümleri',
	'story_achievements' => 'Hikaye Başarıları',
	'story_books' => 'Hikaye Kitapları',

	// Assessment System
	'assessment_system' => 'Değerlendirme Sistemi',
	'book_questions' => 'Kitap Soruları',
	'book_words' => 'Kitap Kelimeleri',
	

	// Reading Log System
	'reading_log_system' => 'Okuma Günlüğü Sistemi',
	
	'story' => 'Hikaye',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'is_active' => 'Aktif',
	'status' => 'Durum',
	'duration' => 'Süre',
	'participating_schools' => 'Katılımcı Okullar',
	'participating_classes' => 'Katılımcı Sınıflar',
	'points' => 'Puan',

	// Task Fields
	'task_type' => 'Görev Türü',
	'page_start' => 'Başlangıç Sayfası',
	'page_end' => 'Bitiş Sayfası',
	'page_range' => 'Sayfa Aralığı',
	'days_remaining' => 'Kalan Günler',

	// Assessment Fields
	'question_text' => 'Soru Metni',
	'correct_answer' => 'Doğru Cevap',
	'incorrect_answer' => 'Yanlış Cevap',
	'question_image_url' => 'Soru Resim URL\'si',
	'question_audio_url' => 'Soru Ses URL\'si',
	'question_video_url' => 'Soru Video URL\'si',
	'page_reference' => 'Sayfa Referansı',
	'word' => 'Kelime',
	'definition' => 'Tanım',
	'synonym' => 'Eş Anlamlı',
	'antonym' => 'Zıt Anlamlı',
	'word_display' => 'Kelime Görünümü',
	'min_word_count' => 'Minimum Kelime Sayısı',
	'activity_content' => 'Aktivite İçeriği',
	'feedback' => 'Geri Bildirim',
	'media_type' => 'Medya Türü',
	'answer_options' => 'Cevap Seçenekleri',
	'media_content' => 'Medya İçeriği',

	// Reading Log Fields
	'start_page' => 'Başlangıç Sayfası',
	'end_page' => 'Bitiş Sayfası',
	'page_range' => 'Sayfa Aralığı',
	'pages_read' => 'Okunan Sayfa Sayısı',

	'active' => 'Aktif',
	'inactive' => 'Pasif',
	'upcoming' => 'Yaklaşan',
	'completed' => 'Tamamlanmış',

	// Point Sources
	'achievement' => 'Başarı',
	'task' => 'Görev',
	'quest' => 'Macera',
	'reading' => 'Okuma',

	// Task Types
	'reading_log' => 'Okuma Günlüğü',
	'activity' => 'Aktivite',
	'question' => 'Soru',

	// Task Status
	'pending' => 'Beklemede',
	'upcoming' => 'Yaklaşan',

	// Recurrence Patterns

	// Assignment Types
	'individual' => 'Bireysel',
	'team' => 'Takım',

	// Activity Categories

	// Quiz Types
	'completion' => 'Kitap Tamamlama Sınavı',

	'difficulty_level' => 'Zorluk Düzeyi',

	// Common Actions
	'select_school' => 'Okul Seç',
	'enter_name' => 'Ad Girin',
	'enter_name_surname' => 'Ad & Soyad Girin',
	'enter_description' => 'Açıklama Girin',
	'select_class' => 'Sınıf Seç',
	'select_book' => 'Kitap Seç',
	'select_team' => 'Takım Seç',
	'select_book_type' => 'Kitap Türü Seç',
	'view' => 'Görüntüle',

	// Additional common fields
	'enter_user_title' => 'Ünvan Girin',
	'enter_email' => 'E-posta Girin',
	'select_user' => 'Kullanıcı Seç',
	'main_information' => 'Ana Bilgiler',
	'user' => 'Kullanıcı',
	'role' => 'Rol',
	'email' => 'E-posta',
	'username' => 'Kullanıcı Adı',
	'enter_username' => 'Kullanıcı Adını Girin',
	'unique_username_hint' => 'Kullanıcı adı benzersiz olmalıdır',
	'check_username' => 'Kullanıcı Adını Kontrol Et',
	'username_exists' => 'Kullanıcı adı zaten mevcut',
	'username_available' => 'Kullanıcı adı kullanılabilir',
	'point' => 'Puan',
	'date_range' => 'Tarih Aralığı',

	// Tags
	
	// Common Fields
	'name' => 'Ad',
	'name_surname' => 'Ad & Soyad',
	'user_title' => 'Unvan',
	'email' => 'E-posta',
	'password' => 'Şifre',
	'password_repeat' => 'Şifre Tekrar',
	'description' => 'Açıklama',
	'active' => 'Aktif',
	'default' => 'Varsayılan',
	'available' => 'Müsait',
	'related_information' => 'İlgili Bilgiler',
	'default_school_hint' => 'Bunu kullanıcının varsayılan kurumu olarak işaretleyin. Kullanıcı başına yalnızca bir kurum varsayılan olabilir.',
	'default_class_hint' => 'Bunu kullanıcının varsayılan sınıfı olarak işaretleyin. Kullanıcı başına yalnızca bir sınıf varsayılan olabilir.',
	'cannot_deactivate_default_school' => 'Varsayılan kurum pasif hale getirilemez. Lütfen önce başka bir kurumu varsayılan olarak ayarlayın.',
	'cannot_deactivate_default_class' => 'Varsayılan sınıf pasif hale getirilemez. Lütfen önce başka bir sınıfı varsayılan olarak ayarlayın.',
	'cannot_delete_default_school' => 'Varsayılan kurum silinemez. Lütfen önce başka bir kurumu varsayılan olarak ayarlayın.',
	'cannot_delete_default_class' => 'Varsayılan sınıf silinemez. Lütfen önce başka bir sınıfı varsayılan olarak ayarlayın.',
	'default_class_not_found' => 'Varsayılan sınıf bulunamadı',
	'invalid_request' => 'Geçersiz istek',

	// Student Management
	'students' => 'Öğrenciler',
	'student_management_subtitle' => 'Kapsamlı öğrenci yönetimi ve ilerleme takibi',
	'student_profile' => 'Öğrenci Profili',
	'student_metrics' => 'Öğrenci Metrikleri',
	'student_school_assignment_hint' => 'Öğrenciyi bir kuruma atayın. Belirtilmezse varsayılan kurumunuz kullanılır.',
	'student_class_assignment_hint' => 'Öğrenciyi seçilen kurum içindeki bir sınıfa atayın.',
	'total_students' => 'Toplam Öğrenci',
	'active_readers_today' => 'Bugün Aktif Okuyucular',
	'books_completed_this_month' => 'Bu Ay Tamamlanan Kitaplar',
	'students_by_reading_activity' => 'Okuma Aktivitesine Göre Öğrenciler',
	'active_today' => 'Bugün Aktif',
	'active_this_week' => 'Bu Hafta Aktif',
	'inactive' => 'Pasif',
	'no_school' => 'Kurum Yok',
	'no_class' => 'Sınıf Yok',
	'books_reading' => 'Şu Anda Okuduğu Kitaplar',
	'books_completed' => 'Tamamlanan Kitaplar',
	'reading_points' => 'Okuma Puanları',
	'reading_minutes' => 'Okuma Dakikaları',
	'rewards_earned' => 'Kazanılan Ödüller',
	'activity_points' => 'Aktivite Puanları',
	'reading_streak' => 'Okuma Serisi',
	'days' => 'gün',
	'reading_details' => 'Okuma Detayları',
	'activity_details' => 'Aktivite Detayları',
	'rewards_details' => 'Ödül Detayları',
	'tasks_completed' => 'Tamamlanan Görevler',
	'assigned_date' => 'Atama Tarihi',
	'book_name' => 'Kitap Adı',
	'date' => 'Tarih',
	'pages_read' => 'Okunan Sayfalar',
	'reading_duration' => 'Okuma Süresi',
	'progress' => 'İlerleme',
	'activity_name' => 'Aktivite Adı',
	'activity_date' => 'Aktivite Tarihi',
	'status' => 'Durum',
	'rating' => 'Değerlendirme',
	'points_earned' => 'Kazanılan Puan',
	'feedback' => 'Geri Bildirim',
	'reward_type' => 'Ödül Türü',
	'reward_name' => 'Ödül Adı',
	'awarded_date' => 'Ödül Tarihi',
	'image' => 'Resim',
	'pending_activities' => 'Bekleyen Aktiviteler',
	'status_pending' => 'Bekliyor',
	'status_approved' => 'Onaylandı',
	'status_rejected' => 'Reddedildi',
	'status_completed' => 'Tamamlandı',
	'status_failed' => 'Başarısız',
	'status_unknown' => 'Bilinmeyen',
	'approve' => 'Onayla',
	'reject' => 'Reddet',
	'student_not_found' => 'Öğrenci bulunamadı',
	'review_approved_successfully' => 'İnceleme onaylandı',
	'review_rejected_successfully' => 'İnceleme reddedildi',	
	'review_not_found' => 'İnceleme bulunamadı',

	// Class Activities
	'class_activities' => 'Sınıf Aktiviteleri',
	'class_activity' => 'Sınıf Aktivitesi',
	'test_settings' => 'Test Ayarları',
	'writing_settings' => 'Yazma Ayarları',
	'general_settings' => 'Genel Ayarlar',
	'question_count_hint' => 'Quiz/kelime testleri için soru sayısını geçersiz kıl (aktivite varsayılanını kullanmak için boş bırakın)',
	'min_grade_hint' => 'Testler için minimum geçme notunu geçersiz kıl (aktivite varsayılanını kullanmak için boş bırakın)',
	'allowed_tries_hint' => 'Test aktiviteleri için maksimum deneme sayısını geçersiz kıl (aktivite varsayılanını kullanmak için boş bırakın)',
	'min_word_count_hint' => 'Yazma aktiviteleri için minimum kelime sayısını geçersiz kıl (aktivite varsayılanını kullanmak için boş bırakın)',
	'activity_points_hint' => 'Bu aktivite için verilen puanları geçersiz kıl (aktivite varsayılanını kullanmak için boş bırakın)',
	'required_activity_hint' => 'Bu aktivitenin kitap tamamlama için gerekli olup olmadığını geçersiz kıl',
	'active_activity_hint' => 'Bu aktivitenin bu sınıf için etkin olup olmadığını geçersiz kıl',
	'basic_information' => 'Temel Bilgiler',
	'school_assignments' => 'Kurum Atamaları',
	'enter_name' => 'Ad girin',
	'enter_username' => 'Kullanıcı adı girin',
	'enter_email' => 'E-posta girin',
	'enter_user_title' => 'Kullanıcı unvanı girin',
	'confirm_approve_review' => 'Bu incelemeyi onaylamak istediğinizden emin misiniz?',
	'confirm_reject_review' => 'Bu incelemeyi reddetmek istediğinizden emin misiniz?',
	'activity_not_found' => 'Aktivite bulunamadı',
	'content_required_for_writing' => 'Yazma aktiviteleri için içerik gerekli',

	'type' => 'Tip',
	'role' => 'Rol',
	'user' => 'Kullanıcı',
	'school' => 'Kurum',
	'class' => 'Sınıf',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'status' => 'Durum',
	'thumbnail' => 'Küçük Resim',
	'guard_name' => 'Laravel Guard Adı',
	
	// Book Fields
	'isbn' => 'Barkod',
	'publisher' => 'Yayınevi',
	'page_count' => 'Sayfa Sayısı',
	'year_of_publish' => 'Yayın Yılı',
	'author' => 'Yazar',
	'category' => 'Kategori',
	'book' => 'Kitap',	

	// Gamification Fields
	'story' => 'Hikaye',
	'cover_image' => 'Kapak Resmi',
	'rule_type' => 'Kural Tipi',
	'no' => 'Hayır',
	'assignment_info' => 'Atama Bilgileri',
	'start_date' => 'Başlangıç Tarihi',
	'end_date' => 'Bitiş Tarihi',
	'status' => 'Durum',
	'completed' => 'Tamamlandı',
	'duration_days' => 'Süre',
	'days' => 'gün',
	'base_image' => 'Temel Resim',
	'happy_image' => 'Mutlu Resim',
	'sad_image' => 'Üzgün Resim',
	'sleepy_image' => 'Uykulu Resim',
	'avatar' => 'Karakter',
	'image' => 'Resim',
	'achievement' => 'Başarı',
	'rule' => 'Kural',
	
	// Counts
	'books_count' => 'Kitap Sayısı',
	'student_count' => 'Öğrenci Sayısı',
	'duration_days' => 'Süre (Gün)',
	
	// Relationships
	'school_assignments' => 'Kullanıcı Kurum/Rolleri',
	'class_assignments' => 'Kullanıcı Sınıfları',
	'assignment_info' => 'Atama Bilgileri',
	'summary' => 'Özet',
	
	// Info Sections
	'main_information' => 'Ana Bilgiler',
	'assignments' => 'Atamalar',
	
	// Placeholders
	'enter_name' => 'Ad girin',
	'enter_email' => 'E-posta girin',
	'enter_description' => 'Açıklama girin',
	'select_user' => 'Kullanıcı seçin',
	'select_school' => 'Kurum seçin',
	'select_school_type' => 'Kurum türü seçin',
	'select_class' => 'Sınıf seçin',
	'select_publisher' => 'Yayınevi seçin',
	'select_book' => 'Kitap seçin',
	'select_class_level' => 'Sınıf seviyesi seçin',
	'enter_isbn' => 'Kitap barkodunu girin',
	'enter_page_count' => 'Sayfa sayısı girin',
	'enter_year' => 'Yıl girin',

	// Task Placeholders
	'enter_page_start' => 'Başlangıç sayfası girin',
	'enter_page_end' => 'Bitiş sayfası girin',

	// Assessment Placeholders
	'enter_question_text' => 'Soru metni girin',
	'enter_correct_answer' => 'Doğru cevap girin',
	'enter_incorrect_answer' => 'Yanlış cevap girin',
	'enter_image_url' => 'Resim URL\'si girin',
	'enter_audio_url' => 'Ses URL\'si girin',
	'enter_video_url' => 'Video URL\'si girin',
	'enter_word' => 'Kelime girin',
	'enter_definition' => 'Tanım girin',
	'enter_synonym' => 'Eş anlamlı girin',
	'enter_antonym' => 'Zıt anlamlı girin',
	'enter_page_reference' => 'Sayfa referansı girin',

	// Reading Log Placeholders

	// Reading Log Messages

	// Attributes
	'display_name' => 'Görünen Ad',
	'author_names' => 'Yazar Adları',
	'difficulty' => 'Zorluk',
	'total_pages' => 'Toplam Sayfa',
	'average_pages' => 'Ortalama Sayfa',
	'books_list' => 'Kitap Listesi',
	
	// Privacy Agreement
	'privacy_agreement_title' => 'Gizlilik Politikası Sözleşmesi',
	'privacy_policy_title' => 'Gizlilik Politikası',
	'privacy_agreement_consent' => 'Gizlilik politikasını ve kullanım şartlarını kabul ediyorum',
	'accept_and_continue' => 'Kabul Et ve Devam Et',
	'privacy_consent_required' => 'Devam etmek için gizlilik politikasını kabul etmelisiniz.',
	'privacy_agreement_accepted' => 'Gizlilik politikası başarıyla kabul edildi.',
	'privacy_agreement_error' => 'Onayınız işlenirken bir hata oluştu. Lütfen tekrar deneyin.',
	'authentication_required' => 'Kimlik doğrulama gerekli.',
	'last_updated' => 'Son Güncelleme',
	'version' => 'Versiyon',

	// Privacy Policy Content
	'privacy_policy_intro' => 'Bu gizlilik politikası, okuma takip uygulamamızı kullandığınızda kişisel bilgilerinizi nasıl topladığımızı, kullandığımızı ve koruduğumuzu açıklar.',
	'data_collection_title' => '1. Veri Toplama',
	'data_collection_content' => 'Adınız, e-posta adresiniz, okuma ilerlemeniz ve hizmetlerimizi sağlamak ve geliştirmek için kullanım verileriniz gibi doğrudan bize sağladığınız bilgileri topluyoruz.',
	'data_usage_title' => '2. Veri Kullanımı',
	'data_usage_content' => 'Bilgilerinizi eğitim hizmetleri sağlamak, okuma ilerlemesini takip etmek, raporlar oluşturmak ve uygulama işlevselliğimizi geliştirmek için kullanıyoruz.',
	'data_sharing_title' => '3. Veri Paylaşımı',
	'data_sharing_content' => 'Hizmetlerimizi sağlamak için gerekli olan durumlar veya yasal zorunluluklar dışında kişisel bilgilerinizi üçüncü taraflarla satmıyor veya paylaşmıyoruz.',
	'data_security_title' => '4. Veri Güvenliği',
	'data_security_content' => 'Kişisel bilgilerinizi yetkisiz erişim, değişiklik, ifşa veya imhaya karşı korumak için uygun güvenlik önlemleri uyguluyoruz.',
	'user_rights_title' => '5. Haklarınız',
	'user_rights_content' => 'Kişisel bilgilerinize erişme, güncelleme veya silme hakkınız vardır. Bu hakları kullanmak istiyorsanız bizimle iletişime geçin.',
	'contact_info_title' => '6. İletişim Bilgileri',
	'contact_info_content' => 'Bu gizlilik politikası hakkında sorularınız varsa, lütfen sistem yöneticiniz veya okul yönetimiyle iletişime geçin.',

	// User Agreements
	'user_agreements' => 'Kullanıcı Sözleşmeleri',
	'agreement_type' => 'Sözleşme Türü',
	'accepted_at' => 'Kabul Tarihi',
	'ip_address' => 'IP Adresi',
	'privacy_policy' => 'Gizlilik Politikası',
	'terms_of_service' => 'Hizmet Şartları',
	'cookie_policy' => 'Çerez Politikası',
	'enter_version' => 'Versiyon girin',
	'enter_ip_address' => 'IP adresi girin',

	// Actions
	'cancel' => 'İptal',
	'cancel' => 'İptal',
	
	// Messages
	
	// Validation
	
	// Role Levels
	'student' => 'Öğrenci',
	
	// School Types
	'school' => 'Okul',
	
	// Tag Types
	
	// Status Values
	'current' => 'Güncel',

	// Rule Types
	'time_based' => 'Zaman Bazlı',
	
	// Time
	'minutes' => 'dakika',
	'days' => 'gün',

  // Motivational Messages
    'goal_messages' => [
        'achieved' => 'Tebrikler! Bu hedefi başardınız!',
        'almost_there' => 'Neredeyse bitirdiniz! Harika iş çıkarıyorsunuz!',
        'great_progress' => 'Harika ilerleme! Mükemmel gidiyorsunuz!',
        'halfway_there' => 'Yarı yoldasınız! Devam edin!',
        'good_start' => 'İyi bir başlangıç! Doğru yoldasınız!',
        'just_started' => 'Yeni başladınız. Adım adım ilerleyin!',
    ],

    // Recommendations
    'goal_recommendations' => [
        'need_more_effort' => 'Yetişmek için okuma aktivitelerine daha fazla zaman ayırmayı düşünün.',
        'almost_there' => 'Çok yaklaştınız! Hedefinize ulaşmak için biraz daha çaba gösterin.',
        'deadline_approaching' => 'Yaklaşan son tarihleriniz var. Acil görevleri tamamlamaya odaklanın.',
        'keep_going' => 'Düzenli ilerleme kaydediyorsunuz. Böyle devam edin!',
    ],

    // Student Goals
    'save_and_continue' => 'Kaydet ve Devam Et',
    'complete_step_1_first' => 'Lütfen önce 1. adımı tamamlayın',
    'add_task' => 'Görev Ekle',
    'continue_to_assignment' => 'Atamaya Devam Et',
    'complete_previous_steps_first' => 'Lütfen önce önceki adımları tamamlayın',
    'student_team_assignment' => 'Öğrenci ve Takım Ataması',
    'assign_to_students' => 'Öğrencilere Ata',
    'assign_to_teams' => 'Takımlara Ata',
    'assignment_comment' => 'Atama Yorumu',
    'optional_comment' => 'Bu atama için isteğe bağlı yorum',
    'save_task' => 'Görevi Kaydet',
    'task_deleted_successfully' => 'Görev başarıyla silindi',
    'completed_assignments' => 'Tamamlanan Atamalar',
    'assigned_students' => 'Atanan Öğrenciler',
    'assigned_teams' => 'Atanan Takımlar',
    'completion_rate' => 'Tamamlanma Oranı',
'completion_percentage' => 'Tamamlanma %',
    'total_tasks' => 'Toplam Görevler',
    'progress_overview' => 'İlerleme Özeti',
    'tasks_completed' => 'Tamamlanan Görevler',
    'in_progress' => 'Devam Ediyor',
    'completed' => 'Tamamlandı',
    'individual_assignments' => 'Bireysel Görevler',
    'team_assignments' => 'Takım Görevleri',
	'class_task_assignments' => 'Sınıf Görevleri',
    'in_progress_assignments' => 'Devam Eden Görevler',
    'average_progress' => 'Ortalama İlerleme',
    'member_count' => 'Üye Sayısı',
    'team_leader' => 'Takım Lideri',
    'no_leader' => 'Lider Yok',
    'achieve_date' => 'Başarı Tarihi',
    'not_specified' => 'Belirtilmemiş',
    'days' => 'Gün',
    'duration' => 'Süre',
    'assignment_info' => 'Öğrenci ve takım görevleri hedef oluşturulduktan sonra yönetilebilir',
    'student_assignment_description' => 'Hedefi öğrenciler ve takımlara atayın',
    'please_complete_previous_steps_first' => 'Lütfen önce önceki adımları tamamlayın',
	'assigned_by' => 'Atayan',
	'assign_date' => 'Atama Tarihi',
	'due_date' => 'Bitiş Tarihi',
	'active' => 'Aktif',
	'inactive' => 'Pasif',
	'missing_book_type' => 'Kitap türü eksik',
	'missing_page_count' => 'Sayfa sayısı eksik',
	'book_inactive_warning' => 'Bu kitap pasif durumda ve okuma kayıtları veya aktiviteler için kullanılamaz',
	'cannot_create_reading_log_inactive_book' => 'Pasif kitap için okuma kaydı oluşturulamaz',
	'cannot_create_activity_inactive_book' => 'Pasif kitap için aktivite oluşturulamaz',
	'assignments_count' => 'Atama Sayısı',
	'completed_tasks' => 'Tamamlanan Görevler',

	// Book Discovery
	'search_import_book' => 'Kitap Ara ve İçe Aktar',
	'isbn_hint' => 'Kitap bilgilerini otomatik olarak keşfetmek için barkod girip Ara düğmesine basın',
	'publisher_auto_filled' => 'Bu alan kitap bulunduğunda otomatik olarak doldurulacaktır',
	'enter_isbn_to_search' => 'Lütfen arama yapmak için barkodu girin.',
	'invalid_isbn_format' => 'Lütfen geçerli bir barkod girin (10 veya 13 haneli).',
	'book_already_exists' => 'Bu barkoda sahip bir kitap veritabanında zaten mevcut.',
	'book_not_found_sources' => 'Kitap yapılandırılmış kaynaklarda bulunamadı. Lütfen bilgileri manuel olarak girin.',
	'book_exists_locally' => 'Bu kitap yerel veritabanında zaten mevcut.',
	'book_found_auto_populated' => 'Kitap bulundu kaydı otomatik olarak oluşturuldu. Lütfen Kitap Türü bilgisini güncelleyin.',
	'book_search_error' => 'Kitap aranırken bir hata oluştu. Lütfen tekrar deneyin veya bilgileri manuel olarak girin.',

	// Level System
	'levels' => 'Seviyeler',
	'level' => 'Seviye',
	'user_levels' => 'Kullanıcı Seviyeleri',
	'level_number' => 'Seviye Numarası',
	'level_name' => 'Seviye Adı',
	'level_image' => 'Seviye Rozeti',
	'books_required' => 'Gerekli Kitap Sayısı',
	'page_points_required' => 'Gerekli Sayfa Puanı',
	'all_required' => 'Tüm Koşullar Gerekli',
	'users_achieved' => 'Ulaşan Kullanıcı Sayısı',
	'achievement_date' => 'Ulaşma Tarihi',
	'triggered_by_reading' => 'Tetikleyen Okuma',
	'manual' => 'Manuel',
	'level_number_hint' => '1\'den başlayarak artan seviye numarası (düzenlenemez)',
	'level_name_hint' => 'Seviye için görünen ad (örn: "Başlangıç Okuyucusu")',
	'level_image_hint' => 'İsteğe bağlı seviye rozeti/ikonu',
	'books_required_hint' => 'Bu seviyeye ulaşmak için gereken minimum kitap sayısı',
	'page_points_required_hint' => 'Bu seviyeye ulaşmak için gereken minimum sayfa puanı',
	'all_required_hint' => 'Açık: Hem kitap hem puan koşulu karşılanmalı. Kapalı: Herhangi biri yeterli',
	'reading_log_trigger_hint' => 'Bu seviye ulaşımını tetikleyen okuma kaydı (varsa)',
	'can_only_delete_most_recent_reading_log' => 'Sadece en son okuma kaydı silinebilir',

	// Dashboard Metrics
	'dashboard_metrics' => 'Panel Metrikleri',
	'activities_pending_approval' => 'Onay Bekleyen Aktiviteler',
	'students_read_last_24h' => 'Okuyan Öğrenciler (Son 24s)',
	'pages_read_last_24h' => 'Okunan Sayfalar (Son 24s)',
	'activities_done_last_24h' => 'Yapılan Aktiviteler (Son 24s)',

	// Student Rankings
	'student_rankings' => 'Öğrenci Sıralamaları',
	'most_books_read' => 'En Çok Kitap Okuyanlar',
	'fewest_books_read' => 'En Az Kitap Okuyanlar',
	'highest_level_students' => 'En Yüksek Seviye Öğrenciler',
	'most_badges' => 'En Çok Rozeti Olanlar',
	'fewest_badges' => 'En Az Rozeti Olanlar',
	'longest_reading_streak' => 'En Uzun Süredir Ara Vermeden Okuyanlar',
	'longest_reading_gap' => 'En Uzun Süredir Okumayanları',
	'highest_activity_score' => 'En Yüksek Aktivite Puanı',
	'lowest_activity_score' => 'En Düşük Aktivite Puanı',
	'lowest_level_students' => 'En Düşük Seviye Öğrenciler',
	'student_name' => 'Öğrenci Adı',
	'books_count' => 'Kitaplar',
	'level' => 'Seviye',
	'badges_count' => 'Rozetler',
	'streak_days' => 'Seri (Gün)',
	'activity_points' => 'Aktivite Puanları',
	'days_since_reading' => 'Son Okumadan Beri',
	'avatar' => 'Avatar',

];
