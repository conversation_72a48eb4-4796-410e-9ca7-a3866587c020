<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ClassBook;
use App\Models\SchoolClass;
use App\Models\Book;
use App\Models\User;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Fields\Text;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Attributes\UseEloquentBuilder;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Fields\Relationships\HasMany;
use MoonShine\Support\AlpineJs;
use MoonShine\Support\Enums\JsEvent;
use MoonShine\UI\Components\Badge;
use MoonShine\UI\Components\CardsBuilder;
use MoonShine\UI\Fields\Date;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

#[Icon('view-columns')]
class PanelClassBookResource extends ClassBookResource
{
    use WithRolePermissions;
    
    protected function indexFields(): iterable
    {
        return [
        ];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make([
                BelongsTo::make(
                    __('admin.book'),
                    'book',
                    formatted: fn(Book $book) => html_entity_decode($book->name) . ' - ' . $book->isbn,
                    resource: PanelBookResource::class
                )
                    ->required()
                    ->searchable()
                    ->asyncSearch(),
            ]),

        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name . ' (' . $class->school->name . ')',
                resource: SchoolClassResource::class
            ),

            BelongsTo::make(
                __('admin.book'),
                'book',
                formatted: fn(Book $book) => html_entity_decode($book->name),
                resource: PanelBookResource::class
            ),

            Text::make(__('admin.isbn'), 'book.isbn'),
            Text::make(__('admin.publisher'), 'book.publisher.name'),
            Text::make(__('admin.book_type'), 'book.bookType.name'),
            Text::make(__('admin.page_count'), 'book.page_count'),
            Text::make(__('admin.year_of_publish'), 'book.year_of_publish'),
            ...parent::getCommonDetailFields(),
            HasMany::make(__('admin.reading_history'), 'userBooks', PanelUserBookResource::class)
                ->async()
                ->creatable()
                ->fields([
                    BelongsTo::make(
                        __('admin.user'),
                        'user',
                        formatted: fn(User $user) => $user->name,
                        resource: StudentResource::class
                    ),
                    Date::make(__('admin.start_date'), 'start_date')
                    ->format('d.m.Y'),
                    Date::make(__('admin.end_date'), 'end_date')
                    ->format('d.m.Y'),
                    Text::make(__('admin.reading_status'), 'localized_reading_status'),
                ]),
        ];
    }

    protected function modifyQueryBuilder(Builder $builder): Builder
    {
        if (!$builder instanceof UseEloquentBuilder) {
            return $builder;
        }
        return $builder->forCurrentUser();
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), [])
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->title( fn($classBook) => html_entity_decode($classBook->book->name))
            ->subtitle('book.author_names')
            // if class book have any user books with enddate is null, show badge of reading user name else show badge of available
            ->header(fn($classBook) => $classBook->userBooks()->whereNull('end_date')->exists() ? Badge::make($classBook->userBooks()->whereNull('end_date')->first()->user->name, 'info') : Badge::make(__('admin.available'), 'success'))
            ->url(fn($classBook) => $this->getDetailPageUrl($classBook->getKey()))
            ->thumbnail(fn($classBook) => asset('storage/' . $classBook->book->cover_image))
            ->fields([
                Text::make(__('admin.isbn'), 'book.isbn'),
                    ]);
    }

    // override rules to exclude class selection, it will be added on create
    public function rules(mixed $item): array
    {
        return [
            'book_id' => ['required', 'exists:books,id'],
            ...parent::getCommonRules($item),
        ];
    }
}
