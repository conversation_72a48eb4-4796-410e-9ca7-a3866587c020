<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\Traits\BypassesPermissionScopes;

class UserReadingLog extends BaseModel
{
    use BypassesPermissionScopes;
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'book_id',
        'log_date',
        'start_page',
        'end_page',
        'pages_read',
        'reading_duration',
        'book_completed',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'log_date' => 'datetime',
            'book_completed' => 'boolean',
            'start_page' => 'integer',
            'end_page' => 'integer',
            'pages_read' => 'integer',
            'reading_duration' => 'integer',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Calculate pages_read if start_page and end_page are provided
        static::saving(function ($log) {
            if ($log->start_page && $log->end_page) {
                $log->pages_read = $log->end_page - $log->start_page + 1;
            }

            // Handle automatic book completion when pages exceed book's total page count
            if ($log->pages_read && $log->book && $log->book->page_count) {
                $totalPagesRead = UserReadingLog::where('user_id', $log->user_id)
                    ->where('book_id', $log->book_id)
                    ->where('id', '!=', $log->id ?? 0) // Exclude current log if updating
                    ->sum('pages_read');

                $newTotalPages = $totalPagesRead + $log->pages_read;

                // If new total would exceed book's page count, adjust and mark as completed
                if ($newTotalPages > $log->book->page_count) {
                    $remainingPages = max(0, $log->book->page_count - $totalPagesRead);
                    if ($remainingPages > 0) {
                        $log->pages_read = $remainingPages;
                        $log->book_completed = true;
                    } else {
                        // Book was already completed, don't allow this log
                        throw new \Exception(__('mobile.book_already_completed'));
                    }
                } elseif ($newTotalPages == $log->book->page_count) {
                    // Auto-complete when pages equal total pages
                    $log->book_completed = true;
                }
            }
        });

        // Create user points after saving reading log
        static::created(function ($log) {
            // Only calculate reading points if all required activities are completed
            // or if there are no required activities for this book
            if ($log->allRequiredActivitiesCompleted()) {
                $log->calculateAndCreatePoints();
            }

            // Check for task completion after creating reading log
            $log->checkAndCompleteUserTasks();
        });

        // Handle book completion - update UserBook session when book is marked as completed
        static::created(function ($readingLog) {
            if ($readingLog->book_completed) {
                $readingLog->updateUserBookSession();
            }

            // ALWAYS check and award rewards for every reading log creation
            // This enables incremental rewards like daily pages read, weekly minutes read, etc.
            // Only trigger rewards if all required activities are completed
            if ($readingLog->allRequiredActivitiesCompleted()) {
                // Check and award individual rewards after creating reading log
                $readingLog->checkAndAwardRewards();

                // Check and award team rewards for all user's teams
// 05.10.2025 review team awarding logic again
//                $readingLog->checkAndAwardTeamRewards();
            }

            // Check for level progression after creating reading log
            $readingLog->checkAndAwardLevels();
        });

        static::updated(function ($readingLog) {
            // Check if book_completed was changed to true
            if ($readingLog->book_completed && $readingLog->wasChanged('book_completed')) {
                $readingLog->updateUserBookSession();
            }

            // ALWAYS check and award rewards for any significant reading log update
            // This enables incremental rewards like daily pages read, weekly minutes read, etc.
            // Only check if significant fields changed (pages_read, reading_duration, book_completed)
            if ($readingLog->wasChanged(['pages_read', 'reading_duration', 'book_completed'])) {
                // Only trigger rewards if all required activities are completed
                if ($readingLog->allRequiredActivitiesCompleted()) {
                    // Check and award individual rewards after updating reading log
                    $readingLog->checkAndAwardRewards();

                    // Check and award team rewards for all user's teams
// 05.10.2025 review team awarding logic again
//                    $readingLog->checkAndAwardTeamRewards();
                }

                // Check for task completion after updating reading log
                $readingLog->checkAndCompleteUserTasks();
            }

            // Check for level progression after any update
            $readingLog->checkAndAwardLevels();
        });

        // Handle level regression when reading log is deleted
        static::deleting(function ($readingLog) {
            // Remove any user levels that were triggered by this specific reading log
            UserLevel::where('reading_log_id', $readingLog->id)->delete();
        });
    }

    /**
     * Get the user who created this reading log.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the book for this reading log.
     */
    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class);
    }

    /**
     * Get the rewards that were awarded based on this reading log.
     */
    public function userRewards(): HasMany
    {
        return $this->hasMany(UserReward::class, 'reading_log_id');
    }

    /**
     * Get the team rewards that were awarded based on this reading log.
     */
    public function teamRewards(): HasMany
    {
        return $this->hasMany(TeamReward::class, 'reading_log_id');
    }

    /**
     * Check if this reading log has associated reward awards.
     */
    public function hasAssociatedRewards(): bool
    {
        return $this->userRewards()->exists() || $this->teamRewards()->exists();
    }

    /**
     * Get the count of rewards awarded from this reading log.
     */
    public function getRewardCountAttribute(): int
    {
        return $this->userRewards()->count() + $this->teamRewards()->count();
    }

    /**
     * Get the names of rewards awarded from this reading log.
     */
    public function getAwardedRewardNamesAttribute(): string
    {
        $userRewardNames = $this->userRewards()
            ->with('reward')
            ->get()
            ->pluck('reward.name')
            ->toArray();

        $teamRewardNames = $this->teamRewards()
            ->with('reward')
            ->get()
            ->pluck('reward.name')
            ->toArray();

        $allRewardNames = array_merge($userRewardNames, $teamRewardNames);

        return empty($allRewardNames) ? __('admin.no_rewards') : implode(', ', $allRewardNames);
    }

    /**
     * Check and award rewards based on this reading log.
     */
    public function checkAndAwardRewards(): array
    {
        $awardedRewards = [];

        // Use RewardCalculationService for automatic reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        $awardedRewards = $service->checkAndAwardUserRewards($this->user_id, $this->id, null);

        return $awardedRewards;
    }

    /**
     * Check and award team rewards for all user's teams.
     */
    public function checkAndAwardTeamRewards(): array
    {
        // Use RewardCalculationService for automatic team reward awarding
        $service = app(\App\Services\RewardCalculationService::class);
        return $service->checkAndAwardTeamRewards($this->user_id, $this->id, null);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by book.
     */
    public function scopeByBook($query, $bookId)
    {
        return $query->where('book_id', $bookId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('log_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by completion status.
     */
    public function scopeCompleted($query, $completed = true)
    {
        return $query->where('book_completed', $completed);
    }

    /**
     * Scope to get recent logs.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('log_date', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Calculate and create points for this reading log.
     */
    public function calculateAndCreatePoints()
    {
        $points = $this->calculatePoints();
        
        if ($points > 0) {
            UserPoint::create([
                'point_date' => now(),
                'user_id' => $this->user_id,
                'book_id' => $this->book_id,
                'source_id' => $this->id,
                'point_type' => UserPoint::POINT_TYPE_PAGE,
                'points' => $points,
            ]);
        }
    }

    /**
     * Calculate points based on pages read and book/class level.
     */
    public function calculatePoints(): int
    {
        if (!$this->pages_read || $this->pages_read <= 0) {
            return 0;
        }

        // Get user's active class level through user_classes
        $userClass = $this->user->activeUserClasses()->first();
        if (!$userClass || !$userClass->schoolClass) {
            return 0;
        }

        $classLevelId = $userClass->schoolClass->class_level_id;
        $bookTypeId = $this->book->book_type_id;

        // Get page points from page_points table
        $pagePoint = PagePoint::where('book_type_id', $bookTypeId)
            ->where('class_level_id', $classLevelId)
            ->first();

        if (!$pagePoint) {
            return 0;
        }

        return (int) ($this->pages_read * $pagePoint->point);
    }

    /**
     * Get the display name for the reading log.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->name . ' - ' . $this->book->name . ' (' . $this->log_date->format('Y-m-d') . ')';
    }

    /**
     * Get summary information for the reading log.
     */
    public function getSummaryAttribute(): string
    {
        $duration = $this->reading_duration ? " ({$this->reading_duration} min)" : '';
        $completed = $this->book_completed ? ' [Completed]' : '';
        
        return sprintf(
            '%s pages%s%s on %s',
            $this->pages_read,
            $duration,
            $completed,
            $this->log_date->format('M d, Y')
        );
    }

    /**
     * Validation rules for the model.
     */
    public static function validationRules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'book_id' => [
                'required',
                'exists:books,id',
                function ($attribute, $value, $fail) {
                    $book = Book::find($value);
                    if ($book && !$book->canCreateReadingLogs()) {
                        $fail(__('admin.cannot_create_reading_log_inactive_book'));
                    }
                }
            ],
            'log_date' => ['required', 'date', 'before_or_equal:today'],
            'start_page' => ['nullable', 'integer', 'min:1'],
            'end_page' => ['nullable', 'integer', 'min:1', 'gte:start_page'],
            'pages_read' => ['required', 'integer', 'min:1'],
            'reading_duration' => ['nullable', 'integer', 'min:1'],
            'book_completed' => ['boolean'],
        ];
    }

    /**
     * Update the corresponding UserBook session when book is marked as completed.
     */
    public function updateUserBookSession(): void
    {
        // Find the current active session for this user-book combination
        $activeSession = UserBook::getCurrentSession($this->user_id, $this->book_id);

        if (!$activeSession) {
            // No active session found - this might be a reading log without a session
            // or the session was already completed
            return;
        }

        // Validate that the reading log date is within the session's date range
        if ($this->log_date < $activeSession->start_date) {
            // Reading log date is before session start - invalid
            return;
        }

        // If session already has an end date, check if this log date is earlier
        if ($activeSession->end_date && $this->log_date > $activeSession->end_date) {
            // Reading log date is after session end - invalid
            return;
        }

        // Update the session's end_date to the reading log's log_date
        $activeSession->update([
            'end_date' => $this->log_date
        ]);

        // Log the automatic session completion for debugging/auditing
        Log::info('UserBook session automatically completed', [
            'user_id' => $this->user_id,
            'book_id' => $this->book_id,
            'session_id' => $activeSession->id,
            'completion_date' => $this->log_date,
            'reading_log_id' => $this->id
        ]);
    }

    /**
     * Check if all required activities for this book are completed by the user.
     */
    public function allRequiredActivitiesCompleted(): bool
    {
        // Get all required activities for this book with class-specific resolution
//        $user = User::find($this->user_id);
//        $requiredActivities = Activity::resolvedForUser($user)
//            ->where('activities.active', true)

        $requiredActivities = Activity::where('activities.active', true)    
            ->get()
            ->where('required', true); // Filter after resolution to use resolved values

        if ($requiredActivities->isEmpty()) {
            // No required activities, so completion is allowed
            return true;
        }

        foreach ($requiredActivities as $activity) {
            // For quiz and vocabulary test activities, check if book has sufficient questions/words
            if ($activity->isTestActivity()) {
                $hasEnoughContent = $this->bookHasEnoughContentForActivity($activity);
                if (!$hasEnoughContent) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Check if user has completed this activity for this book
            $userActivity = UserActivity::where('user_id', $this->user_id)
                ->where('book_id', $this->book_id)
                ->where('activity_id', $activity->id)
                ->where('status', UserActivity::STATUS_COMPLETED)
                ->first(); 

            if (!$userActivity) {
                // Required activity not completed
                return false;
            }

            // For test activities, check if they passed the minimum grade
            if ($activity->isTestActivity() && $activity->min_grade) {
                $content =  (is_string($userActivity->content)) ? json_decode($userActivity->content, true) : $userActivity->content;
                $score = $content['score_percentage'] ?? 0;

                if ($score < $activity->min_grade) {
                    // User didn't pass the minimum grade
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if book has enough content for a test activity.
     */
    private function bookHasEnoughContentForActivity(Activity $activity): bool
    {
        if ($activity->isQuiz()) {
            // Check if book has at least 10 questions
            $questionCount = BookQuestion::where('book_id', $this->book_id)
                ->where('is_active', true)
                ->count();
            return $questionCount >= 10;
        }

        if ($activity->isVocabularyTest()) {
            // Check if book has at least 10 words
            $wordCount = BookWord::where('book_id', $this->book_id)
                ->where('is_active', true)
                ->count();
            return $wordCount >= 10;
        }

        return true;
    }

    /**
     * Get incomplete required activities for this book and user.
     */
    public function getIncompleteRequiredActivities(): array
    {
        // Get all required activities with class-specific resolution
//        $user = User::find($this->user_id);
//        $requiredActivities = Activity::resolvedForUser($user)
//            ->where('activities.active', true)
        $requiredActivities = Activity::where('activities.active', true)
            ->get()
            ->where('required', true); // Filter after resolution to use resolved values

        $incompleteActivities = [];

        foreach ($requiredActivities as $activity) {
            // For quiz and vocabulary test activities, check if book has sufficient questions/words
            if ($activity->isTestActivity()) {
                $hasEnoughContent = $this->bookHasEnoughContentForActivity($activity);
                if (!$hasEnoughContent) {
                    // Skip this activity if book doesn't have enough content
                    continue;
                }
            }

            // Check if user has completed this activity for this book
            $userActivity = UserActivity::where('user_id', $this->user_id)
                ->where('book_id', $this->book_id)
                ->where('activity_id', $activity->id)
                ->where('status', UserActivity::STATUS_COMPLETED)
                ->first();

            $isCompleted = false;
            if ($userActivity && $activity->isTestActivity() && $activity->min_grade) {
                $content =  (is_string($userActivity->content)) ? json_decode($userActivity->content, true) : $userActivity->content;
                $score = $content['score_percentage'] ?? 0;
                $isCompleted = $score >= $activity->min_grade;
            } elseif ($userActivity) {
                $isCompleted = true;
            }

            if (!$isCompleted) {
                $incompleteActivities[] = $activity;
            }
        }

        return $incompleteActivities;
    }

    /**
     * Award previously withheld reading points and rewards for completed books.
     * This should be called when a required activity is completed.
     */
    public static function awardWithheldRewardsForBook(int $userId, int $bookId): void
    {
        // Find all completed reading logs for this user and book that haven't awarded points yet
        $completedLogs = self::where('user_id', $userId)
            ->where('book_id', $bookId)
            ->where('book_completed', true)
            ->get();

        $rewardsTriggered = false;

        foreach ($completedLogs as $log) {
            // Check if this log already has reading points awarded
            $existingPoints = UserPoint::where('user_id', $userId)
                ->where('book_id', $bookId)
                ->where('source_id', $log->id)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->exists();

            // If no points exist and all required activities are now completed, award them
            if (!$existingPoints && $log->allRequiredActivitiesCompleted()) {
                $log->calculateAndCreatePoints();
                $rewardsTriggered = true;
            }
        }

        // Only trigger rewards once after all points are awarded
        // This prevents duplicate reward awarding
        if ($rewardsTriggered) {
            // Use the most recent completed log for reward checking
            $mostRecentLog = $completedLogs->sortByDesc('log_date')->first();
            if ($mostRecentLog) {
                // Trigger rewards that were withheld
                $mostRecentLog->checkAndAwardRewards();
// 05.10.2025 review team awarding logic again
//                $mostRecentLog->checkAndAwardTeamRewards();

                // Note: Level progression should NOT be triggered here
                // Levels are only awarded when reading logs are created/updated,
                // not when required activities are completed
            }
        }
    }

    /**
     * Check and award new levels for the user based on their current progress.
     */
    public function checkAndAwardLevels()
    {
        $user = $this->user;
        $currentLevelNumber = $user->getCurrentLevelNumber();

        // Get all levels higher than the user's current level
        $availableLevels = Level::where('nr', '>', $currentLevelNumber)
            ->ordered()
            ->get();

        foreach ($availableLevels as $level) {
            // Check if user qualifies for this level and hasn't achieved it yet
            if ($level->userQualifies($user) && !$user->hasAchievedLevel($level)) {
                // Award the level
                UserLevel::create([
                    'user_id' => $user->id,
                    'level_id' => $level->id,
                    'level_date' => now(),
                    'reading_log_id' => $this->id,
                ]);
            }
        }
    }

    /**
     * Check and complete any UserTask instances that should be marked as completed
     * based on the current reading activity.
     *
     * This method uses the TaskProgressCalculationService to determine if any
     * assigned tasks for this user should be marked as completed based on their
     * current progress including this reading log.
     *
     * @return array Array of UserTask instances that were marked as completed
     */
    public function checkAndCompleteUserTasks(): array
    {
        $service = app(\App\Services\TaskProgressCalculationService::class);
        return $service->checkAndCompleteUserTasks($this->user_id, $this->book_id);
    }

    /**
     * Check if this reading log can be deleted (only most recent log can be deleted).
     */
    public function canBeDeleted(): bool
    {
        // Get the most recent reading log for this user
        $mostRecentLog = UserReadingLog::where('user_id', $this->user_id)
            ->orderBy('log_date', 'desc')
            ->orderBy('id', 'desc')
            ->first();

        return $mostRecentLog && $mostRecentLog->id === $this->id;
    }

    /**
     * Get validation rules for deletion.
     */
    public static function deletionRules(): array
    {
        return [
            'can_delete' => function ($attribute, $value, $fail) {
                if ($value instanceof UserReadingLog && !$value->canBeDeleted()) {
                    $fail(__('admin.can_only_delete_most_recent_reading_log'));
                }
            }
        ];
    }
}
