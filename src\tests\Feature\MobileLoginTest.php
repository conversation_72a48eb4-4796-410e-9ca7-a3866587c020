<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

class MobileLoginTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user with student role
        $this->user = User::factory()->create([
            'username' => 'testuser',
            'password' => bcrypt('password123'),
        ]);
        
        $studentRole = Role::factory()->create(['name' => 'student']);
        $this->user->assignRole($studentRole);
    }

    /** @test */
    public function splash_screen_redirects_to_login_for_unauthenticated_user()
    {
        $response = $this->get(route('mobile.splash'));

        $response->assertStatus(200);
        $response->assertSee('logo-img.png');
        $response->assertSee('animate-zoom-in-out');
    }

    /** @test */
    public function login_screen_shows_step_1_by_default()
    {
        Livewire::test('mobile.login')
            ->assertSet('currentStep', 1)
            ->assertSee('Enter user name...')
            ->assertSee('NEXT');
    }

    /** @test */
    public function user_can_proceed_to_password_step()
    {
        Livewire::test('mobile.login')
            ->set('username', 'testuser')
            ->call('proceedToPasswordStep')
            ->assertSet('currentStep', 2)
            ->assertSee('Your password')
            ->assertSee('LOGIN');
    }

    /** @test */
    public function user_can_go_back_to_username_step()
    {
        Livewire::test('mobile.login')
            ->set('currentStep', 2)
            ->call('goBackToUsernameStep')
            ->assertSet('currentStep', 1)
            ->assertSet('password', '');
    }

    /** @test */
    public function user_can_login_with_valid_credentials()
    {
        Livewire::test('mobile.login')
            ->set('username', 'testuser')
            ->set('password', 'password123')
            ->set('currentStep', 2)
            ->call('login')
            ->assertRedirect();

        $this->assertAuthenticatedAs($this->user);
    }

    /** @test */
    public function login_fails_with_invalid_credentials()
    {
        Livewire::test('mobile.login')
            ->set('username', 'testuser')
            ->set('password', 'wrongpassword')
            ->set('currentStep', 2)
            ->call('login')
            ->assertSet('errorMessage', __('mobile.invalid_credentials'))
            ->assertSet('password', '');

        $this->assertGuest();
    }

    /** @test */
    public function username_is_required_to_proceed_to_password_step()
    {
        Livewire::test('mobile.login')
            ->set('username', '')
            ->call('proceedToPasswordStep')
            ->assertHasErrors(['username']);
    }

    /** @test */
    public function selecting_username_from_list_proceeds_to_password_step()
    {
        Livewire::test('mobile.login')
            ->call('selectUsername', 'testuser')
            ->assertSet('username', 'testuser')
            ->assertSet('currentStep', 2);
    }
}
