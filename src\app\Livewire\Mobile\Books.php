<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserBook;
use App\Models\UserReadingLog;
use App\Models\User;
use App\Models\Role;

class Books extends Component
{
    public $searchQuery = '';
    public $currentBooks;
    public $completedBooks;
    public $user;
    public $classRankingData;

    public function mount()
    {
        $this->user = Auth::user();
        $this->loadBooks();
        $this->loadClassRankingData();
    }

    public function updatedSearchQuery()
    {
        $this->loadBooks();
    }

    /**
     * Refresh ranking data (useful for testing or manual refresh).
     */
    public function refreshRanking()
    {
        $this->loadClassRankingData();
    }

    public function markAsCompleted($userBookId)
    {
        $userBook = UserBook::where('id', $userBookId)
            ->where('user_id', $this->user->id)
            ->first();

        if ($userBook && $userBook->isInProgress()) {
            $userBook->update(['end_date' => now()]);

            // Create a reading log entry for completion
            UserReadingLog::create([
                'user_id' => $this->user->id,
                'book_id' => $userBook->book_id,
                'log_date' => now(),
                'pages_read' => $userBook->book->page_count ?? 0,
                'book_completed' => true,
            ]);

            $this->loadBooks();
            $this->loadClassRankingData(); // Refresh ranking after book completion

            // Show success message and redirect to activities
            session()->flash('success', __('mobile.book_completed_success'));
            return redirect()->route('mobile.books.activities', $userBook->book_id);
        }
    }

    public function removeBook($userBookId)
    {
        $userBook = UserBook::where('id', $userBookId)
            ->where('user_id', $this->user->id)
            ->first();

        if ($userBook) {
            $userBook->delete();
            $this->loadBooks();
            session()->flash('success', __('mobile.book_removed_success'));
        }
    }

    private function loadBooks()
    {
        $baseQuery = UserBook::where('user_id', $this->user->id)->with(['book', 'book.authors']);

        // Load current books (always load these)
        $currentQuery = clone $baseQuery;
        $this->currentBooks = $currentQuery->inProgress()->get();

        // Load completed books with search filter
        $completedQuery = clone $baseQuery;
        if ($this->searchQuery) {
            $completedQuery->whereHas('book', function ($q) {
                $q->where('name', 'like', '%' . $this->searchQuery . '%')
                  ->orWhereHas('authors', function ($authorQuery) {
                      $authorQuery->where('name', 'like', '%' . $this->searchQuery . '%');
                  });
            });
        }
        $this->completedBooks = $completedQuery->completed()->latest('end_date')->get();
    }

    /**
     * Check if a book has incomplete required activities.
     */
    public function hasIncompleteRequiredActivities($book)
    {
        // Create a temporary UserReadingLog instance to use the method
        $tempLog = new UserReadingLog([
            'user_id' => $this->user->id,
            'book_id' => $book->id,
        ]);

        $incompleteActivities = $tempLog->getIncompleteRequiredActivities();
        return count($incompleteActivities) > 0;
    }

    public function getReadingProgress($userBook)
    {
        if (!$userBook->book->page_count) {
            return 0;
        }

        $totalPages = UserReadingLog::where('user_id', $userBook->user_id)
            ->where('book_id', $userBook->book_id)
            ->sum('pages_read');

        return min(100, ($totalPages / $userBook->book->page_count) * 100);
    }

    /**
     * Load class ranking data for congratulatory messages.
     */
    private function loadClassRankingData()
    {
        $this->classRankingData = null;

        // Get user's active classes
        $activeClasses = $this->user->activeClasses;

        if ($activeClasses->isEmpty()) {
            return;
        }

        // For now, use the first active class (could be enhanced to handle multiple classes)
        $primaryClass = $activeClasses->first();

        // Get all students in the same class
        $classStudents = User::whereHas('activeClasses', function ($query) use ($primaryClass) {
            $query->where('class_id', $primaryClass->id);
        })
        ->withRole(Role::STUDENT)
        ->get();

        if ($classStudents->count() < 2) {
            return; // Need at least 2 students for ranking
        }

        // Calculate completed books count for each student
        $studentRankings = [];
        foreach ($classStudents as $student) {
            $completedBooksCount = UserBook::where('user_id', $student->id)
                ->whereNotNull('end_date')
                ->count();

            $studentRankings[] = [
                'user_id' => $student->id,
                'name' => $student->name,
                'completed_books' => $completedBooksCount
            ];
        }

        // Sort by completed books count (descending), then by name for consistent ordering
        usort($studentRankings, function ($a, $b) {
            if ($a['completed_books'] === $b['completed_books']) {
                return strcmp($a['name'], $b['name']); // Alphabetical for ties
            }
            return $b['completed_books'] <=> $a['completed_books'];
        });

        // Find current user's position and calculate ranking data
        $currentUserRank = null;
        $currentUserBooks = 0;

        foreach ($studentRankings as $index => $student) {
            if ($student['user_id'] == $this->user->id) {
                $currentUserRank = $index + 1;
                $currentUserBooks = $student['completed_books'];
                break;
            }
        }

        // Only show congratulatory message if user is in top 3
        if ($currentUserBooks > 0 && $currentUserRank && $currentUserRank <= 3) {
            $booksNeededForNextRank = 0;
            $nextRankPosition = null;

            // Calculate books needed to advance (if not already #1)
            if ($currentUserRank > 1) {
                $nextRankPosition = $currentUserRank - 1;
                $nextRankBooks = $studentRankings[$nextRankPosition - 1]['completed_books'];
                $booksNeededForNextRank = max(0, $nextRankBooks - $currentUserBooks + 1);
            }

            $this->classRankingData = [
                'current_rank' => $currentUserRank,
                'completed_books' => $currentUserBooks,
                'total_students' => count($studentRankings),
                'class_name' => $primaryClass->name,
                'books_needed_for_next_rank' => $booksNeededForNextRank,
                'next_rank_position' => $nextRankPosition,
                'is_first_place' => $currentUserRank === 1
            ];
        }
    }

    public function render()
    {
        return view('livewire.mobile.books');
    }
}
