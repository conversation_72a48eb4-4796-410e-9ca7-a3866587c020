<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Sweet1s\MoonshineRBAC\Traits\MoonshineRBACHasRoles;

class User extends Authenticatable
{
    use MoonshineRBACHasRoles, LogsActivity;

    const SUPER_ADMIN_ROLE_ID = 1;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'name',
        'title',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    /**
     * Configure activity logging options for the User model.
     *
     * This method defines which attributes should be logged when the user
     * model is created, updated, or deleted.
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['username', 'name', 'email', 'title'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(fn(string $eventName) => "User {$eventName}");
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-assign default messages to new users
        static::created(function ($user) {
            $defaultMessages = Message::where('default', true)->get();

            foreach ($defaultMessages as $message) {
                MessageRecipient::create([
                    'message_id' => $message->id,
                    'user_id' => $user->id,
                    'read' => false,
                    'sent_date' => now(),
                ]);
            }
        });
    }

    public function isSystemAdmin(): bool
    {
        return $this->hasRole('system_admin');
    }

    public function isRegionUser(): bool
    {
        return $this->hasRole('region_user');
    }

    /**
     * Check if user is school admin.
     */
    public function isSchoolAdmin(): bool
    {
        return $this->hasRole('school_admin');
    }

    /**
     * Check if user is teacher.
     */
    public function isTeacher(): bool
    {
        return $this->hasRole('teacher');
    }

    public function isParent(): bool
    {
        return $this->hasRole('parent');
    }

    /**
     * Check if user is student.
     */
    public function isStudent(): bool
    {
        return $this->hasRole('student');
    }

    /**
     * Get school assignments for this user.
     */
    public function userSchools(): HasMany
    {
        return $this->hasMany(UserSchool::class);
    }

    /**
     * Get active school assignments for this user.
     */
    public function activeUserSchools(): HasMany
    {
        return $this->hasMany(UserSchool::class)->where('active', true);
    }

    /**
     * Get class assignments for this user.
     */
    public function userClasses(): HasMany
    {
        return $this->hasMany(UserClass::class);
    }

    /**
     * Get active class assignments for this user.
     */
    public function activeUserClasses(): HasMany
    {
        return $this->hasMany(UserClass::class)->where('active', true);
    }

    /**
     * Get reading logs for this user.
     */
    public function readingLogs(): HasMany
    {
        return $this->hasMany(UserReadingLog::class);
    }

    /**
     * Get points for this user.
     */
    public function points(): HasMany
    {
        return $this->hasMany(UserPoint::class);
    }

    /**
     * Get activities submitted by this user.
     */
    public function userActivities(): HasMany
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Get activity reviews done by this user (as reviewer).
     */
    public function activityReviews(): HasMany
    {
        return $this->hasMany(UserActivityReview::class, 'reviewed_by');
    }

    /**
     * Get books assigned to this user.
     */
    public function userBooks(): HasMany
    {
        return $this->hasMany(UserBook::class);
    }

    /**
     * Get books currently being read by this user.
     */
    public function booksInProgress(): HasMany
    {
        return $this->hasMany(UserBook::class)->inProgress();
    }

    /**
     * Get books completed by this user.
     */
    public function completedBooks(): HasMany
    {
        return $this->hasMany(UserBook::class)->completed();
    }

    /**
     * Get the user's current avatar selection.
     */
    public function userAvatar(): HasOne
    {
        return $this->hasOne(UserAvatar::class);
    }

    /**
     * Get the user's earned rewards.
     */
    public function userRewards(): HasMany
    {
        return $this->hasMany(UserReward::class);
    }

    /**
     * Get tasks assigned to this user.
     */
    public function userTasks(): HasMany
    {
        return $this->hasMany(UserTask::class);
    }

    /**
     * Get active (incomplete) tasks assigned to this user.
     */
    public function getActiveTasks()
    {
        return $this->userTasks()
            ->with(['task', 'challengeTask.task', 'challengeTask.challenge'])
            ->where('completed', false)
            ->orderBy('assign_date', 'desc')
            ->get()
            ->map(function ($userTask) {
                $actualTask = $userTask->getActualTask();
                if (!$actualTask) {
                    return null;
                }

                $progress = $userTask->getDetailedProgress();

                return (object) [
                    'id' => $userTask->id,
                    'name' => $actualTask->name,
                    'description' => $actualTask->description ?? '',
                    'progress' => '% ' . $progress['percentage'],
                    'completion_percentage' => $progress['percentage'],
                    'assigned_date' => $userTask->assign_date ? $userTask->assign_date->format('d.m.Y') : '',
                    'task_type' => $userTask->task_type,
                    'is_challenge' => $userTask->isChallengeTask(),
                    'is_overdue' => $userTask->isOverdue(),
                    'days_to_complete' => $userTask->days_to_complete,
                ];
            })
            ->filter(); // Remove null entries
    }

    /**
     * Check if user has earned a specific reward.
     */
    public function hasEarnedReward($rewardId): bool
    {
        return $this->userRewards()->where('reward_id', $rewardId)->exists();
    }

    /**
     * Get all rewards earned by this user.
     */
    public function getEarnedRewards()
    {
        return $this->userRewards()
            ->with(['reward', 'awarder', 'readingLog', 'userActivity'])
            ->orderBy('awarded_date', 'desc')
            ->get();
    }

    /**
     * Get recent rewards earned by this user.
     */
    public function getRecentRewards($days = 30)
    {
        return $this->userRewards()
            ->with(['reward', 'awarder', 'readingLog', 'userActivity'])
            ->where('awarded_date', '>=', now()->subDays($days))
            ->orderBy('awarded_date', 'desc')
            ->get();
    }

    /**
     * Get reward statistics for this user.
     */
    public function getRewardStats(): array
    {
        return UserReward::getRewardStatsForUser($this->id);
    }

    /**
     * Get the user's default school assignment.
     */
    public function getDefaultSchool(): ?UserSchool
    {
        return $this->userSchools()->where('default', true)->first();
    }

    /**
     * Get the user's default class assignment.
     */
    public function getDefaultClass(): ?UserClass
    {
        return $this->userClasses()->where('default', true)->first();
    }

    /**
     * Get the user's default school ID.
     */
    public function getDefaultSchoolId(): ?int
    {
        $defaultSchool = $this->getDefaultSchool();
        return $defaultSchool ? $defaultSchool->school_id : null;
    }

    /**
     * Get the user's default class ID.
     */
    public function getDefaultClassId(): ?int
    {
        $defaultClass = $this->getDefaultClass();
        return $defaultClass ? $defaultClass->class_id : null;
    }

    /**
     * Set a school as the user's default.
     */
    public function setDefaultSchool(int $schoolId): bool
    {
        $userSchool = $this->userSchools()->where('school_id', $schoolId)->first();

        if (!$userSchool) {
            return false;
        }

        return $userSchool->setAsDefault();
    }

    /**
     * Set a class as the user's default.
     */
    public function setDefaultClass(int $classId): bool
    {
        $userClass = $this->userClasses()->where('class_id', $classId)->first();

        if (!$userClass) {
            return false;
        }

        return $userClass->setAsDefault();
    }

    /**
     * Get comprehensive student metrics.
     */
    public function getStudentMetrics(): array
    {
        return [
            'books_completed' => $this->readingLogs()->where('book_completed', true)->distinct('book_id')->count(),
            'total_reading_points' => $this->points()->where('point_type', UserPoint::POINT_TYPE_PAGE)->sum('points'),
            'total_reading_minutes' => $this->readingLogs()->sum('reading_duration'),
            'total_rewards_earned' => $this->userRewards()->count(),
            'total_activity_points' => $this->getActivityPoints(),
            'current_reading_streak' => $this->getCurrentReadingStreak(),
        ];
    }



    /**
     * Get reading details including history and current sessions.
     */
    public function getReadingDetails(): array
    {
        $recentLogs = $this->readingLogs()
            ->with('book')
            ->orderBy('log_date', 'desc')
            ->limit(10)
            ->get();

        $currentBooks = UserBook::where('user_id', $this->id)
            ->whereNull('end_date')
            ->with('book')
            ->get();

        $completedBooks = UserBook::where('user_id', $this->id)
            ->whereNotNull('end_date')
            ->with('book')
            ->limit(100)
            ->get();

        return [
            'recent_logs' => $recentLogs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'book_name' => $log->book->name,
                    'log_date' => $log->log_date,
                    'pages_read' => $log->pages_read,
                    'reading_duration' => $log->reading_duration,
                    'book_completed' => $log->book_completed,
                ];
            }),
            'current_books' => $currentBooks->map(function ($userBook) {
                return [
                    'id' => $userBook->id,
                    'book_name' => $userBook->book->name,
                    'cover_image' => $userBook->book->cover_image,
                    'start_date' => $userBook->start_date,
                    'progress_percentage' => $userBook->getProgressPercentage(),
                ];
            }),
            'completed_books' => $completedBooks->map(function ($userBook) {
                return [
                    'id' => $userBook->id,
                    'book_name' => $userBook->book->name,
                    'cover_image' => $userBook->book->cover_image,
                    'start_date' => $userBook->start_date,
                    'end_date' => $userBook->end_date,
                ];
            }),
        ];
    }

    /**
     * Get activity details including submissions and reviews.
     */
    public function getActivityDetails($status = null): array
    {
        $activities = $this->userActivities()
            ->with(['activity', 'book', 'review'])
            ->orderBy('activity_date', 'desc')
            ->limit(10)
            ->when($status, function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->get();

        return $activities->map(function ($userActivity) {
            $review = $userActivity->review;

            return [
                'id' => $userActivity->id,
                'activity_name' => $userActivity->activity->name,
                'book_name' => $userActivity->book->name,
                'activity_date' => $userActivity->activity_date,
                'status' => $userActivity->status,
                'status_text' => $userActivity->getStatusText(),
                'rating' => $userActivity->rating,
                'points_earned' => $userActivity->activity->points,
                'review_status' => $review ? $review->status : null,
                'feedback' => $review ? $review->feedback : null,
            ];
        })->toArray();
    }

    /**
     * Get reward details grouped by type.
     */
    public function getRewardDetails(): array
    {
        $rewards = $this->userRewards()
            ->with('reward')
            ->orderBy('awarded_date', 'desc')
            ->get();

        $groupedRewards = $rewards->groupBy('reward.reward_type');

        // return existing groups as mapped array
        $groupedArray = [];
        foreach ($groupedRewards as $type => $rewards) {
            $groupedArray[$type] = $rewards->map(function ($userReward) {
                return [
                    'id' => $userReward->id,
                    'name' => $userReward->reward->name,
                    'description' => $userReward->reward->description,
                    'image' => $userReward->reward->image,
                    'awarded_date' => $userReward->awarded_date,
                ];
            })->toArray();
        }
        return $groupedArray;        
    }

    /**
     * Get current reading streak (consecutive days).
     */
    public function getCurrentReadingStreak(): int
    {
        $readingDates = $this->readingLogs()
            ->selectRaw('DATE(log_date) as date_only')
            ->orderBy('date_only', 'desc')
            ->distinct()
            ->pluck('date_only')
            ->map(function ($date) {
                return \Carbon\Carbon::parse($date);
            });

        if ($readingDates->isEmpty()) {
            return 0;
        }

        $streak = 0;
        $currentDate = now()->startOfDay();

        $lastReadingDate = $readingDates->first();
        $daysDiff = $currentDate->diffInDays($lastReadingDate);

        if ($daysDiff > 1) {
            return 0;
        }

        foreach ($readingDates as $index => $date) {
            if ($index === 0) {
                $streak = 1;
                continue;
            }

            $previousDate = $readingDates[$index - 1];
            $daysBetween = $previousDate->diffInDays($date);

            if ($daysBetween === 1) {
                $streak++;
            } else {
                break;
            }
        }

        return $streak;
    }









    /**
     * Get the teams this user belongs to.
     */
    public function teams(): BelongsToMany
    {
        return $this->belongsToMany(Team::class, 'user_teams');
    }

    /**
     * Get the user team pivot records.
     */
    public function userTeams(): HasMany
    {
        return $this->hasMany(UserTeam::class);
    }

    /**
     * Get teams where this user is the leader.
     */
    public function ledTeams(): HasMany
    {
        return $this->hasMany(Team::class, 'leader_user_id');
    }

    /**
     * Get the user's currently selected avatar.
     */
    public function getCurrentAvatar(): ?Avatar
    {
        $userAvatar = $this->userAvatar;
        return $userAvatar ? $userAvatar->avatar : null;
    }

    /**
     * Check if user can select a specific avatar based on their activity points.
     */
    public function canSelectAvatar($avatarId): bool
    {
        $avatar = Avatar::find($avatarId);
        if (!$avatar) {
            return false;
        }

        $activityPoints = $this->getActivityPoints();
        return $activityPoints >= $avatar->required_points;
    }

    /**
     * Get the appropriate avatar image URL based on reading activity.
     */
    public function getAvatarDisplayImage(): string
    {
        $currentAvatar = $this->getCurrentAvatar();

        if (!$currentAvatar) {
            // No avatar selected - return default placeholder (avatars/default-avatar.webp)
            return 'avatars/default-avatar.webp';
        }
        $daysSinceLastReading = $this->getDaysSinceLastReading();
        
        if ($daysSinceLastReading === 0) {
            // Happy: has reading activity today
            return $currentAvatar->happy_image ?: $currentAvatar->base_image;
        } elseif ($daysSinceLastReading === -1) {
            // Sleepy: no activity for exactly 1 day
            return $currentAvatar->sleepy_image ?: $currentAvatar->base_image;
        } elseif ($daysSinceLastReading < -1) {
            // Sad: no activity for more than 1 day
            return $currentAvatar->sad_image ?: $currentAvatar->base_image;
        }

        // Default case
        return $currentAvatar->base_image;
    }

    /**
     * Check if user has reading activity today (reading logs or activities).
     */
    public function hasReadingActivityToday(): bool
    {
        $today = now()->toDateString();

        // Check for reading logs today
        $hasReadingLogs = $this->readingLogs()
            ->where('log_date', $today)
            ->exists();

        // Check for activities today
        $hasActivities = $this->userActivities()
            ->where('activity_date', $today)
            ->exists();

        return $hasReadingLogs || $hasActivities;
    }

    /**
     * Get the number of days since last reading activity.
     */
    public function getDaysSinceLastReading(): int
    {
        // Get the most recent reading log date
        $lastReadingLogDate = $this->readingLogs()
            ->max('log_date');

        if (!$lastReadingLogDate) {
            // No reading activity found - return a large number
            return 999;
        }

/*
        // Get the most recent activity date
        $lastActivityDate = $this->userActivities()
            ->max('activity_date');

        // Find the most recent date between the two
        $lastActivityDate = collect([$lastReadingLogDate, $lastActivityDate])
            ->filter()
            ->max();

        if (!$lastActivityDate) {
            // No reading activity found - return a large number
            return 999;
        }

        return now()->startOfDay()->diffInDays($lastActivityDate);
*/
        return now()->startOfDay()->diffInDays($lastReadingLogDate);
    }

    /**
     * Select an avatar for this user.
     */
    public function selectAvatar($avatarId): bool
    {
        if (!$this->canSelectAvatar($avatarId)) {
            return false;
        }

        UserAvatar::selectAvatarForUser($this->id, $avatarId);
        return true;
    }

    /**
     * Remove avatar selection for this user.
     */
    public function removeAvatarSelection(): bool
    {
        return UserAvatar::removeSelectionForUser($this->id);
    }

    /**
     * Get available avatars that this user can select based on activity points.
     */
    public function getAvailableAvatars()
    {
        $activityPoints = $this->getActivityPoints();

        return Avatar::where('required_points', '<=', $activityPoints)
            ->where('active', true)
            ->orderBy('required_points', 'asc')
            ->get();
    }

    /**
     * Get locked avatars that this user cannot select yet based on activity points.
     */
    public function getLockedAvatars()
    {
        $activityPoints = $this->getActivityPoints();

        return Avatar::where('required_points', '>', $activityPoints)
            ->where('active', true)
            ->orderBy('required_points', 'asc')
            ->get();
    }

    /**
     * Accessor for avatar display image attribute.
     */
    public function getAvatarDisplayImageAttribute(): string
    {
        return $this->getAvatarDisplayImage();
    }

    /**
     * Get the user's total points (all types).
     */
    public function getTotalPointsAttribute(): int
    {
        return $this->points()->sum('points');
    }

    /**
     * Get the user's activity points only (for avatar selection).
     */
    public function getActivityPoints(): int
    {
        return $this->points()
            ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
            ->sum('points');
    }

    /**
     * Get the user's activity points as an attribute.
     */
    public function getActivityPointsAttribute(): int
    {
        return $this->getActivityPoints();
    }



    /**
     * Get user's active teams.
     */
    public function getTeams()
    {
        return $this->teams()->where('active', true)->get();
    }

    /**
     * Check if user is member of a specific team.
     */
    public function isMemberOfTeam($teamId): bool
    {
        return UserTeam::isUserInTeam($this->id, $teamId);
    }

    /**
     * Check if user is leader of a specific team.
     */
    public function isLeaderOfTeam($teamId): bool
    {
        return Team::where('id', $teamId)->where('leader_user_id', $this->id)->exists();
    }

    /**
     * Get teams where this user is a leader.
     */
    public function getLeaderTeams()
    {
        return $this->ledTeams()->where('active', true)->get();
    }



    /**
     * Get schools this user is assigned to.
     */
    public function schools()
    {
        return $this->belongsToMany(School::class, 'user_schools')
                    ->withPivot(['role_id', 'active']);
    }

    /**
     * Get active schools this user is assigned to.
     */
    public function activeSchools()
    {
        return $this->belongsToMany(School::class, 'user_schools')
                    ->withPivot(['role_id', 'active'])
                    ->wherePivot('active', true);
    }

    /**
     * Get classes this user is assigned to.
     */
    public function classes()
    {
        return $this->belongsToMany(SchoolClass::class, 'user_classes', 'user_id', 'class_id')
                    ->withPivot(['school_id', 'active']);
    }

    /**
     * Get active classes this user is assigned to.
     */
    public function activeClasses()
    {
        return $this->belongsToMany(SchoolClass::class, 'user_classes', 'user_id', 'class_id')
                    ->withPivot(['school_id', 'active'])
                    ->wherePivot('active', true);
    }

      /**
     * Get the display name for the user.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * Get the user's agreements.
     */
    public function agreements(): HasMany
    {
        return $this->hasMany(UserAgreement::class);
    }

    /**
     * Get the message recipients for this user.
     */
    public function messageRecipients(): HasMany
    {
        return $this->hasMany(MessageRecipient::class);
    }

    /**
     * Get the messages received by this user.
     */
    public function messages(): BelongsToMany
    {
        return $this->belongsToMany(Message::class, 'message_recipients')
            ->withPivot('read', 'sent_date', 'read_date')
            ->orderBy('message_date', 'desc');
    }

    /**
     * Get unread messages count for this user.
     */
    public function getUnreadMessagesCountAttribute(): int
    {
        return $this->messageRecipients()->unread()->count();
    }

    /**
     * Get unread messages for this user.
     */
    public function unreadMessages()
    {
        return $this->messages()->wherePivot('read', false);
    }

    /**
     * Get read messages for this user.
     */
    public function readMessages()
    {
        return $this->messages()->wherePivot('read', true);
    }

    /**
     * Get friends from the same class or same teams.
     */
    public function getFriends()
    {
        $friendIds = collect();

        // Get friends from same classes
        $classIds = $this->activeUserClasses()->pluck('class_id');
        if ($classIds->isNotEmpty()) {
            $classmateIds = UserClass::whereIn('class_id', $classIds)
                ->where('user_id', '!=', $this->id)
                ->where('active', true)
                ->pluck('user_id');
            $friendIds = $friendIds->merge($classmateIds);
        }

        // Get friends from same teams
        $teamIds = $this->userTeams()->pluck('team_id');
        if ($teamIds->isNotEmpty()) {
            $teammateIds = UserTeam::whereIn('team_id', $teamIds)
                ->where('user_id', '!=', $this->id)
                ->pluck('user_id');
            $friendIds = $friendIds->merge($teammateIds);
        }

        // Remove duplicates and get users - filter only students
        $uniqueFriendIds = $friendIds->unique();

        return User::whereIn('id', $uniqueFriendIds)
            ->withRole(Role::STUDENT)
            ->with(['activeUserClasses.schoolClass', 'userTeams.team'])
            ->get();
    }

    /**
     * Get friends from the same class only.
     */
    public function getClassmates()
    {
        $classIds = $this->activeUserClasses()->pluck('class_id');

        if ($classIds->isEmpty()) {
            return collect();
        }

        $classmateIds = UserClass::whereIn('class_id', $classIds)
            ->where('user_id', '!=', $this->id)
            ->where('active', true)
            ->pluck('user_id');

        return User::whereIn('id', $classmateIds)
            ->withRole(Role::STUDENT)
            ->with(['activeUserClasses.schoolClass', 'userTeams.team'])
            ->get();
    }

    /**
     * Get friends from the same teams only.
     */
    public function getTeammates()
    {
        $teamIds = $this->userTeams()->pluck('team_id');

        if ($teamIds->isEmpty()) {
            return collect();
        }

        $teammateIds = UserTeam::whereIn('team_id', $teamIds)
            ->where('user_id', '!=', $this->id)
            ->pluck('user_id');

        return User::whereIn('id', $teammateIds)
            ->withRole(Role::STUDENT)
            ->with(['activeUserClasses.schoolClass', 'userTeams.team'])
            ->get();
    }

    /**
     * Get shared teams with another user.
     */
    public function getSharedTeams(User $otherUser)
    {
        $myTeamIds = $this->userTeams()->pluck('team_id');
        $theirTeamIds = $otherUser->userTeams()->pluck('team_id');

        $sharedTeamIds = $myTeamIds->intersect($theirTeamIds);

        return Team::whereIn('id', $sharedTeamIds)->get();
    }

    /**
     * Get total books completed by this user.
     */
    public function getTotalBooksCompleted(): int
    {
        // calculate total books completed by this user by user_books table
        return UserBook::where('user_id', $this->id)
             ->whereNotNull('end_date')
             ->distinct('book_id')
             ->count();
    }

    /**
     * Get total activity points earned by this user.
     */
    public function getTotalActivityPoints(): int
    {
        return UserPoint::where('user_id', $this->id)
            ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
            ->sum('points');
    }

    /**
     * Get total badges/achievements earned by this user.
     */
    public function getTotalBadges(): int
    {
        return $this->userRewards()->byRewardType(Reward::TYPE_BADGE)->count();
    }

    /**
     * Get total rewards earned by this user (all types).
     */
    public function getTotalRewards(): int
    {
        return $this->userRewards()->count();
    }

    /**
     * Get total rewards by type.
     */
    public function getTotalRewardsByType(): array
    {
        return $this->getRewardStats();
    }

    /**
     * Get total page points earned by this user.
     */
    public function getTotalPagePoints(): int
    {
        return UserPoint::where('user_id', $this->id)
            ->where('point_type', UserPoint::POINT_TYPE_PAGE)
            ->sum('points');
    }

    /**
     * Get the user's level achievements.
     */
    public function userLevels(): HasMany
    {
        return $this->hasMany(UserLevel::class);
    }

    /**
     * Get the user's current level (highest achieved level).
     */
    public function getCurrentLevel(): ?Level
    {
        $userLevel = $this->userLevels()
            ->with('level')
            ->join('levels', 'user_levels.level_id', '=', 'levels.id')
            ->orderBy('levels.nr', 'desc')
            ->first();

        return $userLevel ? $userLevel->level : null;
    }

    /**
     * Get the user's current level number.
     */
    public function getCurrentLevelNumber(): int
    {
        $currentLevel = $this->getCurrentLevel();
        return $currentLevel ? $currentLevel->nr : 0;
    }

    /**
     * Get the next level the user can achieve.
     */
    public function getNextLevel(): ?Level
    {
        $currentLevelNumber = $this->getCurrentLevelNumber();

        return Level::where('nr', '>', $currentLevelNumber)
            ->orderBy('nr', 'asc')
            ->first();
    }

    /**
     * Check if user has achieved a specific level.
     */
    public function hasAchievedLevel(Level $level): bool
    {
        return $this->userLevels()
            ->where('level_id', $level->id)
            ->exists();
    }

    /**
     * Get user's level achievement history.
     */
    public function getLevelHistory()
    {
        return $this->userLevels()
            ->with(['level', 'readingLog.book'])
            ->recent()
            ->get();
    }

    /**
     * Get progress toward next level.
     */
    public function getLevelProgress(): array
    {
        $currentLevel = $this->getCurrentLevel();;
        $nextLevel = $this->getNextLevel();

        if (!$nextLevel) {
            return [
                'current_level' => $currentLevel->nr ?? 0,
                'next_level' => null,
                'books_progress' => 100,
                'points_progress' => 100,
                'overall_progress' => 100,
                'is_max_level' => true,
            ];
        }

        $userBooks = $this->getTotalBooksCompleted();
        $userPoints = $this->getTotalPagePoints();

        $booksProgress = $nextLevel->books_count > 0
            ? min(100, ($userBooks / $nextLevel->books_count) * 100)
            : 100;

        $pointsProgress = $nextLevel->page_points > 0
            ? min(100, ($userPoints / $nextLevel->page_points) * 100)
            : 100;

        // Calculate overall progress based on level requirements
        if ($nextLevel->all_required) {
            // Both conditions must be met - use minimum progress
            $overallProgress = min($booksProgress, $pointsProgress);
        } else {
            // Either condition can be met - use maximum progress
            $overallProgress = max($booksProgress, $pointsProgress);
        }

        return [
            'current_level' => $currentLevel->nr ?? 0,
            'current_level_name' => $currentLevel->name ?? '',
            'current_level_image' => $currentLevel->image ?? '',
            'next_level' => $nextLevel->nr,
            'next_level_name' => $nextLevel->name,
            'books_current' => $userBooks,
            'books_required' => $nextLevel->books_count,
            'books_progress' => round($booksProgress, 1),
            'points_current' => $userPoints,
            'points_required' => $nextLevel->page_points,
            'points_progress' => round($pointsProgress, 1),
            'overall_progress' => round($overallProgress, 1),
            'all_required' => $nextLevel->all_required,
            'is_max_level' => false,
        ];
    }

    /**
     * Get reading streak data for the last N days.
     */
    public function getReadingStreakData(int $days = 30): array
    {
        $analyzer = new \App\Services\ReadingStreakAnalyzer();
        return $analyzer->getReadingStreakData($this->id, $days);
    }

    /**
     * Get recent badges earned by this user.
     */
    public function getRecentBadges(int $limit = 5)
    {
        return $this->userRewards()
            ->byRewardType(Reward::TYPE_BADGE)
            ->with('reward')
            ->orderBy('awarded_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent rewards earned by this user (limited by count).
     */
    public function getRecentRewardsByLimit(int $limit = 5)
    {
        return $this->userRewards()
            ->with('reward')
            ->orderBy('awarded_date', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get rewards grouped by type.
     */
    public function getRewardsGroupedByType()
    {
        return $this->userRewards()
            ->with('reward')
            ->orderBy('awarded_date', 'desc')
            ->get()
            ->groupBy('reward.reward_type');
    }

    /**
     * Get reading pattern analysis with motivational message.
     */
    public function getReadingPatternAnalysis(): array
    {
        $analyzer = new \App\Services\ReadingStreakAnalyzer();
        return $analyzer->analyzeReadingPattern($this->id);
    }

    /**
     * Get currently reading book.
     */
    public function getCurrentlyReadingBook()
    {
        return UserBook::where('user_id', $this->id)
            ->whereNull('end_date')
            ->with('book')
            ->latest('start_date')
            ->first();
    }

    /**
     * Get primary class name for display.
     */
    public function getPrimaryClassName(): ?string
    {
        $defaultClass = $this->getDefaultClass();
        return $defaultClass ? $defaultClass->schoolClass->name : null;
    }

    /**
     * Check if user has accepted the current privacy policy.
     */
    public function hasAcceptedCurrentPrivacyPolicy(): bool
    {
        return UserAgreement::hasAcceptedCurrentPrivacyPolicy($this->id);
    }

    /**
     * Scope to filter users by role name.
     */
    public function scopeWithRole($query, string $roleName)
    {
        return $query->whereHas('roles', function ($q) use ($roleName) {
            $q->where('name', $roleName);
        });
    }

    /**
     * Scope to filter users by multiple role names.
     */
    public function scopeWithAnyRole($query, array $roleNames)
    {
        return $query->whereHas('roles', function ($q) use ($roleNames) {
            $q->whereIn('name', $roleNames);
        });
    }

    /**
     * Scope to filter users by school assignment.
     */
    public function scopeInSchool($query, int $schoolId)
    {
        return $query->whereHas('userSchools', function ($q) use ($schoolId) {
            $q->where('school_id', $schoolId)->where('active', true);
        });
    }

    /**
     * Scope to filter users by class assignment.
     */
    public function scopeInClass($query, int $classId)
    {
        return $query->whereHas('userClasses', function ($q) use ($classId) {
            $q->where('class_id', $classId)->where('active', true);
        });
    }

    /**
     * Scope to filter users who have at least one active role.
     */
    public function scopeActive($query)
    {
        return $query->whereHas('roles');
    }

    /**
     * Role-based access control scopes
     */

    /**
     * Apply role-based filtering based on the current authenticated user.
     * This is the main method that should be used for role-based access control.
     */
    public function scopeForCurrentUser($query)
    {
        $user = auth('moonshine')->user() ?: auth('web')->user();

        if (!$user || !($user instanceof User)) {
            // No user authenticated, restrict all access
            return $query->where('id', 0);
        }

        // System admin has full access
        if ($user->isSystemAdmin()) {
            return $query; // No restrictions
        }

        // Apply role-specific filtering
        if ($user->isSchoolAdmin()) {
            return $query->forSchoolAdmin($user);
        }

        if ($user->isTeacher()) {
            return $query->forTeacher($user);
        }

        if ($user->isParent()) {
            return $query->forParent($user);
        }

        if ($user->isStudent()) {
            return $query->forStudent($user);
        }

        // Unknown role or no role - no access
        return $query->where('id', 0);
    }

    /**
     * Scope for system admin access - no restrictions.
     */
    public function scopeForSystemAdmin($query)
    {
        // System admins can access all users
        return $query;
    }

    /**
     * Scope for school admin access - can access users in their assigned schools.
     */
    public function scopeForSchoolAdmin($query, User $user)
    {
        $schoolIds = $user->activeUserSchools()->pluck('school_id')->toArray();

        if (empty($schoolIds)) {
            // No schools assigned - only access to themselves
            return $query->where('id', $user->id);
        }

        // Access users who have active assignments in the admin's schools
        // This includes teachers and students in those schools
        return $query->where(function ($q) use ($schoolIds, $user) {
            $q->where('id', $user->id) // Include themselves
              ->orWhereHas('activeUserSchools', function ($subQuery) use ($schoolIds) {
                  $subQuery->whereIn('school_id', $schoolIds);
              })
              ->orWhereHas('activeUserClasses', function ($subQuery) use ($schoolIds) {
                  $subQuery->whereIn('school_id', $schoolIds);
              });
        });
    }

    /**
     * Scope for teacher access - can access students in their assigned classes.
     */
    public function scopeForTeacher($query, User $user)
    {
        $classIds = $user->activeUserClasses()->pluck('class_id')->toArray();

        if (empty($classIds)) {
            // No classes assigned - only access to themselves
            return $query->where('id', $user->id);
        }

        // Access students who have active assignments in the teacher's classes
        return $query->where(function ($q) use ($classIds, $user) {
//            $q->where('id', $user->id) // Include themselves
//              ->orWhere(function ($subQuery) use ($classIds) {
            $q->where(function ($subQuery) use ($classIds) {
                $subQuery->whereHas('activeUserClasses', function ($classQuery) use ($classIds) {
                    $classQuery->whereIn('class_id', $classIds);
                })
                ->whereHas('roles', function ($roleQuery) {
                    $roleQuery->where('name', 'student');
                });
            });
        });
    }

    /**
     * Scope for parent access - can access their own children (students).
     */
    public function scopeForParent($query, User $user)
    {
        // For now, parents can only access themselves
        // TODO: Implement parent-child relationships when the feature is added
        return $query->where('id', $user->id);
    }

    /**
     * Scope for student access - can only access themselves.
     */
    public function scopeForStudent($query, User $user)
    {
        return $query->where('id', $user->id);
    }

    /**
     * Get students for a specific school.
     */
    public static function getStudentsForSchool(?int $schoolId = null)
    {
        $query = static::withRole('student');

        if ($schoolId) {
            $query->inSchool($schoolId);
        }

        return $query;
    }

    /**
     * Get teachers for a specific school.
     */
    public static function getTeachersForSchool(?int $schoolId = null)
    {
        $query = static::withRole('teacher');

        if ($schoolId) {
            $query->inSchool($schoolId);
        }

        return $query;
    }

    /**
     * Get school admins for a specific school.
     */
    public static function getSchoolAdminsForSchool(?int $schoolId = null)
    {
        $query = static::withRole('school_admin');

        if ($schoolId) {
            $query->inSchool($schoolId);
        }

        return $query;
    }
}
