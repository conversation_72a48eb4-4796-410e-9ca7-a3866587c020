<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\UserTask;
use App\Models\UserReadingLog;
use App\Models\UserPoint;
use App\Models\UserActivity;
use App\Models\EnumTaskType;
use App\Models\EnumTaskCycle;
use Carbon\Carbon;

/**
 * TaskProgressCalculationService
 * 
 * Comprehensive service for calculating user task progress based on task type and cycle combinations.
 * Implements the specifications from analysis/task_progress_calculation.md
 */
class TaskProgressCalculationService
{
    /**
     * Calculate progress for a user task.
     */
    public function calculateProgress(UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        
        if (!$task || !$task->taskType || !$task->taskCycle) {
            return $this->getDefaultProgress();
        }

        $taskType = $task->taskType;
        $taskCycle = $task->taskCycle;
        
        // Get date range for calculation
        $dateRange = $this->getTaskDateRange($userTask);
        
        // Calculate based on task type and cycle combination
        return $this->calculateProgressByTypeAndCycle(
            $userTask,
            $taskType,
            $taskCycle,
            $dateRange['start'],
            $dateRange['end']
        );
    }

    /**
     * Get the date range for task calculation.
     */
    private function getTaskDateRange(UserTask $userTask): array
    {
        if ($userTask->isChallengeTask() && $userTask->challengeTask) {
            return [
                'start' => Carbon::parse($userTask->challengeTask->start_date),
                'end' => Carbon::parse($userTask->challengeTask->end_date),
            ];
        }

        // For standalone tasks, use start_date and due_date or assign_date
        $startDate = $userTask->start_date ? 
            Carbon::parse($userTask->start_date) : 
            Carbon::parse($userTask->assign_date);
            
        $endDate = $userTask->due_date ? 
            Carbon::parse($userTask->due_date) : 
            Carbon::now()->addDays(30); // Default 30 days if no due date

        return [
            'start' => $startDate,
            'end' => $endDate,
        ];
    }

    /**
     * Calculate progress based on task type and cycle combination.
     */
    private function calculateProgressByTypeAndCycle(
        UserTask $userTask,
        EnumTaskType $taskType,
        EnumTaskCycle $taskCycle,
        Carbon $startDate,
        Carbon $endDate
    ): array {
        $userId = $userTask->user_id;
        $taskValue = $userTask->getActualTask()->task_value;

        return match([$taskType->nr, $taskCycle->nr]) {
            // READ_PAGES combinations
            [EnumTaskType::READ_PAGES, EnumTaskCycle::TOTAL] =>
                $this->calculateReadPagesTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_PAGES, EnumTaskCycle::DAILY] =>
                $this->calculateReadPagesDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_PAGES, EnumTaskCycle::WEEKLY] =>
                $this->calculateReadPagesWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_PAGES, EnumTaskCycle::MONTHLY] =>
                $this->calculateReadPagesMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // READ_BOOKS combinations
            [EnumTaskType::READ_BOOKS, EnumTaskCycle::TOTAL] =>
                $this->calculateReadBooksTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_BOOKS, EnumTaskCycle::DAILY] =>
                $this->calculateReadBooksDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_BOOKS, EnumTaskCycle::WEEKLY] =>
                $this->calculateReadBooksWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_BOOKS, EnumTaskCycle::MONTHLY] =>
                $this->calculateReadBooksMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // READ_MINUTES combinations
            [EnumTaskType::READ_MINUTES, EnumTaskCycle::TOTAL] =>
                $this->calculateReadMinutesTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_MINUTES, EnumTaskCycle::DAILY] =>
                $this->calculateReadMinutesDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_MINUTES, EnumTaskCycle::WEEKLY] =>
                $this->calculateReadMinutesWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_MINUTES, EnumTaskCycle::MONTHLY] =>
                $this->calculateReadMinutesMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // READ_DAYS combinations
            [EnumTaskType::READ_DAYS, EnumTaskCycle::TOTAL] =>
                $this->calculateReadDaysTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_DAYS, EnumTaskCycle::DAILY] =>
                $this->calculateReadDaysDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_DAYS, EnumTaskCycle::WEEKLY] =>
                $this->calculateReadDaysWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_DAYS, EnumTaskCycle::MONTHLY] =>
                $this->calculateReadDaysMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // READ_STREAK combinations
            [EnumTaskType::READ_STREAK, EnumTaskCycle::TOTAL] =>
                $this->calculateReadStreakTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_STREAK, EnumTaskCycle::DAILY] =>
                $this->calculateReadStreakDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_STREAK, EnumTaskCycle::WEEKLY] =>
                $this->calculateReadStreakWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::READ_STREAK, EnumTaskCycle::MONTHLY] =>
                $this->calculateReadStreakMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // EARN_READING_POINTS combinations
            [EnumTaskType::EARN_READING_POINTS, EnumTaskCycle::TOTAL] => 
                $this->calculateEarnReadingPointsTotal($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_READING_POINTS, EnumTaskCycle::DAILY] => 
                $this->calculateEarnReadingPointsDaily($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_READING_POINTS, EnumTaskCycle::WEEKLY] => 
                $this->calculateEarnReadingPointsWeekly($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_READING_POINTS, EnumTaskCycle::MONTHLY] => 
                $this->calculateEarnReadingPointsMonthly($userId, $taskValue, $startDate, $endDate),

            // EARN_ACTIVITY_POINTS combinations
            [EnumTaskType::EARN_ACTIVITY_POINTS, EnumTaskCycle::TOTAL] => 
                $this->calculateEarnActivityPointsTotal($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_ACTIVITY_POINTS, EnumTaskCycle::DAILY] => 
                $this->calculateEarnActivityPointsDaily($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_ACTIVITY_POINTS, EnumTaskCycle::WEEKLY] => 
                $this->calculateEarnActivityPointsWeekly($userId, $taskValue, $startDate, $endDate),
            [EnumTaskType::EARN_ACTIVITY_POINTS, EnumTaskCycle::MONTHLY] => 
                $this->calculateEarnActivityPointsMonthly($userId, $taskValue, $startDate, $endDate),

            // COMPLETE_BOOK_ACTIVITY combinations
            [EnumTaskType::COMPLETE_BOOK_ACTIVITY, EnumTaskCycle::TOTAL] => 
                $this->calculateCompleteBookActivityTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_ACTIVITY, EnumTaskCycle::DAILY] => 
                $this->calculateCompleteBookActivityDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_ACTIVITY, EnumTaskCycle::WEEKLY] => 
                $this->calculateCompleteBookActivityWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_ACTIVITY, EnumTaskCycle::MONTHLY] => 
                $this->calculateCompleteBookActivityMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            // COMPLETE_BOOK_LIST combinations
            [EnumTaskType::COMPLETE_BOOK_LIST, EnumTaskCycle::TOTAL] => 
                $this->calculateCompleteBookListTotal($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_LIST, EnumTaskCycle::DAILY] => 
                $this->calculateCompleteBookListDaily($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_LIST, EnumTaskCycle::WEEKLY] => 
                $this->calculateCompleteBookListWeekly($userId, $taskValue, $startDate, $endDate, $userTask),
            [EnumTaskType::COMPLETE_BOOK_LIST, EnumTaskCycle::MONTHLY] => 
                $this->calculateCompleteBookListMonthly($userId, $taskValue, $startDate, $endDate, $userTask),

            default => $this->getDefaultProgress(),
        };
    }

    /**
     * Get default progress structure.
     */
    private function getDefaultProgress(): array
    {
        return [
            'percentage' => 0.0,
            'current' => 0,
            'target' => 1,
            'completed' => false,
            'periods_completed' => 0,
            'total_periods' => 1,
        ];
    }

    /**
     * Create progress result array.
     */
    private function createProgressResult(
        int $current,
        int $target,
        int $periodsCompleted = 0,
        int $totalPeriods = 1
    ): array {
        $percentage = $target > 0 ? min(100, ($current / $target) * 100) : 0.0;

        return [
            'percentage' => round($percentage, 2),
            'current' => $current,
            'target' => $target,
            'completed' => $percentage >= 100,
            'periods_completed' => $periodsCompleted,
            'total_periods' => $totalPeriods,
        ];
    }

    /**
     * Check if a UserTask should be marked as completed based on progress calculation.
     *
     * This method calculates the current progress for a UserTask and determines
     * if it meets the completion criteria (100% progress). It respects category
     * filtering and all task type/cycle combinations.
     *
     * @param UserTask $userTask The user task to check for completion
     * @return bool True if the task should be marked as completed, false otherwise
     */
    public function shouldTaskBeCompleted(UserTask $userTask): bool
    {
        // Skip if task is already completed
        if ($userTask->completed) {
            return false;
        }

        // Calculate current progress
        $progress = $this->calculateProgress($userTask);

        // Task should be completed if progress is 100%
        return $progress['completed'] === true;
    }

    /**
     * Check and update completion status for a UserTask if criteria are met.
     *
     * This method checks if a UserTask should be completed and automatically
     * marks it as completed if the progress criteria are met.
     *
     * @param UserTask $userTask The user task to check and potentially complete
     * @return bool True if the task was marked as completed, false otherwise
     */
    public function checkAndCompleteTask(UserTask $userTask): bool
    {
        if ($this->shouldTaskBeCompleted($userTask)) {
            $userTask->markAsCompleted();
            return true;
        }

        return false;
    }

    /**
     * Find and check all relevant UserTask instances for a user that might be completed.
     *
     * This method finds all incomplete UserTask instances for a user and checks if any
     * should be marked as completed based on their current progress. It's designed to be
     * called from model event handlers when reading activities are updated.
     *
     * @param int $userId The user ID to check tasks for
     * @param int|null $bookId Optional book ID to filter tasks related to specific books
     * @return array Array of UserTask instances that were marked as completed
     */
    public function checkAndCompleteUserTasks(int $userId, ?int $bookId = null): array
    {
        $completedTasks = [];

        // Find all incomplete UserTask instances for this user
        $query = UserTask::where('user_id', $userId)
            ->where('completed', false)
            ->with(['task', 'challengeTask.task', 'task.taskType', 'task.taskCycle', 'challengeTask.task.taskType', 'challengeTask.task.taskCycle']);

        // If book ID is provided, we can optimize by only checking tasks that might be affected
        // For now, we'll check all tasks as category filtering might make any task relevant

        $userTasks = $query->get();

        foreach ($userTasks as $userTask) {
            if ($this->checkAndCompleteTask($userTask)) {
                $completedTasks[] = $userTask;
            }
        }

        return $completedTasks;
    }

    /**
     * Get reading days count in date range.
     */
    private function getReadingDaysCount(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        return UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->selectRaw('DATE(log_date) as date_only')
            ->distinct()
            ->count('date_only');
    }

    /**
     * Get total pages read in date range.
     */
    private function getTotalPagesRead(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        $sum = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('pages_read') ?? 0;
        return (int)$sum;
    }

    /**
     * Get total books completed in date range.
     */
    private function getTotalBooksCompleted(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        return UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->where('book_completed', true)
            ->distinct('book_id')
            ->count();
    }

    /**
     * Get total books completed in date range with optional category filtering.
     */
    private function getTotalBooksCompletedWithCategoryFilter(int $userId, Carbon $startDate, Carbon $endDate, UserTask $userTask): int
    {
        $task = $userTask->getActualTask();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->where('book_completed', true);

        // Apply category filtering if task has associated categories
        if (!empty($taskCategories)) {
            $query->whereHas('book.categories', function ($q) use ($taskCategories) {
                $q->whereIn('categories.id', $taskCategories);
            });
        }

        return $query->distinct('book_id')->count();
    }

    /**
     * Apply category filtering to a UserReadingLog query if task has categories.
     *
     * This method checks if the task has associated book categories and applies
     * filtering to only include reading logs from books that belong to those categories.
     * If the task has no categories, the query remains unchanged (backward compatibility).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query The UserReadingLog query builder
     * @param UserTask $userTask The user task containing category information
     * @return \Illuminate\Database\Eloquent\Builder The modified query with category filtering applied
     *
     * @example
     * // For a task with Science Fiction and Fantasy categories:
     * // Only reading logs from books in those categories will be included
     * $query = $this->applyCategoryFilter($query, $userTask);
     */
    private function applyCategoryFilter($query, UserTask $userTask)
    {
        $task = $userTask->getActualTask();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        // Apply category filtering if task has associated categories
        if (!empty($taskCategories)) {
            $query->whereHas('book.categories', function ($q) use ($taskCategories) {
                $q->whereIn('categories.id', $taskCategories);
            });
        }

        return $query;
    }

    /**
     * Get total pages read in date range with optional category filtering.
     */
    private function getTotalPagesReadWithCategoryFilter(int $userId, Carbon $startDate, Carbon $endDate, UserTask $userTask): int
    {
        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);

        $query = $this->applyCategoryFilter($query, $userTask);

        $sum = $query->sum('pages_read') ?? 0;
        return (int)$sum;
    }

    /**
     * Get total reading minutes in date range with optional category filtering.
     */
    private function getTotalReadingMinutesWithCategoryFilter(int $userId, Carbon $startDate, Carbon $endDate, UserTask $userTask): int
    {
        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);

        $query = $this->applyCategoryFilter($query, $userTask);

        return (int) $query->sum('reading_duration') ?? 0;
    }

    /**
     * Get reading days count in date range with optional category filtering.
     */
    private function getReadingDaysCountWithCategoryFilter(int $userId, Carbon $startDate, Carbon $endDate, UserTask $userTask): int
    {
        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);

        $query = $this->applyCategoryFilter($query, $userTask);

        return $query->selectRaw('DATE(log_date) as date_only')
            ->distinct()
            ->count('date_only');
    }

    /**
     * Get current reading streak in date range with optional category filtering.
     */
    private function getCurrentReadingStreakWithCategoryFilter(int $userId, Carbon $startDate, Carbon $endDate, UserTask $userTask): int
    {
        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')]);

        $query = $this->applyCategoryFilter($query, $userTask);

        $readingDates = $query->distinct('log_date')
            ->orderBy('log_date')
            ->pluck('log_date')
            ->map(fn($date) => Carbon::parse($date))
            ->toArray();

        if (empty($readingDates)) {
            return 0;
        }

        $streak = 1;
        $maxStreak = 1;

        for ($i = 1; $i < count($readingDates); $i++) {
            if ($readingDates[$i]->diffInDays($readingDates[$i - 1]) === 1) {
                $streak++;
                $maxStreak = max($maxStreak, $streak);
            } else {
                $streak = 1;
            }
        }

        return $maxStreak;
    }

    /**
     * Get total reading minutes in date range.
     */
    private function getTotalReadingMinutes(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        $sum = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('reading_duration') ?? 0;
        return (int)$sum;
    }

    // ========== READ_PAGES CALCULATIONS ==========

    /**
     * READ_PAGES + TOTAL: Total pages read since start date / Total pages target.
     */
    private function calculateReadPagesTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $current = $this->getTotalPagesReadWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult((int) $current, $target);
    }

    /**
     * READ_PAGES + DAILY: Days with daily target pages completed / Total days in range.
     */
    private function calculateReadPagesDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $query = UserReadingLog::where('user_id', $userId)
                ->whereDate('log_date', $currentDate->format('Y-m-d'));

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $dailyPages = $query->sum('pages_read') ?? 0;

            if ($dailyPages >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * READ_PAGES + WEEKLY: Weeks with weekly target pages completed / Total weeks in range.
     */
    private function calculateReadPagesWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $weeklyPages = $query->sum('pages_read') ?? 0;

            if ($weeklyPages >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * READ_PAGES + MONTHLY: Months with monthly target pages completed / Total months in range.
     */
    private function calculateReadPagesMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $monthlyPages = $query->sum('pages_read') ?? 0;

            if ($monthlyPages >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== READ_BOOKS CALCULATIONS ==========

    /**
     * READ_BOOKS + TOTAL: Total books completed since start date / Total books target.
     */
    private function calculateReadBooksTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $current = $this->getTotalBooksCompletedWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult( (int) $current, $target);
    }

    /**
     * READ_BOOKS + DAILY: Days with daily target books completed / Total days in range.
     */
    private function calculateReadBooksDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $query = UserReadingLog::where('user_id', $userId)
                ->whereDate('log_date', $currentDate->format('Y-m-d'))
                ->where('book_completed', true);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $dailyBooks = $query->distinct('book_id')->count();

            if ($dailyBooks >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * READ_BOOKS + WEEKLY: Weeks with weekly target books completed / Total weeks in range.
     */
    private function calculateReadBooksWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->where('book_completed', true);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $weeklyBooks = $query->distinct('book_id')->count();

            if ($weeklyBooks >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * READ_BOOKS + MONTHLY: Months with monthly target books completed / Total months in range.
     */
    private function calculateReadBooksMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                ->where('book_completed', true);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $monthlyBooks = $query->distinct('book_id')->count();

            if ($monthlyBooks >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== READ_MINUTES CALCULATIONS ==========

    /**
     * READ_MINUTES + TOTAL: Total minutes read since start date / Total minutes target.
     */
    private function calculateReadMinutesTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $current = $this->getTotalReadingMinutesWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult($current, $target);
    }

    /**
     * READ_MINUTES + DAILY: Days with daily target minutes completed / Total days in range.
     */
    private function calculateReadMinutesDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalDays = (int) $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $query = UserReadingLog::where('user_id', $userId)
                ->whereDate('log_date', $currentDate->format('Y-m-d'));

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $dailyMinutes = $query->sum('reading_duration') ?? 0;

            if ($dailyMinutes >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * READ_MINUTES + WEEKLY: Weeks with weekly target minutes completed / Total weeks in range.
     */
    private function calculateReadMinutesWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $weeklyMinutes = $query->sum('reading_duration') ?? 0;

            if ($weeklyMinutes >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * READ_MINUTES + MONTHLY: Months with monthly target minutes completed / Total months in range.
     */
    private function calculateReadMinutesMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $monthlyMinutes = $query->sum('reading_duration') ?? 0;

            if ($monthlyMinutes >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== READ_DAYS CALCULATIONS ==========

    /**
     * READ_DAYS + TOTAL: Days with reading activity since start date / Total days target.
     */
    private function calculateReadDaysTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $current = $this->getReadingDaysCountWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult($current, $target);
    }

    /**
     * READ_DAYS + DAILY: Days with reading activity since start date / Total days in range.
     */
    private function calculateReadDaysDaily(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $current = $this->getReadingDaysCountWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult($current, (int) $totalDays, $current, (int) $totalDays);
    }

    /**
     * READ_DAYS + WEEKLY: Weeks with weekly target reading days completed / Total weeks in range.
     */
    private function calculateReadDaysWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $weeklyReadingDays = $query->distinct('log_date')->count();

            if ($weeklyReadingDays >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * READ_DAYS + MONTHLY: Months with monthly target reading days completed / Total months in range.
     */
    private function calculateReadDaysMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')]);

            // Apply category filtering if task has categories
            $query = $this->applyCategoryFilter($query, $userTask);

            $monthlyReadingDays = $query->distinct('log_date')->count();

            if ($monthlyReadingDays >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== READ_STREAK CALCULATIONS ==========

    /**
     * READ_STREAK + TOTAL: Current consecutive reading days from start date / Target consecutive days.
     */
    private function calculateReadStreakTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $current = $this->getCurrentReadingStreakWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult($current, $target);
    }

    /**
     * READ_STREAK + DAILY: Days with reading activity since start date / Total days in range.
     */
    private function calculateReadStreakDaily(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $current = $this->getReadingDaysCountWithCategoryFilter($userId, $startDate, $endDate, $userTask);
        return $this->createProgressResult($current, (int) $totalDays, $current, (int) $totalDays);
    }

    /**
     * READ_STREAK + WEEKLY: Weeks with weekly target consecutive reading days / Total weeks.
     */
    private function calculateReadStreakWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $weeklyStreak = $this->getCurrentReadingStreakWithCategoryFilter($userId, $currentWeekStart, $weekEnd, $userTask);
            if ($weeklyStreak >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * READ_STREAK + MONTHLY: Months with monthly target consecutive reading days / Total months.
     */
    private function calculateReadStreakMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $monthlyStreak = $this->getCurrentReadingStreakWithCategoryFilter($userId, $currentMonthStart, $monthEnd, $userTask);
            if ($monthlyStreak >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    /**
     * Get current reading streak in date range.
     */
    private function getCurrentReadingStreak(int $userId, Carbon $startDate, Carbon $endDate): int
    {
        $readingDates = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->selectRaw('DATE(log_date) as date_only')
            ->distinct()
            ->orderBy('date_only')
            ->pluck('date_only')
            ->map(fn($date) => Carbon::parse($date))
            ->toArray();

        if (empty($readingDates)) {
            return 0;
        }

        $maxStreak = 1;
        $currentStreak = 1;

        for ($i = 1; $i < count($readingDates); $i++) {
            if ($readingDates[$i]->diffInDays($readingDates[$i - 1]) === 1) {
                $currentStreak++;
                $maxStreak = max($maxStreak, $currentStreak);
            } else {
                $currentStreak = 1;
            }
        }

        return $maxStreak;
    }

    // ========== EARN_READING_POINTS CALCULATIONS ==========

    /**
     * EARN_READING_POINTS + TOTAL: Total reading points earned since start date / Target points.
     */
    private function calculateEarnReadingPointsTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate): array
    {
        $current = UserPoint::where('user_id', $userId)
            ->where('point_type', UserPoint::POINT_TYPE_PAGE)
            ->whereBetween('point_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('points') ?? 0;

        return $this->createProgressResult( (int) $current, $target);
    }

    /**
     * EARN_READING_POINTS + DAILY: Days with daily target reading points / Total days.
     */
    private function calculateEarnReadingPointsDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $dailyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->whereDate('point_date', $currentDate->format('Y-m-d'))
                ->sum('points') ?? 0;

            if ($dailyPoints >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * EARN_READING_POINTS + WEEKLY: Weeks with weekly target reading points / Total weeks.
     */
    private function calculateEarnReadingPointsWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $weeklyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->whereBetween('point_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('points') ?? 0;

            if ($weeklyPoints >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * EARN_READING_POINTS + MONTHLY: Months with monthly target reading points / Total months.
     */
    private function calculateEarnReadingPointsMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $monthlyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_PAGE)
                ->whereBetween('point_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                ->sum('points') ?? 0;

            if ($monthlyPoints >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== EARN_ACTIVITY_POINTS CALCULATIONS ==========

    /**
     * EARN_ACTIVITY_POINTS + TOTAL: Total activity points earned since start date / Target points.
     */
    private function calculateEarnActivityPointsTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate): array
    {
        $current = UserPoint::where('user_id', $userId)
            ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
            ->whereBetween('point_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('points') ?? 0;

        return $this->createProgressResult($current, $target);
    }

    /**
     * EARN_ACTIVITY_POINTS + DAILY: Days with daily target activity points / Total days.
     */
    private function calculateEarnActivityPointsDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $dailyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
                ->whereDate('point_date', $currentDate->format('Y-m-d'))
                ->sum('points') ?? 0;

            if ($dailyPoints >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * EARN_ACTIVITY_POINTS + WEEKLY: Weeks with weekly target activity points / Total weeks.
     */
    private function calculateEarnActivityPointsWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $weeklyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
                ->whereBetween('point_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->sum('points') ?? 0;

            if ($weeklyPoints >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * EARN_ACTIVITY_POINTS + MONTHLY: Months with monthly target activity points / Total months.
     */
    private function calculateEarnActivityPointsMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate): array
    {
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $monthlyPoints = UserPoint::where('user_id', $userId)
                ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY)
                ->whereBetween('point_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                ->sum('points') ?? 0;

            if ($monthlyPoints >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== COMPLETE_BOOK_ACTIVITY CALCULATIONS ==========

    /**
     * COMPLETE_BOOK_ACTIVITY + TOTAL: Total activities completed since start date / Target activities.
     */
    private function calculateCompleteBookActivityTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $activityId = $task->activity_id;

        $query = UserActivity::where('user_id', $userId)
            ->whereBetween('activity_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->whereIn('status', [UserActivity::STATUS_APPROVED, UserActivity::STATUS_COMPLETED]);

        if ($activityId) {
            $query->where('activity_id', $activityId);
        }

        $current = $query->count();
        return $this->createProgressResult($current, $target);
    }

    /**
     * COMPLETE_BOOK_ACTIVITY + DAILY: Days with daily target activities completed / Total days.
     */
    private function calculateCompleteBookActivityDaily(int $userId, int $dailyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $activityId = $task->activity_id;
        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $query = UserActivity::where('user_id', $userId)
                ->whereDate('activity_date', $currentDate->format('Y-m-d'))
                ->whereIn('status', [UserActivity::STATUS_APPROVED, UserActivity::STATUS_COMPLETED]);

            if ($activityId) {
                $query->where('activity_id', $activityId);
            }

            $dailyActivities = $query->count();
            if ($dailyActivities >= $dailyTarget) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * COMPLETE_BOOK_ACTIVITY + WEEKLY: Weeks with weekly target activities completed / Total weeks.
     */
    private function calculateCompleteBookActivityWeekly(int $userId, int $weeklyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $activityId = $task->activity_id;
        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $query = UserActivity::where('user_id', $userId)
                ->whereBetween('activity_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                ->whereIn('status', [UserActivity::STATUS_APPROVED, UserActivity::STATUS_COMPLETED]);

            if ($activityId) {
                $query->where('activity_id', $activityId);
            }

            $weeklyActivities = $query->count();
            if ($weeklyActivities >= $weeklyTarget) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * COMPLETE_BOOK_ACTIVITY + MONTHLY: Months with monthly target activities completed / Total months.
     */
    private function calculateCompleteBookActivityMonthly(int $userId, int $monthlyTarget, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $activityId = $task->activity_id;
        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $query = UserActivity::where('user_id', $userId)
                ->whereBetween('activity_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                ->whereIn('status', [UserActivity::STATUS_APPROVED, UserActivity::STATUS_COMPLETED]);

            if ($activityId) {
                $query->where('activity_id', $activityId);
            }

            $monthlyActivities = $query->count();
            if ($monthlyActivities >= $monthlyTarget) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    // ========== COMPLETE_BOOK_LIST CALCULATIONS ==========

    /**
     * COMPLETE_BOOK_LIST + TOTAL: Books from list completed since start date / Total books in list.
     */
    private function calculateCompleteBookListTotal(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $taskBooks = $task->books()->pluck('books.id')->toArray();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        // If task has specific books, use those; otherwise use category filtering
        if (!empty($taskBooks)) {
            $query = UserReadingLog::where('user_id', $userId)
                ->whereIn('book_id', $taskBooks)
                ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->where('book_completed', true);

            $completedBooks = $query->distinct('book_id')->count();
            return $this->createProgressResult($completedBooks, count($taskBooks));
        } elseif (!empty($taskCategories)) {
            // Use category filtering when no specific books are assigned
            $query = UserReadingLog::where('user_id', $userId)
                ->whereBetween('log_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->where('book_completed', true)
                ->whereHas('book.categories', function ($q) use ($taskCategories) {
                    $q->whereIn('categories.id', $taskCategories);
                });

            $completedBooks = $query->distinct('book_id')->count();
            // For category-based tasks, we can't determine a fixed target, so use the target parameter
            return $this->createProgressResult($completedBooks, $target);
        }

        return $this->getDefaultProgress();
    }

    /**
     * COMPLETE_BOOK_LIST + DAILY: Days with listed books completed daily / Total days.
     */
    private function calculateCompleteBookListDaily(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $taskBooks = $task->books()->pluck('books.id')->toArray();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        $totalDays = $startDate->diffInDays($endDate) + 1;
        $completedDays = 0;

        $currentDate = $startDate->copy();
        while ($currentDate->lte($endDate)) {
            $dailyCompletedBooks = 0;

            if (!empty($taskBooks)) {
                // Use specific book list
                $dailyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereIn('book_id', $taskBooks)
                    ->whereDate('log_date', $currentDate->format('Y-m-d'))
                    ->where('book_completed', true)
                    ->distinct('book_id')
                    ->count();
            } elseif (!empty($taskCategories)) {
                // Use category filtering
                $dailyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereDate('log_date', $currentDate->format('Y-m-d'))
                    ->where('book_completed', true)
                    ->whereHas('book.categories', function ($q) use ($taskCategories) {
                        $q->whereIn('categories.id', $taskCategories);
                    })
                    ->distinct('book_id')
                    ->count();
            }

            if ($dailyCompletedBooks > 0) {
                $completedDays++;
            }

            $currentDate->addDay();
        }

        return $this->createProgressResult($completedDays, (int) $totalDays, $completedDays, (int) $totalDays);
    }

    /**
     * COMPLETE_BOOK_LIST + WEEKLY: Weeks with listed books completed weekly / Total weeks.
     */
    private function calculateCompleteBookListWeekly(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $taskBooks = $task->books()->pluck('books.id')->toArray();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        $totalWeeks = $startDate->diffInWeeks($endDate) + 1;
        $completedWeeks = 0;

        $currentWeekStart = $startDate->copy()->startOfWeek();
        for ($week = 0; $week < $totalWeeks; $week++) {
            $weekEnd = $currentWeekStart->copy()->endOfWeek();
            if ($weekEnd->gt($endDate)) {
                $weekEnd = $endDate->copy();
            }

            $weeklyCompletedBooks = 0;

            if (!empty($taskBooks)) {
                // Use specific book list
                $weeklyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereIn('book_id', $taskBooks)
                    ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->where('book_completed', true)
                    ->distinct('book_id')
                    ->count();
            } elseif (!empty($taskCategories)) {
                // Use category filtering
                $weeklyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereBetween('log_date', [$currentWeekStart->format('Y-m-d'), $weekEnd->format('Y-m-d')])
                    ->where('book_completed', true)
                    ->whereHas('book.categories', function ($q) use ($taskCategories) {
                        $q->whereIn('categories.id', $taskCategories);
                    })
                    ->distinct('book_id')
                    ->count();
            }

            if ($weeklyCompletedBooks > 0) {
                $completedWeeks++;
            }

            $currentWeekStart->addWeek();
        }

        return $this->createProgressResult($completedWeeks, (int) $totalWeeks, $completedWeeks, (int) $totalWeeks);
    }

    /**
     * COMPLETE_BOOK_LIST + MONTHLY: Months with listed books completed monthly / Total months.
     */
    private function calculateCompleteBookListMonthly(int $userId, int $target, Carbon $startDate, Carbon $endDate, UserTask $userTask): array
    {
        $task = $userTask->getActualTask();
        $taskBooks = $task->books()->pluck('books.id')->toArray();
        $taskCategories = $task->categories()->pluck('categories.id')->toArray();

        $totalMonths = $startDate->diffInMonths($endDate) + 1;
        $completedMonths = 0;

        $currentMonthStart = $startDate->copy()->startOfMonth();
        for ($month = 0; $month < $totalMonths; $month++) {
            $monthEnd = $currentMonthStart->copy()->endOfMonth();
            if ($monthEnd->gt($endDate)) {
                $monthEnd = $endDate->copy();
            }

            $monthlyCompletedBooks = 0;

            if (!empty($taskBooks)) {
                // Use specific book list
                $monthlyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereIn('book_id', $taskBooks)
                    ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                    ->where('book_completed', true)
                    ->distinct('book_id')
                    ->count();
            } elseif (!empty($taskCategories)) {
                // Use category filtering
                $monthlyCompletedBooks = UserReadingLog::where('user_id', $userId)
                    ->whereBetween('log_date', [$currentMonthStart->format('Y-m-d'), $monthEnd->format('Y-m-d')])
                    ->where('book_completed', true)
                    ->whereHas('book.categories', function ($q) use ($taskCategories) {
                        $q->whereIn('categories.id', $taskCategories);
                    })
                    ->distinct('book_id')
                    ->count();
            }

            if ($monthlyCompletedBooks > 0) {
                $completedMonths++;
            }

            $currentMonthStart->addMonth();
        }

        return $this->createProgressResult($completedMonths, (int) $totalMonths, $completedMonths, (int) $totalMonths);
    }

    /**
     * Check if a user task should be automatically marked as completed based on progress.
     */
    public function shouldMarkAsCompleted(UserTask $userTask): bool
    {
        if ($userTask->completed) {
            return false;
        }

        $progress = $this->calculateProgress($userTask);
        return $progress['completed'] ?? false;
    }

    /**
     * Update user task completion status based on calculated progress.
     */
    public function updateTaskCompletion(UserTask $userTask): bool
    {
        if ($this->shouldMarkAsCompleted($userTask)) {
            $userTask->update([
                'completed' => true,
                'complete_date' => Carbon::now(),
            ]);
            return true;
        }

        return false;
    }
}
