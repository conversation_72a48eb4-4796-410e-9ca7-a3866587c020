var Qi=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports);var v0=Qi((M0,Gt)=>{function ai(r,e){return function(){return r.apply(e,arguments)}}const{toString:Ji}=Object.prototype,{getPrototypeOf:zr}=Object,{iterator:ar,toStringTag:oi}=Symbol,or=(r=>e=>{const t=Ji.call(e);return r[t]||(r[t]=t.slice(8,-1).toLowerCase())})(Object.create(null)),et=r=>(r=r.toLowerCase(),e=>or(e)===r),sr=r=>e=>typeof e===r,{isArray:It}=Array,mt=sr("undefined");function Lt(r){return r!==null&&!mt(r)&&r.constructor!==null&&!mt(r.constructor)&&Ge(r.constructor.isBuffer)&&r.constructor.isBuffer(r)}const si=et("ArrayBuffer");function ea(r){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(r):e=r&&r.buffer&&si(r.buffer),e}const ta=sr("string"),Ge=sr("function"),fi=sr("number"),kt=r=>r!==null&&typeof r=="object",ra=r=>r===!0||r===!1,Kt=r=>{if(or(r)!=="object")return!1;const e=zr(r);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(oi in r)&&!(ar in r)},na=r=>{if(!kt(r)||Lt(r))return!1;try{return Object.keys(r).length===0&&Object.getPrototypeOf(r)===Object.prototype}catch{return!1}},ia=et("Date"),aa=et("File"),oa=et("Blob"),sa=et("FileList"),fa=r=>kt(r)&&Ge(r.pipe),ua=r=>{let e;return r&&(typeof FormData=="function"&&r instanceof FormData||Ge(r.append)&&((e=or(r))==="formdata"||e==="object"&&Ge(r.toString)&&r.toString()==="[object FormData]"))},ca=et("URLSearchParams"),[la,ha,da,va]=["ReadableStream","Request","Response","Headers"].map(et),pa=r=>r.trim?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ut(r,e,{allOwnKeys:t=!1}={}){if(r===null||typeof r>"u")return;let n,i;if(typeof r!="object"&&(r=[r]),It(r))for(n=0,i=r.length;n<i;n++)e.call(null,r[n],n,r);else{if(Lt(r))return;const a=t?Object.getOwnPropertyNames(r):Object.keys(r),o=a.length;let s;for(n=0;n<o;n++)s=a[n],e.call(null,r[s],s,r)}}function ui(r,e){if(Lt(r))return null;e=e.toLowerCase();const t=Object.keys(r);let n=t.length,i;for(;n-- >0;)if(i=t[n],e===i.toLowerCase())return i;return null}const gt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ci=r=>!mt(r)&&r!==gt;function Ir(){const{caseless:r,skipUndefined:e}=ci(this)&&this||{},t={},n=(i,a)=>{const o=r&&ui(t,a)||a;Kt(t[o])&&Kt(i)?t[o]=Ir(t[o],i):Kt(i)?t[o]=Ir({},i):It(i)?t[o]=i.slice():(!e||!mt(i))&&(t[o]=i)};for(let i=0,a=arguments.length;i<a;i++)arguments[i]&&Ut(arguments[i],n);return t}const ga=(r,e,t,{allOwnKeys:n}={})=>(Ut(e,(i,a)=>{t&&Ge(i)?r[a]=ai(i,t):r[a]=i},{allOwnKeys:n}),r),xa=r=>(r.charCodeAt(0)===65279&&(r=r.slice(1)),r),ya=(r,e,t,n)=>{r.prototype=Object.create(e.prototype,n),r.prototype.constructor=r,Object.defineProperty(r,"super",{value:e.prototype}),t&&Object.assign(r.prototype,t)},wa=(r,e,t,n)=>{let i,a,o;const s={};if(e=e||{},r==null)return e;do{for(i=Object.getOwnPropertyNames(r),a=i.length;a-- >0;)o=i[a],(!n||n(o,r,e))&&!s[o]&&(e[o]=r[o],s[o]=!0);r=t!==!1&&zr(r)}while(r&&(!t||t(r,e))&&r!==Object.prototype);return e},_a=(r,e,t)=>{r=String(r),(t===void 0||t>r.length)&&(t=r.length),t-=e.length;const n=r.indexOf(e,t);return n!==-1&&n===t},Aa=r=>{if(!r)return null;if(It(r))return r;let e=r.length;if(!fi(e))return null;const t=new Array(e);for(;e-- >0;)t[e]=r[e];return t},Ea=(r=>e=>r&&e instanceof r)(typeof Uint8Array<"u"&&zr(Uint8Array)),Ca=(r,e)=>{const n=(r&&r[ar]).call(r);let i;for(;(i=n.next())&&!i.done;){const a=i.value;e.call(r,a[0],a[1])}},ma=(r,e)=>{let t;const n=[];for(;(t=r.exec(e))!==null;)n.push(t);return n},Sa=et("HTMLFormElement"),Ia=r=>r.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,n,i){return n.toUpperCase()+i}),sn=(({hasOwnProperty:r})=>(e,t)=>r.call(e,t))(Object.prototype),Oa=et("RegExp"),li=(r,e)=>{const t=Object.getOwnPropertyDescriptors(r),n={};Ut(t,(i,a)=>{let o;(o=e(i,a,r))!==!1&&(n[a]=o||i)}),Object.defineProperties(r,n)},ba=r=>{li(r,(e,t)=>{if(Ge(r)&&["arguments","caller","callee"].indexOf(t)!==-1)return!1;const n=r[t];if(Ge(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+t+"'")})}})},Ta=(r,e)=>{const t={},n=i=>{i.forEach(a=>{t[a]=!0})};return It(r)?n(r):n(String(r).split(e)),t},Ra=()=>{},Da=(r,e)=>r!=null&&Number.isFinite(r=+r)?r:e;function Na(r){return!!(r&&Ge(r.append)&&r[oi]==="FormData"&&r[ar])}const Pa=r=>{const e=new Array(10),t=(n,i)=>{if(kt(n)){if(e.indexOf(n)>=0)return;if(Lt(n))return n;if(!("toJSON"in n)){e[i]=n;const a=It(n)?[]:{};return Ut(n,(o,s)=>{const f=t(o,i+1);!mt(f)&&(a[s]=f)}),e[i]=void 0,a}}return n};return t(r,0)},Ma=et("AsyncFunction"),Ba=r=>r&&(kt(r)||Ge(r))&&Ge(r.then)&&Ge(r.catch),hi=((r,e)=>r?setImmediate:e?((t,n)=>(gt.addEventListener("message",({source:i,data:a})=>{i===gt&&a===t&&n.length&&n.shift()()},!1),i=>{n.push(i),gt.postMessage(t,"*")}))(`axios@${Math.random()}`,[]):t=>setTimeout(t))(typeof setImmediate=="function",Ge(gt.postMessage)),Fa=typeof queueMicrotask<"u"?queueMicrotask.bind(gt):typeof process<"u"&&process.nextTick||hi,La=r=>r!=null&&Ge(r[ar]),m={isArray:It,isArrayBuffer:si,isBuffer:Lt,isFormData:ua,isArrayBufferView:ea,isString:ta,isNumber:fi,isBoolean:ra,isObject:kt,isPlainObject:Kt,isEmptyObject:na,isReadableStream:la,isRequest:ha,isResponse:da,isHeaders:va,isUndefined:mt,isDate:ia,isFile:aa,isBlob:oa,isRegExp:Oa,isFunction:Ge,isStream:fa,isURLSearchParams:ca,isTypedArray:Ea,isFileList:sa,forEach:Ut,merge:Ir,extend:ga,trim:pa,stripBOM:xa,inherits:ya,toFlatObject:wa,kindOf:or,kindOfTest:et,endsWith:_a,toArray:Aa,forEachEntry:Ca,matchAll:ma,isHTMLForm:Sa,hasOwnProperty:sn,hasOwnProp:sn,reduceDescriptors:li,freezeMethods:ba,toObjectSet:Ta,toCamelCase:Ia,noop:Ra,toFiniteNumber:Da,findKey:ui,global:gt,isContextDefined:ci,isSpecCompliantForm:Na,toJSONObject:Pa,isAsyncFn:Ma,isThenable:Ba,setImmediate:hi,asap:Fa,isIterable:La};function V(r,e,t,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=r,this.name="AxiosError",e&&(this.code=e),t&&(this.config=t),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}m.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:m.toJSONObject(this.config),code:this.code,status:this.status}}});const di=V.prototype,vi={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(r=>{vi[r]={value:r}});Object.defineProperties(V,vi);Object.defineProperty(di,"isAxiosError",{value:!0});V.from=(r,e,t,n,i,a)=>{const o=Object.create(di);m.toFlatObject(r,o,function(c){return c!==Error.prototype},u=>u!=="isAxiosError");const s=r&&r.message?r.message:"Error",f=e==null&&r?r.code:e;return V.call(o,s,f,t,n,i),r&&o.cause==null&&Object.defineProperty(o,"cause",{value:r,configurable:!0}),o.name=r&&r.name||"Error",a&&Object.assign(o,a),o};const ka=null;function Or(r){return m.isPlainObject(r)||m.isArray(r)}function pi(r){return m.endsWith(r,"[]")?r.slice(0,-2):r}function fn(r,e,t){return r?r.concat(e).map(function(i,a){return i=pi(i),!t&&a?"["+i+"]":i}).join(t?".":""):e}function Ua(r){return m.isArray(r)&&!r.some(Or)}const Ha=m.toFlatObject(m,{},null,function(e){return/^is[A-Z]/.test(e)});function fr(r,e,t){if(!m.isObject(r))throw new TypeError("target must be an object");e=e||new FormData,t=m.toFlatObject(t,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,x){return!m.isUndefined(x[p])});const n=t.metaTokens,i=t.visitor||c,a=t.dots,o=t.indexes,f=(t.Blob||typeof Blob<"u"&&Blob)&&m.isSpecCompliantForm(e);if(!m.isFunction(i))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(m.isDate(v))return v.toISOString();if(m.isBoolean(v))return v.toString();if(!f&&m.isBlob(v))throw new V("Blob is not supported. Use a Buffer instead.");return m.isArrayBuffer(v)||m.isTypedArray(v)?f&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,p,x){let y=v;if(v&&!x&&typeof v=="object"){if(m.endsWith(p,"{}"))p=n?p:p.slice(0,-2),v=JSON.stringify(v);else if(m.isArray(v)&&Ua(v)||(m.isFileList(v)||m.endsWith(p,"[]"))&&(y=m.toArray(v)))return p=pi(p),y.forEach(function(A,C){!(m.isUndefined(A)||A===null)&&e.append(o===!0?fn([p],C,a):o===null?p:p+"[]",u(A))}),!1}return Or(v)?!0:(e.append(fn(x,p,a),u(v)),!1)}const l=[],h=Object.assign(Ha,{defaultVisitor:c,convertValue:u,isVisitable:Or});function d(v,p){if(!m.isUndefined(v)){if(l.indexOf(v)!==-1)throw Error("Circular reference detected in "+p.join("."));l.push(v),m.forEach(v,function(y,w){(!(m.isUndefined(y)||y===null)&&i.call(e,y,m.isString(w)?w.trim():w,p,h))===!0&&d(y,p?p.concat(w):[w])}),l.pop()}}if(!m.isObject(r))throw new TypeError("data must be an object");return d(r),e}function un(r){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(r).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function jr(r,e){this._pairs=[],r&&fr(r,this,e)}const gi=jr.prototype;gi.append=function(e,t){this._pairs.push([e,t])};gi.toString=function(e){const t=e?function(n){return e.call(this,n,un)}:un;return this._pairs.map(function(i){return t(i[0])+"="+t(i[1])},"").join("&")};function Va(r){return encodeURIComponent(r).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+")}function xi(r,e,t){if(!e)return r;const n=t&&t.encode||Va;m.isFunction(t)&&(t={serialize:t});const i=t&&t.serialize;let a;if(i?a=i(e,t):a=m.isURLSearchParams(e)?e.toString():new jr(e,t).toString(n),a){const o=r.indexOf("#");o!==-1&&(r=r.slice(0,o)),r+=(r.indexOf("?")===-1?"?":"&")+a}return r}class cn{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){m.forEach(this.handlers,function(n){n!==null&&e(n)})}}const yi={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ga=typeof URLSearchParams<"u"?URLSearchParams:jr,Wa=typeof FormData<"u"?FormData:null,Xa=typeof Blob<"u"?Blob:null,za={isBrowser:!0,classes:{URLSearchParams:Ga,FormData:Wa,Blob:Xa},protocols:["http","https","file","blob","url","data"]},Yr=typeof window<"u"&&typeof document<"u",br=typeof navigator=="object"&&navigator||void 0,ja=Yr&&(!br||["ReactNative","NativeScript","NS"].indexOf(br.product)<0),Ya=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",$a=Yr&&window.location.href||"http://localhost",Za=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Yr,hasStandardBrowserEnv:ja,hasStandardBrowserWebWorkerEnv:Ya,navigator:br,origin:$a},Symbol.toStringTag,{value:"Module"})),Ne={...Za,...za};function Ka(r,e){return fr(r,new Ne.classes.URLSearchParams,{visitor:function(t,n,i,a){return Ne.isNode&&m.isBuffer(t)?(this.append(n,t.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)},...e})}function qa(r){return m.matchAll(/\w+|\[(\w*)]/g,r).map(e=>e[0]==="[]"?"":e[1]||e[0])}function Qa(r){const e={},t=Object.keys(r);let n;const i=t.length;let a;for(n=0;n<i;n++)a=t[n],e[a]=r[a];return e}function wi(r){function e(t,n,i,a){let o=t[a++];if(o==="__proto__")return!0;const s=Number.isFinite(+o),f=a>=t.length;return o=!o&&m.isArray(i)?i.length:o,f?(m.hasOwnProp(i,o)?i[o]=[i[o],n]:i[o]=n,!s):((!i[o]||!m.isObject(i[o]))&&(i[o]=[]),e(t,n,i[o],a)&&m.isArray(i[o])&&(i[o]=Qa(i[o])),!s)}if(m.isFormData(r)&&m.isFunction(r.entries)){const t={};return m.forEachEntry(r,(n,i)=>{e(qa(n),i,t,0)}),t}return null}function Ja(r,e,t){if(m.isString(r))try{return(e||JSON.parse)(r),m.trim(r)}catch(n){if(n.name!=="SyntaxError")throw n}return(t||JSON.stringify)(r)}const Ht={transitional:yi,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",i=n.indexOf("application/json")>-1,a=m.isObject(e);if(a&&m.isHTMLForm(e)&&(e=new FormData(e)),m.isFormData(e))return i?JSON.stringify(wi(e)):e;if(m.isArrayBuffer(e)||m.isBuffer(e)||m.isStream(e)||m.isFile(e)||m.isBlob(e)||m.isReadableStream(e))return e;if(m.isArrayBufferView(e))return e.buffer;if(m.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Ka(e,this.formSerializer).toString();if((s=m.isFileList(e))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return fr(s?{"files[]":e}:e,f&&new f,this.formSerializer)}}return a||i?(t.setContentType("application/json",!1),Ja(e)):e}],transformResponse:[function(e){const t=this.transitional||Ht.transitional,n=t&&t.forcedJSONParsing,i=this.responseType==="json";if(m.isResponse(e)||m.isReadableStream(e))return e;if(e&&m.isString(e)&&(n&&!this.responseType||i)){const o=!(t&&t.silentJSONParsing)&&i;try{return JSON.parse(e,this.parseReviver)}catch(s){if(o)throw s.name==="SyntaxError"?V.from(s,V.ERR_BAD_RESPONSE,this,null,this.response):s}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ne.classes.FormData,Blob:Ne.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};m.forEach(["delete","get","head","post","put","patch"],r=>{Ht.headers[r]={}});const eo=m.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),to=r=>{const e={};let t,n,i;return r&&r.split(`
`).forEach(function(o){i=o.indexOf(":"),t=o.substring(0,i).trim().toLowerCase(),n=o.substring(i+1).trim(),!(!t||e[t]&&eo[t])&&(t==="set-cookie"?e[t]?e[t].push(n):e[t]=[n]:e[t]=e[t]?e[t]+", "+n:n)}),e},ln=Symbol("internals");function Tt(r){return r&&String(r).trim().toLowerCase()}function qt(r){return r===!1||r==null?r:m.isArray(r)?r.map(qt):String(r)}function ro(r){const e=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=t.exec(r);)e[n[1]]=n[2];return e}const no=r=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(r.trim());function vr(r,e,t,n,i){if(m.isFunction(n))return n.call(this,e,t);if(i&&(e=t),!!m.isString(e)){if(m.isString(n))return e.indexOf(n)!==-1;if(m.isRegExp(n))return n.test(e)}}function io(r){return r.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}function ao(r,e){const t=m.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(r,n+t,{value:function(i,a,o){return this[n].call(this,e,i,a,o)},configurable:!0})})}let We=class{constructor(e){e&&this.set(e)}set(e,t,n){const i=this;function a(s,f,u){const c=Tt(f);if(!c)throw new Error("header name must be a non-empty string");const l=m.findKey(i,c);(!l||i[l]===void 0||u===!0||u===void 0&&i[l]!==!1)&&(i[l||f]=qt(s))}const o=(s,f)=>m.forEach(s,(u,c)=>a(u,c,f));if(m.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(m.isString(e)&&(e=e.trim())&&!no(e))o(to(e),t);else if(m.isObject(e)&&m.isIterable(e)){let s={},f,u;for(const c of e){if(!m.isArray(c))throw TypeError("Object iterator must return a key-value pair");s[u=c[0]]=(f=s[u])?m.isArray(f)?[...f,c[1]]:[f,c[1]]:c[1]}o(s,t)}else e!=null&&a(t,e,n);return this}get(e,t){if(e=Tt(e),e){const n=m.findKey(this,e);if(n){const i=this[n];if(!t)return i;if(t===!0)return ro(i);if(m.isFunction(t))return t.call(this,i,n);if(m.isRegExp(t))return t.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Tt(e),e){const n=m.findKey(this,e);return!!(n&&this[n]!==void 0&&(!t||vr(this,this[n],n,t)))}return!1}delete(e,t){const n=this;let i=!1;function a(o){if(o=Tt(o),o){const s=m.findKey(n,o);s&&(!t||vr(n,n[s],s,t))&&(delete n[s],i=!0)}}return m.isArray(e)?e.forEach(a):a(e),i}clear(e){const t=Object.keys(this);let n=t.length,i=!1;for(;n--;){const a=t[n];(!e||vr(this,this[a],a,e,!0))&&(delete this[a],i=!0)}return i}normalize(e){const t=this,n={};return m.forEach(this,(i,a)=>{const o=m.findKey(n,a);if(o){t[o]=qt(i),delete t[a];return}const s=e?io(a):String(a).trim();s!==a&&delete t[a],t[s]=qt(i),n[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return m.forEach(this,(n,i)=>{n!=null&&n!==!1&&(t[i]=e&&m.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(i=>n.set(i)),n}static accessor(e){const n=(this[ln]=this[ln]={accessors:{}}).accessors,i=this.prototype;function a(o){const s=Tt(o);n[s]||(ao(i,o),n[s]=!0)}return m.isArray(e)?e.forEach(a):a(e),this}};We.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);m.reduceDescriptors(We.prototype,({value:r},e)=>{let t=e[0].toUpperCase()+e.slice(1);return{get:()=>r,set(n){this[t]=n}}});m.freezeMethods(We);function pr(r,e){const t=this||Ht,n=e||t,i=We.from(n.headers);let a=n.data;return m.forEach(r,function(s){a=s.call(t,a,i.normalize(),e?e.status:void 0)}),i.normalize(),a}function _i(r){return!!(r&&r.__CANCEL__)}function Ot(r,e,t){V.call(this,r??"canceled",V.ERR_CANCELED,e,t),this.name="CanceledError"}m.inherits(Ot,V,{__CANCEL__:!0});function Ai(r,e,t){const n=t.config.validateStatus;!t.status||!n||n(t.status)?r(t):e(new V("Request failed with status code "+t.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(t.status/100)-4],t.config,t.request,t))}function oo(r){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(r);return e&&e[1]||""}function so(r,e){r=r||10;const t=new Array(r),n=new Array(r);let i=0,a=0,o;return e=e!==void 0?e:1e3,function(f){const u=Date.now(),c=n[a];o||(o=u),t[i]=f,n[i]=u;let l=a,h=0;for(;l!==i;)h+=t[l++],l=l%r;if(i=(i+1)%r,i===a&&(a=(a+1)%r),u-o<e)return;const d=c&&u-c;return d?Math.round(h*1e3/d):void 0}}function fo(r,e){let t=0,n=1e3/e,i,a;const o=(u,c=Date.now())=>{t=c,i=null,a&&(clearTimeout(a),a=null),r(...u)};return[(...u)=>{const c=Date.now(),l=c-t;l>=n?o(u,c):(i=u,a||(a=setTimeout(()=>{a=null,o(i)},n-l)))},()=>i&&o(i)]}const rr=(r,e,t=3)=>{let n=0;const i=so(50,250);return fo(a=>{const o=a.loaded,s=a.lengthComputable?a.total:void 0,f=o-n,u=i(f),c=o<=s;n=o;const l={loaded:o,total:s,progress:s?o/s:void 0,bytes:f,rate:u||void 0,estimated:u&&s&&c?(s-o)/u:void 0,event:a,lengthComputable:s!=null,[e?"download":"upload"]:!0};r(l)},t)},hn=(r,e)=>{const t=r!=null;return[n=>e[0]({lengthComputable:t,total:r,loaded:n}),e[1]]},dn=r=>(...e)=>m.asap(()=>r(...e)),uo=Ne.hasStandardBrowserEnv?((r,e)=>t=>(t=new URL(t,Ne.origin),r.protocol===t.protocol&&r.host===t.host&&(e||r.port===t.port)))(new URL(Ne.origin),Ne.navigator&&/(msie|trident)/i.test(Ne.navigator.userAgent)):()=>!0,co=Ne.hasStandardBrowserEnv?{write(r,e,t,n,i,a){const o=[r+"="+encodeURIComponent(e)];m.isNumber(t)&&o.push("expires="+new Date(t).toGMTString()),m.isString(n)&&o.push("path="+n),m.isString(i)&&o.push("domain="+i),a===!0&&o.push("secure"),document.cookie=o.join("; ")},read(r){const e=document.cookie.match(new RegExp("(^|;\\s*)("+r+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(r){this.write(r,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function lo(r){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(r)}function ho(r,e){return e?r.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):r}function Ei(r,e,t){let n=!lo(e);return r&&(n||t==!1)?ho(r,e):e}const vn=r=>r instanceof We?{...r}:r;function _t(r,e){e=e||{};const t={};function n(u,c,l,h){return m.isPlainObject(u)&&m.isPlainObject(c)?m.merge.call({caseless:h},u,c):m.isPlainObject(c)?m.merge({},c):m.isArray(c)?c.slice():c}function i(u,c,l,h){if(m.isUndefined(c)){if(!m.isUndefined(u))return n(void 0,u,l,h)}else return n(u,c,l,h)}function a(u,c){if(!m.isUndefined(c))return n(void 0,c)}function o(u,c){if(m.isUndefined(c)){if(!m.isUndefined(u))return n(void 0,u)}else return n(void 0,c)}function s(u,c,l){if(l in e)return n(u,c);if(l in r)return n(void 0,u)}const f={url:a,method:a,data:a,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(u,c,l)=>i(vn(u),vn(c),l,!0)};return m.forEach(Object.keys({...r,...e}),function(c){const l=f[c]||i,h=l(r[c],e[c],c);m.isUndefined(h)&&l!==s||(t[c]=h)}),t}const Ci=r=>{const e=_t({},r);let{data:t,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:a,headers:o,auth:s}=e;if(e.headers=o=We.from(o),e.url=xi(Ei(e.baseURL,e.url,e.allowAbsoluteUrls),r.params,r.paramsSerializer),s&&o.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),m.isFormData(t)){if(Ne.hasStandardBrowserEnv||Ne.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if(m.isFunction(t.getHeaders)){const f=t.getHeaders(),u=["content-type","content-length"];Object.entries(f).forEach(([c,l])=>{u.includes(c.toLowerCase())&&o.set(c,l)})}}if(Ne.hasStandardBrowserEnv&&(n&&m.isFunction(n)&&(n=n(e)),n||n!==!1&&uo(e.url))){const f=i&&a&&co.read(a);f&&o.set(i,f)}return e},vo=typeof XMLHttpRequest<"u",po=vo&&function(r){return new Promise(function(t,n){const i=Ci(r);let a=i.data;const o=We.from(i.headers).normalize();let{responseType:s,onUploadProgress:f,onDownloadProgress:u}=i,c,l,h,d,v;function p(){d&&d(),v&&v(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let x=new XMLHttpRequest;x.open(i.method.toUpperCase(),i.url,!0),x.timeout=i.timeout;function y(){if(!x)return;const A=We.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),O={data:!s||s==="text"||s==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:A,config:r,request:x};Ai(function(T){t(T),p()},function(T){n(T),p()},O),x=null}"onloadend"in x?x.onloadend=y:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(y)},x.onabort=function(){x&&(n(new V("Request aborted",V.ECONNABORTED,r,x)),x=null)},x.onerror=function(C){const O=C&&C.message?C.message:"Network Error",b=new V(O,V.ERR_NETWORK,r,x);b.event=C||null,n(b),x=null},x.ontimeout=function(){let C=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const O=i.transitional||yi;i.timeoutErrorMessage&&(C=i.timeoutErrorMessage),n(new V(C,O.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,r,x)),x=null},a===void 0&&o.setContentType(null),"setRequestHeader"in x&&m.forEach(o.toJSON(),function(C,O){x.setRequestHeader(O,C)}),m.isUndefined(i.withCredentials)||(x.withCredentials=!!i.withCredentials),s&&s!=="json"&&(x.responseType=i.responseType),u&&([h,v]=rr(u,!0),x.addEventListener("progress",h)),f&&x.upload&&([l,d]=rr(f),x.upload.addEventListener("progress",l),x.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(c=A=>{x&&(n(!A||A.type?new Ot(null,r,x):A),x.abort(),x=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));const w=oo(i.url);if(w&&Ne.protocols.indexOf(w)===-1){n(new V("Unsupported protocol "+w+":",V.ERR_BAD_REQUEST,r));return}x.send(a||null)})},go=(r,e)=>{const{length:t}=r=r?r.filter(Boolean):[];if(e||t){let n=new AbortController,i;const a=function(u){if(!i){i=!0,s();const c=u instanceof Error?u:this.reason;n.abort(c instanceof V?c:new Ot(c instanceof Error?c.message:c))}};let o=e&&setTimeout(()=>{o=null,a(new V(`timeout ${e} of ms exceeded`,V.ETIMEDOUT))},e);const s=()=>{r&&(o&&clearTimeout(o),o=null,r.forEach(u=>{u.unsubscribe?u.unsubscribe(a):u.removeEventListener("abort",a)}),r=null)};r.forEach(u=>u.addEventListener("abort",a));const{signal:f}=n;return f.unsubscribe=()=>m.asap(s),f}},xo=function*(r,e){let t=r.byteLength;if(t<e){yield r;return}let n=0,i;for(;n<t;)i=n+e,yield r.slice(n,i),n=i},yo=async function*(r,e){for await(const t of wo(r))yield*xo(t,e)},wo=async function*(r){if(r[Symbol.asyncIterator]){yield*r;return}const e=r.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},pn=(r,e,t,n)=>{const i=yo(r,e);let a=0,o,s=f=>{o||(o=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:u,value:c}=await i.next();if(u){s(),f.close();return}let l=c.byteLength;if(t){let h=a+=l;t(h)}f.enqueue(new Uint8Array(c))}catch(u){throw s(u),u}},cancel(f){return s(f),i.return()}},{highWaterMark:2})},gn=64*1024,{isFunction:Xt}=m,_o=(({Request:r,Response:e})=>({Request:r,Response:e}))(m.global),{ReadableStream:xn,TextEncoder:yn}=m.global,wn=(r,...e)=>{try{return!!r(...e)}catch{return!1}},Ao=r=>{r=m.merge.call({skipUndefined:!0},_o,r);const{fetch:e,Request:t,Response:n}=r,i=e?Xt(e):typeof fetch=="function",a=Xt(t),o=Xt(n);if(!i)return!1;const s=i&&Xt(xn),f=i&&(typeof yn=="function"?(v=>p=>v.encode(p))(new yn):async v=>new Uint8Array(await new t(v).arrayBuffer())),u=a&&s&&wn(()=>{let v=!1;const p=new t(Ne.origin,{body:new xn,method:"POST",get duplex(){return v=!0,"half"}}).headers.has("Content-Type");return v&&!p}),c=o&&s&&wn(()=>m.isReadableStream(new n("").body)),l={stream:c&&(v=>v.body)};i&&["text","arrayBuffer","blob","formData","stream"].forEach(v=>{!l[v]&&(l[v]=(p,x)=>{let y=p&&p[v];if(y)return y.call(p);throw new V(`Response type '${v}' is not supported`,V.ERR_NOT_SUPPORT,x)})});const h=async v=>{if(v==null)return 0;if(m.isBlob(v))return v.size;if(m.isSpecCompliantForm(v))return(await new t(Ne.origin,{method:"POST",body:v}).arrayBuffer()).byteLength;if(m.isArrayBufferView(v)||m.isArrayBuffer(v))return v.byteLength;if(m.isURLSearchParams(v)&&(v=v+""),m.isString(v))return(await f(v)).byteLength},d=async(v,p)=>{const x=m.toFiniteNumber(v.getContentLength());return x??h(p)};return async v=>{let{url:p,method:x,data:y,signal:w,cancelToken:A,timeout:C,onDownloadProgress:O,onUploadProgress:b,responseType:T,headers:D,withCredentials:B="same-origin",fetchOptions:M}=Ci(v),ae=e||fetch;T=T?(T+"").toLowerCase():"text";let W=go([w,A&&A.toAbortSignal()],C),Q=null;const he=W&&W.unsubscribe&&(()=>{W.unsubscribe()});let Me;try{if(b&&u&&x!=="get"&&x!=="head"&&(Me=await d(D,y))!==0){let _=new t(p,{method:"POST",body:y,duplex:"half"}),P;if(m.isFormData(y)&&(P=_.headers.get("content-type"))&&D.setContentType(P),_.body){const[k,H]=hn(Me,rr(dn(b)));y=pn(_.body,gn,k,H)}}m.isString(B)||(B=B?"include":"omit");const Ie=a&&"credentials"in t.prototype,ot={...M,signal:W,method:x.toUpperCase(),headers:D.normalize().toJSON(),body:y,duplex:"half",credentials:Ie?B:void 0};Q=a&&new t(p,ot);let ke=await(a?ae(Q,M):ae(p,ot));const Wt=c&&(T==="stream"||T==="response");if(c&&(O||Wt&&he)){const _={};["status","statusText","headers"].forEach(G=>{_[G]=ke[G]});const P=m.toFiniteNumber(ke.headers.get("content-length")),[k,H]=O&&hn(P,rr(dn(O),!0))||[];ke=new n(pn(ke.body,gn,k,()=>{H&&H(),he&&he()}),_)}T=T||"text";let S=await l[m.findKey(l,T)||"text"](ke,v);return!Wt&&he&&he(),await new Promise((_,P)=>{Ai(_,P,{data:S,headers:We.from(ke.headers),status:ke.status,statusText:ke.statusText,config:v,request:Q})})}catch(Ie){throw he&&he(),Ie&&Ie.name==="TypeError"&&/Load failed|fetch/i.test(Ie.message)?Object.assign(new V("Network Error",V.ERR_NETWORK,v,Q),{cause:Ie.cause||Ie}):V.from(Ie,Ie&&Ie.code,v,Q)}}},Eo=new Map,mi=r=>{let e=r?r.env:{};const{fetch:t,Request:n,Response:i}=e,a=[n,i,t];let o=a.length,s=o,f,u,c=Eo;for(;s--;)f=a[s],u=c.get(f),u===void 0&&c.set(f,u=s?new Map:Ao(e)),c=u;return u};mi();const Tr={http:ka,xhr:po,fetch:{get:mi}};m.forEach(Tr,(r,e)=>{if(r){try{Object.defineProperty(r,"name",{value:e})}catch{}Object.defineProperty(r,"adapterName",{value:e})}});const _n=r=>`- ${r}`,Co=r=>m.isFunction(r)||r===null||r===!1,Si={getAdapter:(r,e)=>{r=m.isArray(r)?r:[r];const{length:t}=r;let n,i;const a={};for(let o=0;o<t;o++){n=r[o];let s;if(i=n,!Co(n)&&(i=Tr[(s=String(n)).toLowerCase()],i===void 0))throw new V(`Unknown adapter '${s}'`);if(i&&(m.isFunction(i)||(i=i.get(e))))break;a[s||"#"+o]=i}if(!i){const o=Object.entries(a).map(([f,u])=>`adapter ${f} `+(u===!1?"is not supported by the environment":"is not available in the build"));let s=t?o.length>1?`since :
`+o.map(_n).join(`
`):" "+_n(o[0]):"as no adapter specified";throw new V("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return i},adapters:Tr};function gr(r){if(r.cancelToken&&r.cancelToken.throwIfRequested(),r.signal&&r.signal.aborted)throw new Ot(null,r)}function An(r){return gr(r),r.headers=We.from(r.headers),r.data=pr.call(r,r.transformRequest),["post","put","patch"].indexOf(r.method)!==-1&&r.headers.setContentType("application/x-www-form-urlencoded",!1),Si.getAdapter(r.adapter||Ht.adapter,r)(r).then(function(n){return gr(r),n.data=pr.call(r,r.transformResponse,n),n.headers=We.from(n.headers),n},function(n){return _i(n)||(gr(r),n&&n.response&&(n.response.data=pr.call(r,r.transformResponse,n.response),n.response.headers=We.from(n.response.headers))),Promise.reject(n)})}const Ii="1.12.2",ur={};["object","boolean","number","function","string","symbol"].forEach((r,e)=>{ur[r]=function(n){return typeof n===r||"a"+(e<1?"n ":" ")+r}});const En={};ur.transitional=function(e,t,n){function i(a,o){return"[Axios v"+Ii+"] Transitional option '"+a+"'"+o+(n?". "+n:"")}return(a,o,s)=>{if(e===!1)throw new V(i(o," has been removed"+(t?" in "+t:"")),V.ERR_DEPRECATED);return t&&!En[o]&&(En[o]=!0,console.warn(i(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(a,o,s):!0}};ur.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function mo(r,e,t){if(typeof r!="object")throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);const n=Object.keys(r);let i=n.length;for(;i-- >0;){const a=n[i],o=e[a];if(o){const s=r[a],f=s===void 0||o(s,a,r);if(f!==!0)throw new V("option "+a+" must be "+f,V.ERR_BAD_OPTION_VALUE);continue}if(t!==!0)throw new V("Unknown option "+a,V.ERR_BAD_OPTION)}}const Qt={assertOptions:mo,validators:ur},rt=Qt.validators;let yt=class{constructor(e){this.defaults=e||{},this.interceptors={request:new cn,response:new cn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const a=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?a&&!String(n.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+a):n.stack=a}catch{}}throw n}}_request(e,t){typeof e=="string"?(t=t||{},t.url=e):t=e||{},t=_t(this.defaults,t);const{transitional:n,paramsSerializer:i,headers:a}=t;n!==void 0&&Qt.assertOptions(n,{silentJSONParsing:rt.transitional(rt.boolean),forcedJSONParsing:rt.transitional(rt.boolean),clarifyTimeoutError:rt.transitional(rt.boolean)},!1),i!=null&&(m.isFunction(i)?t.paramsSerializer={serialize:i}:Qt.assertOptions(i,{encode:rt.function,serialize:rt.function},!0)),t.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Qt.assertOptions(t,{baseUrl:rt.spelling("baseURL"),withXsrfToken:rt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&m.merge(a.common,a[t.method]);a&&m.forEach(["delete","get","head","post","put","patch","common"],v=>{delete a[v]}),t.headers=We.concat(o,a);const s=[];let f=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(t)===!1||(f=f&&p.synchronous,s.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let c,l=0,h;if(!f){const v=[An.bind(this),void 0];for(v.unshift(...s),v.push(...u),h=v.length,c=Promise.resolve(t);l<h;)c=c.then(v[l++],v[l++]);return c}h=s.length;let d=t;for(;l<h;){const v=s[l++],p=s[l++];try{d=v(d)}catch(x){p.call(this,x);break}}try{c=An.call(this,d)}catch(v){return Promise.reject(v)}for(l=0,h=u.length;l<h;)c=c.then(u[l++],u[l++]);return c}getUri(e){e=_t(this.defaults,e);const t=Ei(e.baseURL,e.url,e.allowAbsoluteUrls);return xi(t,e.params,e.paramsSerializer)}};m.forEach(["delete","get","head","options"],function(e){yt.prototype[e]=function(t,n){return this.request(_t(n||{},{method:e,url:t,data:(n||{}).data}))}});m.forEach(["post","put","patch"],function(e){function t(n){return function(a,o,s){return this.request(_t(s||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:a,data:o}))}}yt.prototype[e]=t(),yt.prototype[e+"Form"]=t(!0)});let So=class Oi{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(a){t=a});const n=this;this.promise.then(i=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](i);n._listeners=null}),this.promise.then=i=>{let a;const o=new Promise(s=>{n.subscribe(s),a=s}).then(i);return o.cancel=function(){n.unsubscribe(a)},o},e(function(a,o,s){n.reason||(n.reason=new Ot(a,o,s),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);t!==-1&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=n=>{e.abort(n)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Oi(function(i){e=i}),cancel:e}}};function Io(r){return function(t){return r.apply(null,t)}}function Oo(r){return m.isObject(r)&&r.isAxiosError===!0}const Rr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Rr).forEach(([r,e])=>{Rr[e]=r});function bi(r){const e=new yt(r),t=ai(yt.prototype.request,e);return m.extend(t,yt.prototype,e,{allOwnKeys:!0}),m.extend(t,e,null,{allOwnKeys:!0}),t.create=function(i){return bi(_t(r,i))},t}const pe=bi(Ht);pe.Axios=yt;pe.CanceledError=Ot;pe.CancelToken=So;pe.isCancel=_i;pe.VERSION=Ii;pe.toFormData=fr;pe.AxiosError=V;pe.Cancel=pe.CanceledError;pe.all=function(e){return Promise.all(e)};pe.spread=Io;pe.isAxiosError=Oo;pe.mergeConfig=_t;pe.AxiosHeaders=We;pe.formToJSON=r=>wi(m.isHTMLForm(r)?new FormData(r):r);pe.getAdapter=Si.getAdapter;pe.HttpStatusCode=Rr;pe.default=pe;const{Axios:y0,AxiosError:w0,CanceledError:_0,isCancel:A0,CancelToken:E0,VERSION:C0,all:m0,Cancel:S0,isAxiosError:I0,spread:O0,toFormData:b0,AxiosHeaders:T0,HttpStatusCode:R0,formToJSON:D0,getAdapter:N0,mergeConfig:P0}=pe;window.axios=pe;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";document.addEventListener("livewire:init",()=>{Alpine.data("addBookScanner",()=>({cameraSupported:!1,scanning:!1,codeReader:null,cameraStream:null,init(){console.log("Initializing addBookScanner component..."),this.initBarcodeReader(),this.setupEventListeners(),setTimeout(()=>{this.checkCameraSupport()},100)},checkCameraSupport(){setTimeout(()=>{typeof window.MobileApp<"u"&&window.MobileApp.checkCameraSupport?window.MobileApp.checkCameraSupport().then(r=>{this.cameraSupported=r,this.$wire.call("checkCameraSupport",r)}):setTimeout(()=>this.checkCameraSupport(),100)},100)},initBarcodeReader(){window.BrowserMultiFormatReader&&(this.codeReader=new window.BrowserMultiFormatReader)},setupEventListeners(){this.$wire.on("start-barcode-scanning",()=>{this.startScanning()}),this.$wire.on("stop-barcode-scanning",()=>{this.stopScanning()}),this.$wire.on("checkCameraSupport",()=>{this.checkCameraSupport()})},async startScanning(r=0){if(console.log("Starting camera scanning...","Retry:",r),!this.codeReader){console.error("Barcode reader not initialized"),this.$wire.call("cameraError","Barcode scanner not ready. Please refresh the page.");return}this.scanning=!0,setTimeout(async()=>{const e=document.getElementById("barcode-scanner");if(!e)if(console.error("Video element not found, retry count:",r),r<2){this.scanning=!1,this.startScanning(r+1);return}else{this.scanning=!1,this.$wire.call("cameraError","Camera interface not ready. Please refresh the page.");return}await this.initializeCamera(e)},r===0?100:300)},async initializeCamera(r){try{console.log("Requesting camera access..."),this.cameraStream=await window.MobileApp.startCamera(r),console.log("Camera stream started, notifying Livewire..."),this.$wire.call("cameraInitialized"),setTimeout(()=>{console.log("Starting barcode detection..."),this.codeReader.decodeFromVideoDevice(null,"barcode-scanner",(e,t)=>{e&&(console.log("Barcode detected:",e.text),console.log("Barcode format:",e.format),console.log("Full result object:",e),this.$wire.call("handleScannedCode",e.text),this.stopScanning()),t&&t.name!=="NotFoundException"&&console.log("Barcode scanning error:",t)})},500)}catch(e){console.error("Failed to start camera:",e),this.scanning=!1;let t="Camera access failed. ";e.name==="NotAllowedError"?t+="Please allow camera access and try again.":e.name==="NotFoundError"?t+="No camera found on this device.":e.name==="NotReadableError"?t+="Camera is being used by another application.":t+="Please check permissions and try again.",this.$wire.call("cameraError",t)}},stopScanning(){console.log("Stopping camera scanning..."),this.codeReader&&(this.codeReader.reset(),this.scanning=!1),this.cameraStream&&(console.log("Stopping camera stream..."),window.MobileApp.stopCamera(this.cameraStream),this.cameraStream=null);const r=document.getElementById("barcode-scanner");r&&(r.srcObject=null,r.pause()),console.log("Camera scanning stopped")}}))});function bo(r,e){var t=Object.setPrototypeOf;t?t(r,e):r.__proto__=e}function To(r,e){e===void 0&&(e=r.constructor);var t=Error.captureStackTrace;t&&t(r,e)}var Ro=(function(){var r=function(t,n){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},r(t,n)};return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Do=(function(r){Ro(e,r);function e(t,n){var i=this.constructor,a=r.call(this,t,n)||this;return Object.defineProperty(a,"name",{value:i.name,enumerable:!1,configurable:!0}),bo(a,i.prototype),To(a),a}return e})(Error),No=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Xe=(function(r){No(e,r);function e(t){t===void 0&&(t=void 0);var n=r.call(this,t)||this;return n.message=t,n}return e.prototype.getKind=function(){var t=this.constructor;return t.kind},e.kind="Exception",e})(Do),Po=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),qe=(function(r){Po(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ArgumentException",e})(Xe),Mo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),L=(function(r){Mo(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IllegalArgumentException",e})(Xe),Bo=(function(){function r(e){if(this.binarizer=e,e===null)throw new L("Binarizer must be non-null.")}return r.prototype.getWidth=function(){return this.binarizer.getWidth()},r.prototype.getHeight=function(){return this.binarizer.getHeight()},r.prototype.getBlackRow=function(e,t){return this.binarizer.getBlackRow(e,t)},r.prototype.getBlackMatrix=function(){return(this.matrix===null||this.matrix===void 0)&&(this.matrix=this.binarizer.getBlackMatrix()),this.matrix},r.prototype.isCropSupported=function(){return this.binarizer.getLuminanceSource().isCropSupported()},r.prototype.crop=function(e,t,n,i){var a=this.binarizer.getLuminanceSource().crop(e,t,n,i);return new r(this.binarizer.createBinarizer(a))},r.prototype.isRotateSupported=function(){return this.binarizer.getLuminanceSource().isRotateSupported()},r.prototype.rotateCounterClockwise=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise();return new r(this.binarizer.createBinarizer(e))},r.prototype.rotateCounterClockwise45=function(){var e=this.binarizer.getLuminanceSource().rotateCounterClockwise45();return new r(this.binarizer.createBinarizer(e))},r.prototype.toString=function(){try{return this.getBlackMatrix().toString()}catch{return""}},r})(),Fo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),me=(function(r){Fo(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getChecksumInstance=function(){return new e},e.kind="ChecksumException",e})(Xe),Lo=(function(){function r(e){this.source=e}return r.prototype.getLuminanceSource=function(){return this.source},r.prototype.getWidth=function(){return this.source.getWidth()},r.prototype.getHeight=function(){return this.source.getHeight()},r})(),se=(function(){function r(){}return r.arraycopy=function(e,t,n,i,a){for(;a--;)n[i++]=e[t++]},r.currentTimeMillis=function(){return Date.now()},r})(),ko=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),$r=(function(r){ko(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IndexOutOfBoundsException",e})(Xe),Uo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Cn=(function(r){Uo(e,r);function e(t,n){t===void 0&&(t=void 0),n===void 0&&(n=void 0);var i=r.call(this,n)||this;return i.index=t,i.message=n,i}return e.kind="ArrayIndexOutOfBoundsException",e})($r),Ho=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},_e=(function(){function r(){}return r.fill=function(e,t){for(var n=0,i=e.length;n<i;n++)e[n]=t},r.fillWithin=function(e,t,n,i){r.rangeCheck(e.length,t,n);for(var a=t;a<n;a++)e[a]=i},r.rangeCheck=function(e,t,n){if(t>n)throw new L("fromIndex("+t+") > toIndex("+n+")");if(t<0)throw new Cn(t);if(n>e)throw new Cn(n)},r.asList=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e},r.create=function(e,t,n){var i=Array.from({length:e});return i.map(function(a){return Array.from({length:t}).fill(n)})},r.createInt32Array=function(e,t,n){var i=Array.from({length:e});return i.map(function(a){return Int32Array.from({length:t}).fill(n)})},r.equals=function(e,t){if(!e||!t||!e.length||!t.length||e.length!==t.length)return!1;for(var n=0,i=e.length;n<i;n++)if(e[n]!==t[n])return!1;return!0},r.hashCode=function(e){var t,n;if(e===null)return 0;var i=1;try{for(var a=Ho(e),o=a.next();!o.done;o=a.next()){var s=o.value;i=31*i+s}}catch(f){t={error:f}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.fillUint8Array=function(e,t){for(var n=0;n!==e.length;n++)e[n]=t},r.copyOf=function(e,t){return e.slice(0,t)},r.copyOfUint8Array=function(e,t){if(e.length<=t){var n=new Uint8Array(t);return n.set(e),n}return e.slice(0,t)},r.copyOfRange=function(e,t,n){var i=n-t,a=new Int32Array(i);return se.arraycopy(e,t,a,0,i),a},r.binarySearch=function(e,t,n){n===void 0&&(n=r.numberComparator);for(var i=0,a=e.length-1;i<=a;){var o=a+i>>1,s=n(t,e[o]);if(s>0)i=o+1;else if(s<0)a=o-1;else return o}return-i-1},r.numberComparator=function(e,t){return e-t},r})(),z=(function(){function r(){}return r.numberOfTrailingZeros=function(e){var t;if(e===0)return 32;var n=31;return t=e<<16,t!==0&&(n-=16,e=t),t=e<<8,t!==0&&(n-=8,e=t),t=e<<4,t!==0&&(n-=4,e=t),t=e<<2,t!==0&&(n-=2,e=t),n-(e<<1>>>31)},r.numberOfLeadingZeros=function(e){if(e===0)return 32;var t=1;return e>>>16||(t+=16,e<<=16),e>>>24||(t+=8,e<<=8),e>>>28||(t+=4,e<<=4),e>>>30||(t+=2,e<<=2),t-=e>>>31,t},r.toHexString=function(e){return e.toString(16)},r.toBinaryString=function(e){return String(parseInt(String(e),2))},r.bitCount=function(e){return e=e-(e>>>1&1431655765),e=(e&858993459)+(e>>>2&858993459),e=e+(e>>>4)&252645135,e=e+(e>>>8),e=e+(e>>>16),e&63},r.truncDivision=function(e,t){return Math.trunc(e/t)},r.parseInt=function(e,t){return t===void 0&&(t=void 0),parseInt(e,t)},r.MIN_VALUE_32_BITS=-2147483648,r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r})(),Se=(function(){function r(e,t){e===void 0?(this.size=0,this.bits=new Int32Array(1)):(this.size=e,t==null?this.bits=r.makeArray(e):this.bits=t)}return r.prototype.getSize=function(){return this.size},r.prototype.getSizeInBytes=function(){return Math.floor((this.size+7)/8)},r.prototype.ensureCapacity=function(e){if(e>this.bits.length*32){var t=r.makeArray(e);se.arraycopy(this.bits,0,t,0,this.bits.length),this.bits=t}},r.prototype.get=function(e){return(this.bits[Math.floor(e/32)]&1<<(e&31))!==0},r.prototype.set=function(e){this.bits[Math.floor(e/32)]|=1<<(e&31)},r.prototype.flip=function(e){this.bits[Math.floor(e/32)]^=1<<(e&31)},r.prototype.getNextSet=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=n[i];a&=~((1<<(e&31))-1);for(var o=n.length;a===0;){if(++i===o)return t;a=n[i]}var s=i*32+z.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.getNextUnset=function(e){var t=this.size;if(e>=t)return t;var n=this.bits,i=Math.floor(e/32),a=~n[i];a&=~((1<<(e&31))-1);for(var o=n.length;a===0;){if(++i===o)return t;a=~n[i]}var s=i*32+z.numberOfTrailingZeros(a);return s>t?t:s},r.prototype.setBulk=function(e,t){this.bits[Math.floor(e/32)]=t},r.prototype.setRange=function(e,t){if(t<e||e<0||t>this.size)throw new L;if(t!==e){t--;for(var n=Math.floor(e/32),i=Math.floor(t/32),a=this.bits,o=n;o<=i;o++){var s=o>n?0:e&31,f=o<i?31:t&31,u=(2<<f)-(1<<s);a[o]|=u}}},r.prototype.clear=function(){for(var e=this.bits.length,t=this.bits,n=0;n<e;n++)t[n]=0},r.prototype.isRange=function(e,t,n){if(t<e||e<0||t>this.size)throw new L;if(t===e)return!0;t--;for(var i=Math.floor(e/32),a=Math.floor(t/32),o=this.bits,s=i;s<=a;s++){var f=s>i?0:e&31,u=s<a?31:t&31,c=(2<<u)-(1<<f)&4294967295;if((o[s]&c)!==(n?c:0))return!1}return!0},r.prototype.appendBit=function(e){this.ensureCapacity(this.size+1),e&&(this.bits[Math.floor(this.size/32)]|=1<<(this.size&31)),this.size++},r.prototype.appendBits=function(e,t){if(t<0||t>32)throw new L("Num bits must be between 0 and 32");this.ensureCapacity(this.size+t);for(var n=t;n>0;n--)this.appendBit((e>>n-1&1)===1)},r.prototype.appendBitArray=function(e){var t=e.size;this.ensureCapacity(this.size+t);for(var n=0;n<t;n++)this.appendBit(e.get(n))},r.prototype.xor=function(e){if(this.size!==e.size)throw new L("Sizes don't match");for(var t=this.bits,n=0,i=t.length;n<i;n++)t[n]^=e.bits[n]},r.prototype.toBytes=function(e,t,n,i){for(var a=0;a<i;a++){for(var o=0,s=0;s<8;s++)this.get(e)&&(o|=1<<7-s),e++;t[n+a]=o}},r.prototype.getBitArray=function(){return this.bits},r.prototype.reverse=function(){for(var e=new Int32Array(this.bits.length),t=Math.floor((this.size-1)/32),n=t+1,i=this.bits,a=0;a<n;a++){var o=i[a];o=o>>1&1431655765|(o&1431655765)<<1,o=o>>2&858993459|(o&858993459)<<2,o=o>>4&252645135|(o&252645135)<<4,o=o>>8&16711935|(o&16711935)<<8,o=o>>16&65535|(o&65535)<<16,e[t-a]=o}if(this.size!==n*32){for(var s=n*32-this.size,f=e[0]>>>s,a=1;a<n;a++){var u=e[a];f|=u<<32-s,e[a-1]=f,f=u>>>s}e[n-1]=f}this.bits=e},r.makeArray=function(e){return new Int32Array(Math.floor((e+31)/32))},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.size===t.size&&_e.equals(this.bits,t.bits)},r.prototype.hashCode=function(){return 31*this.size+_e.hashCode(this.bits)},r.prototype.toString=function(){for(var e="",t=0,n=this.size;t<n;t++)(t&7)===0&&(e+=" "),e+=this.get(t)?"X":".";return e},r.prototype.clone=function(){return new r(this.size,this.bits.slice())},r.prototype.toArray=function(){for(var e=[],t=0,n=this.size;t<n;t++)e.push(this.get(t));return e},r})(),Dr;(function(r){r[r.OTHER=0]="OTHER",r[r.PURE_BARCODE=1]="PURE_BARCODE",r[r.POSSIBLE_FORMATS=2]="POSSIBLE_FORMATS",r[r.TRY_HARDER=3]="TRY_HARDER",r[r.CHARACTER_SET=4]="CHARACTER_SET",r[r.ALLOWED_LENGTHS=5]="ALLOWED_LENGTHS",r[r.ASSUME_CODE_39_CHECK_DIGIT=6]="ASSUME_CODE_39_CHECK_DIGIT",r[r.ENABLE_CODE_39_EXTENDED_MODE=7]="ENABLE_CODE_39_EXTENDED_MODE",r[r.ASSUME_GS1=8]="ASSUME_GS1",r[r.RETURN_CODABAR_START_END=9]="RETURN_CODABAR_START_END",r[r.NEED_RESULT_POINT_CALLBACK=10]="NEED_RESULT_POINT_CALLBACK",r[r.ALLOWED_EAN_EXTENSIONS=11]="ALLOWED_EAN_EXTENSIONS"})(Dr||(Dr={}));const le=Dr;var Vo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),R=(function(r){Vo(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getFormatInstance=function(){return new e},e.kind="FormatException",e})(Xe),Go=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},re;(function(r){r[r.Cp437=0]="Cp437",r[r.ISO8859_1=1]="ISO8859_1",r[r.ISO8859_2=2]="ISO8859_2",r[r.ISO8859_3=3]="ISO8859_3",r[r.ISO8859_4=4]="ISO8859_4",r[r.ISO8859_5=5]="ISO8859_5",r[r.ISO8859_6=6]="ISO8859_6",r[r.ISO8859_7=7]="ISO8859_7",r[r.ISO8859_8=8]="ISO8859_8",r[r.ISO8859_9=9]="ISO8859_9",r[r.ISO8859_10=10]="ISO8859_10",r[r.ISO8859_11=11]="ISO8859_11",r[r.ISO8859_13=12]="ISO8859_13",r[r.ISO8859_14=13]="ISO8859_14",r[r.ISO8859_15=14]="ISO8859_15",r[r.ISO8859_16=15]="ISO8859_16",r[r.SJIS=16]="SJIS",r[r.Cp1250=17]="Cp1250",r[r.Cp1251=18]="Cp1251",r[r.Cp1252=19]="Cp1252",r[r.Cp1256=20]="Cp1256",r[r.UnicodeBigUnmarked=21]="UnicodeBigUnmarked",r[r.UTF8=22]="UTF8",r[r.ASCII=23]="ASCII",r[r.Big5=24]="Big5",r[r.GB18030=25]="GB18030",r[r.EUC_KR=26]="EUC_KR"})(re||(re={}));var we=(function(){function r(e,t,n){for(var i,a,o=[],s=3;s<arguments.length;s++)o[s-3]=arguments[s];this.valueIdentifier=e,this.name=n,typeof t=="number"?this.values=Int32Array.from([t]):this.values=t,this.otherEncodingNames=o,r.VALUE_IDENTIFIER_TO_ECI.set(e,this),r.NAME_TO_ECI.set(n,this);for(var f=this.values,u=0,c=f.length;u!==c;u++){var l=f[u];r.VALUES_TO_ECI.set(l,this)}try{for(var h=Go(o),d=h.next();!d.done;d=h.next()){var v=d.value;r.NAME_TO_ECI.set(v,this)}}catch(p){i={error:p}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}}return r.prototype.getValueIdentifier=function(){return this.valueIdentifier},r.prototype.getName=function(){return this.name},r.prototype.getValue=function(){return this.values[0]},r.getCharacterSetECIByValue=function(e){if(e<0||e>=900)throw new R("incorect value");var t=r.VALUES_TO_ECI.get(e);if(t===void 0)throw new R("incorect value");return t},r.getCharacterSetECIByName=function(e){var t=r.NAME_TO_ECI.get(e);if(t===void 0)throw new R("incorect value");return t},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.getName()===t.getName()},r.VALUE_IDENTIFIER_TO_ECI=new Map,r.VALUES_TO_ECI=new Map,r.NAME_TO_ECI=new Map,r.Cp437=new r(re.Cp437,Int32Array.from([0,2]),"Cp437"),r.ISO8859_1=new r(re.ISO8859_1,Int32Array.from([1,3]),"ISO-8859-1","ISO88591","ISO8859_1"),r.ISO8859_2=new r(re.ISO8859_2,4,"ISO-8859-2","ISO88592","ISO8859_2"),r.ISO8859_3=new r(re.ISO8859_3,5,"ISO-8859-3","ISO88593","ISO8859_3"),r.ISO8859_4=new r(re.ISO8859_4,6,"ISO-8859-4","ISO88594","ISO8859_4"),r.ISO8859_5=new r(re.ISO8859_5,7,"ISO-8859-5","ISO88595","ISO8859_5"),r.ISO8859_6=new r(re.ISO8859_6,8,"ISO-8859-6","ISO88596","ISO8859_6"),r.ISO8859_7=new r(re.ISO8859_7,9,"ISO-8859-7","ISO88597","ISO8859_7"),r.ISO8859_8=new r(re.ISO8859_8,10,"ISO-8859-8","ISO88598","ISO8859_8"),r.ISO8859_9=new r(re.ISO8859_9,11,"ISO-8859-9","ISO88599","ISO8859_9"),r.ISO8859_10=new r(re.ISO8859_10,12,"ISO-8859-10","ISO885910","ISO8859_10"),r.ISO8859_11=new r(re.ISO8859_11,13,"ISO-8859-11","ISO885911","ISO8859_11"),r.ISO8859_13=new r(re.ISO8859_13,15,"ISO-8859-13","ISO885913","ISO8859_13"),r.ISO8859_14=new r(re.ISO8859_14,16,"ISO-8859-14","ISO885914","ISO8859_14"),r.ISO8859_15=new r(re.ISO8859_15,17,"ISO-8859-15","ISO885915","ISO8859_15"),r.ISO8859_16=new r(re.ISO8859_16,18,"ISO-8859-16","ISO885916","ISO8859_16"),r.SJIS=new r(re.SJIS,20,"SJIS","Shift_JIS"),r.Cp1250=new r(re.Cp1250,21,"Cp1250","windows-1250"),r.Cp1251=new r(re.Cp1251,22,"Cp1251","windows-1251"),r.Cp1252=new r(re.Cp1252,23,"Cp1252","windows-1252"),r.Cp1256=new r(re.Cp1256,24,"Cp1256","windows-1256"),r.UnicodeBigUnmarked=new r(re.UnicodeBigUnmarked,25,"UnicodeBigUnmarked","UTF-16BE","UnicodeBig"),r.UTF8=new r(re.UTF8,26,"UTF8","UTF-8"),r.ASCII=new r(re.ASCII,Int32Array.from([27,170]),"ASCII","US-ASCII"),r.Big5=new r(re.Big5,28,"Big5"),r.GB18030=new r(re.GB18030,29,"GB18030","GB2312","EUC_CN","GBK"),r.EUC_KR=new r(re.EUC_KR,30,"EUC_KR","EUC-KR"),r})(),Wo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Jt=(function(r){Wo(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="UnsupportedOperationException",e})(Xe),Ye=(function(){function r(){}return r.decode=function(e,t){var n=this.encodingName(t);return this.customDecoder?this.customDecoder(e,n):typeof TextDecoder>"u"||this.shouldDecodeOnFallback(n)?this.decodeFallback(e,n):new TextDecoder(n).decode(e)},r.shouldDecodeOnFallback=function(e){return!r.isBrowser()&&e==="ISO-8859-1"},r.encode=function(e,t){var n=this.encodingName(t);return this.customEncoder?this.customEncoder(e,n):typeof TextEncoder>"u"?this.encodeFallback(e):new TextEncoder().encode(e)},r.isBrowser=function(){return typeof window<"u"&&{}.toString.call(window)==="[object Window]"},r.encodingName=function(e){return typeof e=="string"?e:e.getName()},r.encodingCharacterSet=function(e){return e instanceof we?e:we.getCharacterSetECIByName(e)},r.decodeFallback=function(e,t){var n=this.encodingCharacterSet(t);if(r.isDecodeFallbackSupported(n)){for(var i="",a=0,o=e.length;a<o;a++){var s=e[a].toString(16);s.length<2&&(s="0"+s),i+="%"+s}return decodeURIComponent(i)}if(n.equals(we.UnicodeBigUnmarked))return String.fromCharCode.apply(null,new Uint16Array(e.buffer));throw new Jt("Encoding "+this.encodingName(t)+" not supported by fallback.")},r.isDecodeFallbackSupported=function(e){return e.equals(we.UTF8)||e.equals(we.ISO8859_1)||e.equals(we.ASCII)},r.encodeFallback=function(e){for(var t=btoa(unescape(encodeURIComponent(e))),n=t.split(""),i=[],a=0;a<n.length;a++)i.push(n[a].charCodeAt(0));return new Uint8Array(i)},r})(),$=(function(){function r(){}return r.castAsNonUtf8Char=function(e,t){t===void 0&&(t=null);var n=t?t.getName():this.ISO88591;return Ye.decode(new Uint8Array([e]),n)},r.guessEncoding=function(e,t){if(t!=null&&t.get(le.CHARACTER_SET)!==void 0)return t.get(le.CHARACTER_SET).toString();for(var n=e.length,i=!0,a=!0,o=!0,s=0,f=0,u=0,c=0,l=0,h=0,d=0,v=0,p=0,x=0,y=0,w=e.length>3&&e[0]===239&&e[1]===187&&e[2]===191,A=0;A<n&&(i||a||o);A++){var C=e[A]&255;o&&(s>0?(C&128)===0?o=!1:s--:(C&128)!==0&&((C&64)===0?o=!1:(s++,(C&32)===0?f++:(s++,(C&16)===0?u++:(s++,(C&8)===0?c++:o=!1))))),i&&(C>127&&C<160?i=!1:C>159&&(C<192||C===215||C===247)&&y++),a&&(l>0?C<64||C===127||C>252?a=!1:l--:C===128||C===160||C>239?a=!1:C>160&&C<224?(h++,v=0,d++,d>p&&(p=d)):C>127?(l++,d=0,v++,v>x&&(x=v)):(d=0,v=0))}return o&&s>0&&(o=!1),a&&l>0&&(a=!1),o&&(w||f+u+c>0)?r.UTF8:a&&(r.ASSUME_SHIFT_JIS||p>=3||x>=3)?r.SHIFT_JIS:i&&a?p===2&&h===2||y*10>=n?r.SHIFT_JIS:r.ISO88591:i?r.ISO88591:a?r.SHIFT_JIS:o?r.UTF8:r.PLATFORM_DEFAULT_ENCODING},r.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=-1;function a(s,f,u,c,l,h){if(s==="%%")return"%";if(t[++i]!==void 0){s=c?parseInt(c.substr(1)):void 0;var d=l?parseInt(l.substr(1)):void 0,v;switch(h){case"s":v=t[i];break;case"c":v=t[i][0];break;case"f":v=parseFloat(t[i]).toFixed(s);break;case"p":v=parseFloat(t[i]).toPrecision(s);break;case"e":v=parseFloat(t[i]).toExponential(s);break;case"x":v=parseInt(t[i]).toString(d||16);break;case"d":v=parseFloat(parseInt(t[i],d||10).toPrecision(s)).toFixed(0);break}v=typeof v=="object"?JSON.stringify(v):(+v).toString(d);for(var p=parseInt(u),x=u&&u[0]+""=="0"?"0":" ";v.length<p;)v=f!==void 0?v+x:x+v;return v}}var o=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(o,a)},r.getBytes=function(e,t){return Ye.encode(e,t)},r.getCharCode=function(e,t){return t===void 0&&(t=0),e.charCodeAt(t)},r.getCharAt=function(e){return String.fromCharCode(e)},r.SHIFT_JIS=we.SJIS.getName(),r.GB2312="GB2312",r.ISO88591=we.ISO8859_1.getName(),r.EUC_JP="EUC_JP",r.UTF8=we.UTF8.getName(),r.PLATFORM_DEFAULT_ENCODING=r.UTF8,r.ASSUME_SHIFT_JIS=!1,r})(),X=(function(){function r(e){e===void 0&&(e=""),this.value=e}return r.prototype.enableDecoding=function(e){return this.encoding=e,this},r.prototype.append=function(e){return typeof e=="string"?this.value+=e.toString():this.encoding?this.value+=$.castAsNonUtf8Char(e,this.encoding):this.value+=String.fromCharCode(e),this},r.prototype.appendChars=function(e,t,n){for(var i=t;t<t+n;i++)this.append(e[i]);return this},r.prototype.length=function(){return this.value.length},r.prototype.charAt=function(e){return this.value.charAt(e)},r.prototype.deleteCharAt=function(e){this.value=this.value.substr(0,e)+this.value.substring(e+1)},r.prototype.setCharAt=function(e,t){this.value=this.value.substr(0,e)+t+this.value.substr(e+1)},r.prototype.substring=function(e,t){return this.value.substring(e,t)},r.prototype.setLengthToZero=function(){this.value=""},r.prototype.toString=function(){return this.value},r.prototype.insert=function(e,t){this.value=this.value.substring(0,e)+t+this.value.substring(e)},r})(),it=(function(){function r(e,t,n,i){if(this.width=e,this.height=t,this.rowSize=n,this.bits=i,t==null&&(t=e),this.height=t,e<1||t<1)throw new L("Both dimensions must be greater than 0");n==null&&(n=Math.floor((e+31)/32)),this.rowSize=n,i==null&&(this.bits=new Int32Array(this.rowSize*this.height))}return r.parseFromBooleanArray=function(e){for(var t=e.length,n=e[0].length,i=new r(n,t),a=0;a<t;a++)for(var o=e[a],s=0;s<n;s++)o[s]&&i.set(s,a);return i},r.parseFromString=function(e,t,n){if(e===null)throw new L("stringRepresentation cannot be null");for(var i=new Array(e.length),a=0,o=0,s=-1,f=0,u=0;u<e.length;)if(e.charAt(u)===`
`||e.charAt(u)==="\r"){if(a>o){if(s===-1)s=a-o;else if(a-o!==s)throw new L("row lengths do not match");o=a,f++}u++}else if(e.substring(u,u+t.length)===t)u+=t.length,i[a]=!0,a++;else if(e.substring(u,u+n.length)===n)u+=n.length,i[a]=!1,a++;else throw new L("illegal character encountered: "+e.substring(u));if(a>o){if(s===-1)s=a-o;else if(a-o!==s)throw new L("row lengths do not match");f++}for(var c=new r(s,f),l=0;l<a;l++)i[l]&&c.set(Math.floor(l%s),Math.floor(l/s));return c},r.prototype.get=function(e,t){var n=t*this.rowSize+Math.floor(e/32);return(this.bits[n]>>>(e&31)&1)!==0},r.prototype.set=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]|=1<<(e&31)&4294967295},r.prototype.unset=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]&=~(1<<(e&31)&4294967295)},r.prototype.flip=function(e,t){var n=t*this.rowSize+Math.floor(e/32);this.bits[n]^=1<<(e&31)&4294967295},r.prototype.xor=function(e){if(this.width!==e.getWidth()||this.height!==e.getHeight()||this.rowSize!==e.getRowSize())throw new L("input matrix dimensions do not match");for(var t=new Se(Math.floor(this.width/32)+1),n=this.rowSize,i=this.bits,a=0,o=this.height;a<o;a++)for(var s=a*n,f=e.getRow(a,t).getBitArray(),u=0;u<n;u++)i[s+u]^=f[u]},r.prototype.clear=function(){for(var e=this.bits,t=e.length,n=0;n<t;n++)e[n]=0},r.prototype.setRegion=function(e,t,n,i){if(t<0||e<0)throw new L("Left and top must be nonnegative");if(i<1||n<1)throw new L("Height and width must be at least 1");var a=e+n,o=t+i;if(o>this.height||a>this.width)throw new L("The region must fit inside the matrix");for(var s=this.rowSize,f=this.bits,u=t;u<o;u++)for(var c=u*s,l=e;l<a;l++)f[c+Math.floor(l/32)]|=1<<(l&31)&4294967295},r.prototype.getRow=function(e,t){t==null||t.getSize()<this.width?t=new Se(this.width):t.clear();for(var n=this.rowSize,i=this.bits,a=e*n,o=0;o<n;o++)t.setBulk(o*32,i[a+o]);return t},r.prototype.setRow=function(e,t){se.arraycopy(t.getBitArray(),0,this.bits,e*this.rowSize,this.rowSize)},r.prototype.rotate180=function(){for(var e=this.getWidth(),t=this.getHeight(),n=new Se(e),i=new Se(e),a=0,o=Math.floor((t+1)/2);a<o;a++)n=this.getRow(a,n),i=this.getRow(t-1-a,i),n.reverse(),i.reverse(),this.setRow(a,i),this.setRow(t-1-a,n)},r.prototype.getEnclosingRectangle=function(){for(var e=this.width,t=this.height,n=this.rowSize,i=this.bits,a=e,o=t,s=-1,f=-1,u=0;u<t;u++)for(var c=0;c<n;c++){var l=i[u*n+c];if(l!==0){if(u<o&&(o=u),u>f&&(f=u),c*32<a){for(var h=0;(l<<31-h&4294967295)===0;)h++;c*32+h<a&&(a=c*32+h)}if(c*32+31>s){for(var h=31;!(l>>>h);)h--;c*32+h>s&&(s=c*32+h)}}}return s<a||f<o?null:Int32Array.from([a,o,s-a+1,f-o+1])},r.prototype.getTopLeftOnBit=function(){for(var e=this.rowSize,t=this.bits,n=0;n<t.length&&t[n]===0;)n++;if(n===t.length)return null;for(var i=n/e,a=n%e*32,o=t[n],s=0;(o<<31-s&4294967295)===0;)s++;return a+=s,Int32Array.from([a,i])},r.prototype.getBottomRightOnBit=function(){for(var e=this.rowSize,t=this.bits,n=t.length-1;n>=0&&t[n]===0;)n--;if(n<0)return null;for(var i=Math.floor(n/e),a=Math.floor(n%e)*32,o=t[n],s=31;!(o>>>s);)s--;return a+=s,Int32Array.from([a,i])},r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.getRowSize=function(){return this.rowSize},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.width===t.width&&this.height===t.height&&this.rowSize===t.rowSize&&_e.equals(this.bits,t.bits)},r.prototype.hashCode=function(){var e=this.width;return e=31*e+this.width,e=31*e+this.height,e=31*e+this.rowSize,e=31*e+_e.hashCode(this.bits),e},r.prototype.toString=function(e,t,n){return e===void 0&&(e="X "),t===void 0&&(t="  "),n===void 0&&(n=`
`),this.buildToString(e,t,n)},r.prototype.buildToString=function(e,t,n){for(var i=new X,a=0,o=this.height;a<o;a++){for(var s=0,f=this.width;s<f;s++)i.append(this.get(s,a)?e:t);i.append(n)}return i.toString()},r.prototype.clone=function(){return new r(this.width,this.height,this.rowSize,this.bits.slice())},r})(),Xo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),I=(function(r){Xo(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.getNotFoundInstance=function(){return new e},e.kind="NotFoundException",e})(Xe),zo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),jo=(function(r){zo(e,r);function e(t){var n=r.call(this,t)||this;return n.luminances=e.EMPTY,n.buckets=new Int32Array(e.LUMINANCE_BUCKETS),n}return e.prototype.getBlackRow=function(t,n){var i=this.getLuminanceSource(),a=i.getWidth();n==null||n.getSize()<a?n=new Se(a):n.clear(),this.initArrays(a);for(var o=i.getRow(t,this.luminances),s=this.buckets,f=0;f<a;f++)s[(o[f]&255)>>e.LUMINANCE_SHIFT]++;var u=e.estimateBlackPoint(s);if(a<3)for(var f=0;f<a;f++)(o[f]&255)<u&&n.set(f);else for(var c=o[0]&255,l=o[1]&255,f=1;f<a-1;f++){var h=o[f+1]&255;(l*4-c-h)/2<u&&n.set(f),c=l,l=h}return n},e.prototype.getBlackMatrix=function(){var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight(),a=new it(n,i);this.initArrays(n);for(var o=this.buckets,s=1;s<5;s++)for(var f=Math.floor(i*s/5),u=t.getRow(f,this.luminances),c=Math.floor(n*4/5),l=Math.floor(n/5);l<c;l++){var h=u[l]&255;o[h>>e.LUMINANCE_SHIFT]++}for(var d=e.estimateBlackPoint(o),v=t.getMatrix(),s=0;s<i;s++)for(var p=s*n,l=0;l<n;l++){var h=v[p+l]&255;h<d&&a.set(l,s)}return a},e.prototype.createBinarizer=function(t){return new e(t)},e.prototype.initArrays=function(t){this.luminances.length<t&&(this.luminances=new Uint8ClampedArray(t));for(var n=this.buckets,i=0;i<e.LUMINANCE_BUCKETS;i++)n[i]=0},e.estimateBlackPoint=function(t){for(var n=t.length,i=0,a=0,o=0,s=0;s<n;s++)t[s]>o&&(a=s,o=t[s]),t[s]>i&&(i=t[s]);for(var f=0,u=0,s=0;s<n;s++){var c=s-a,l=t[s]*c*c;l>u&&(f=s,u=l)}if(a>f){var h=a;a=f,f=h}if(f-a<=n/16)throw new I;for(var d=f-1,v=-1,s=f-1;s>a;s--){var p=s-a,l=p*p*(f-s)*(i-t[s]);l>v&&(d=s,v=l)}return d<<e.LUMINANCE_SHIFT},e.LUMINANCE_BITS=5,e.LUMINANCE_SHIFT=8-e.LUMINANCE_BITS,e.LUMINANCE_BUCKETS=1<<e.LUMINANCE_BITS,e.EMPTY=Uint8ClampedArray.from([0]),e})(Lo),Yo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),$o=(function(r){Yo(e,r);function e(t){var n=r.call(this,t)||this;return n.matrix=null,n}return e.prototype.getBlackMatrix=function(){if(this.matrix!==null)return this.matrix;var t=this.getLuminanceSource(),n=t.getWidth(),i=t.getHeight();if(n>=e.MINIMUM_DIMENSION&&i>=e.MINIMUM_DIMENSION){var a=t.getMatrix(),o=n>>e.BLOCK_SIZE_POWER;(n&e.BLOCK_SIZE_MASK)!==0&&o++;var s=i>>e.BLOCK_SIZE_POWER;(i&e.BLOCK_SIZE_MASK)!==0&&s++;var f=e.calculateBlackPoints(a,o,s,n,i),u=new it(n,i);e.calculateThresholdForBlock(a,o,s,n,i,f,u),this.matrix=u}else this.matrix=r.prototype.getBlackMatrix.call(this);return this.matrix},e.prototype.createBinarizer=function(t){return new e(t)},e.calculateThresholdForBlock=function(t,n,i,a,o,s,f){for(var u=o-e.BLOCK_SIZE,c=a-e.BLOCK_SIZE,l=0;l<i;l++){var h=l<<e.BLOCK_SIZE_POWER;h>u&&(h=u);for(var d=e.cap(l,2,i-3),v=0;v<n;v++){var p=v<<e.BLOCK_SIZE_POWER;p>c&&(p=c);for(var x=e.cap(v,2,n-3),y=0,w=-2;w<=2;w++){var A=s[d+w];y+=A[x-2]+A[x-1]+A[x]+A[x+1]+A[x+2]}var C=y/25;e.thresholdBlock(t,p,h,C,a,f)}}},e.cap=function(t,n,i){return t<n?n:t>i?i:t},e.thresholdBlock=function(t,n,i,a,o,s){for(var f=0,u=i*o+n;f<e.BLOCK_SIZE;f++,u+=o)for(var c=0;c<e.BLOCK_SIZE;c++)(t[u+c]&255)<=a&&s.set(n+c,i+f)},e.calculateBlackPoints=function(t,n,i,a,o){for(var s=o-e.BLOCK_SIZE,f=a-e.BLOCK_SIZE,u=new Array(i),c=0;c<i;c++){u[c]=new Int32Array(n);var l=c<<e.BLOCK_SIZE_POWER;l>s&&(l=s);for(var h=0;h<n;h++){var d=h<<e.BLOCK_SIZE_POWER;d>f&&(d=f);for(var v=0,p=255,x=0,y=0,w=l*a+d;y<e.BLOCK_SIZE;y++,w+=a){for(var A=0;A<e.BLOCK_SIZE;A++){var C=t[w+A]&255;v+=C,C<p&&(p=C),C>x&&(x=C)}if(x-p>e.MIN_DYNAMIC_RANGE)for(y++,w+=a;y<e.BLOCK_SIZE;y++,w+=a)for(var A=0;A<e.BLOCK_SIZE;A++)v+=t[w+A]&255}var O=v>>e.BLOCK_SIZE_POWER*2;if(x-p<=e.MIN_DYNAMIC_RANGE&&(O=p/2,c>0&&h>0)){var b=(u[c-1][h]+2*u[c][h-1]+u[c-1][h-1])/4;p<b&&(O=b)}u[c][h]=O}}return u},e.BLOCK_SIZE_POWER=3,e.BLOCK_SIZE=1<<e.BLOCK_SIZE_POWER,e.BLOCK_SIZE_MASK=e.BLOCK_SIZE-1,e.MINIMUM_DIMENSION=e.BLOCK_SIZE*5,e.MIN_DYNAMIC_RANGE=24,e})(jo),cr=(function(){function r(e,t){this.width=e,this.height=t}return r.prototype.getWidth=function(){return this.width},r.prototype.getHeight=function(){return this.height},r.prototype.isCropSupported=function(){return!1},r.prototype.crop=function(e,t,n,i){throw new Jt("This luminance source does not support cropping.")},r.prototype.isRotateSupported=function(){return!1},r.prototype.rotateCounterClockwise=function(){throw new Jt("This luminance source does not support rotation by 90 degrees.")},r.prototype.rotateCounterClockwise45=function(){throw new Jt("This luminance source does not support rotation by 45 degrees.")},r.prototype.toString=function(){for(var e=new Uint8ClampedArray(this.width),t=new X,n=0;n<this.height;n++){for(var i=this.getRow(n,e),a=0;a<this.width;a++){var o=i[a]&255,s=void 0;o<64?s="#":o<128?s="+":o<192?s=".":s=" ",t.append(s)}t.append(`
`)}return t.toString()},r})(),Zo=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Zr=(function(r){Zo(e,r);function e(t){var n=r.call(this,t.getWidth(),t.getHeight())||this;return n.delegate=t,n}return e.prototype.getRow=function(t,n){for(var i=this.delegate.getRow(t,n),a=this.getWidth(),o=0;o<a;o++)i[o]=255-(i[o]&255);return i},e.prototype.getMatrix=function(){for(var t=this.delegate.getMatrix(),n=this.getWidth()*this.getHeight(),i=new Uint8ClampedArray(n),a=0;a<n;a++)i[a]=255-(t[a]&255);return i},e.prototype.isCropSupported=function(){return this.delegate.isCropSupported()},e.prototype.crop=function(t,n,i,a){return new e(this.delegate.crop(t,n,i,a))},e.prototype.isRotateSupported=function(){return this.delegate.isRotateSupported()},e.prototype.invert=function(){return this.delegate},e.prototype.rotateCounterClockwise=function(){return new e(this.delegate.rotateCounterClockwise())},e.prototype.rotateCounterClockwise45=function(){return new e(this.delegate.rotateCounterClockwise45())},e})(cr),Ko=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),qo=(function(r){Ko(e,r);function e(t,n){n===void 0&&(n=!1);var i=r.call(this,t.width,t.height)||this;return i.canvas=t,i.tempCanvasElement=null,i.buffer=e.makeBufferFromCanvasImageData(t,n),i}return e.makeBufferFromCanvasImageData=function(t,n){n===void 0&&(n=!1);var i=t.getContext("2d").getImageData(0,0,t.width,t.height);return e.toGrayscaleBuffer(i.data,t.width,t.height,n)},e.toGrayscaleBuffer=function(t,n,i,a){a===void 0&&(a=!1);var o=new Uint8ClampedArray(n*i);if(e.FRAME_INDEX=!e.FRAME_INDEX,e.FRAME_INDEX||!a)for(var s=0,f=0,u=t.length;s<u;s+=4,f++){var c=void 0,l=t[s+3];if(l===0)c=255;else{var h=t[s],d=t[s+1],v=t[s+2];c=306*h+601*d+117*v+512>>10}o[f]=c}else for(var s=0,f=0,p=t.length;s<p;s+=4,f++){var c=void 0,l=t[s+3];if(l===0)c=255;else{var h=t[s],d=t[s+1],v=t[s+2];c=306*h+601*d+117*v+512>>10}o[f]=255-c}return o},e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new L("Requested row is outside the image: "+t);var i=this.getWidth(),a=t*i;return n===null?n=this.buffer.slice(a,a+i):(n.length<i&&(n=new Uint8ClampedArray(i)),n.set(this.buffer.slice(a,a+i))),n},e.prototype.getMatrix=function(){return this.buffer},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return r.prototype.crop.call(this,t,n,i,a),this},e.prototype.isRotateSupported=function(){return!0},e.prototype.rotateCounterClockwise=function(){return this.rotate(-90),this},e.prototype.rotateCounterClockwise45=function(){return this.rotate(-45),this},e.prototype.getTempCanvasElement=function(){if(this.tempCanvasElement===null){var t=this.canvas.ownerDocument.createElement("canvas");t.width=this.canvas.width,t.height=this.canvas.height,this.tempCanvasElement=t}return this.tempCanvasElement},e.prototype.rotate=function(t){var n=this.getTempCanvasElement(),i=n.getContext("2d"),a=t*e.DEGREE_TO_RADIANS,o=this.canvas.width,s=this.canvas.height,f=Math.ceil(Math.abs(Math.cos(a))*o+Math.abs(Math.sin(a))*s),u=Math.ceil(Math.abs(Math.sin(a))*o+Math.abs(Math.cos(a))*s);return n.width=f,n.height=u,i.translate(f/2,u/2),i.rotate(a),i.drawImage(this.canvas,o/-2,s/-2),this.buffer=e.makeBufferFromCanvasImageData(n),this},e.prototype.invert=function(){return new Zr(this)},e.DEGREE_TO_RADIANS=Math.PI/180,e.FRAME_INDEX=!0,e})(cr),Qo=(function(){function r(e,t,n){this.deviceId=e,this.label=t,this.kind="videoinput",this.groupId=n||void 0}return r.prototype.toJSON=function(){return{kind:this.kind,groupId:this.groupId,deviceId:this.deviceId,label:this.label}},r})(),Be=function(r,e,t,n){function i(a){return a instanceof t?a:new t(function(o){o(a)})}return new(t||(t=Promise))(function(a,o){function s(c){try{u(n.next(c))}catch(l){o(l)}}function f(c){try{u(n.throw(c))}catch(l){o(l)}}function u(c){c.done?a(c.value):i(c.value).then(s,f)}u((n=n.apply(r,e||[])).next())})},Fe=function(r,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(u){return function(c){return f([u,c])}}function f(u){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=u[0]&2?i.return:u[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,u[1])).done)return a;switch(i=0,a&&(u=[u[0]&2,a.value]),u[0]){case 0:case 1:a=u;break;case 4:return t.label++,{value:u[1],done:!1};case 5:t.label++,i=u[1],u=[0];continue;case 7:u=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(u[0]===6||u[0]===2)){t=0;continue}if(u[0]===3&&(!a||u[1]>a[0]&&u[1]<a[3])){t.label=u[1];break}if(u[0]===6&&t.label<a[1]){t.label=a[1],a=u;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(u);break}a[2]&&t.ops.pop(),t.trys.pop();continue}u=e.call(r,t)}catch(c){u=[6,c],i=0}finally{n=a=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}},Jo=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},bt=(function(){function r(e,t,n){t===void 0&&(t=500),this.reader=e,this.timeBetweenScansMillis=t,this._hints=n,this._stopContinuousDecode=!1,this._stopAsyncDecode=!1,this._timeBetweenDecodingAttempts=0}return Object.defineProperty(r.prototype,"hasNavigator",{get:function(){return typeof navigator<"u"},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"isMediaDevicesSuported",{get:function(){return this.hasNavigator&&!!navigator.mediaDevices},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"canEnumerateDevices",{get:function(){return!!(this.isMediaDevicesSuported&&navigator.mediaDevices.enumerateDevices)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"timeBetweenDecodingAttempts",{get:function(){return this._timeBetweenDecodingAttempts},set:function(e){this._timeBetweenDecodingAttempts=e<0?0:e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"hints",{get:function(){return this._hints},set:function(e){this._hints=e||null},enumerable:!1,configurable:!0}),r.prototype.listVideoInputDevices=function(){return Be(this,void 0,void 0,function(){var e,t,n,i,a,o,s,f,u,c,l,h;return Fe(this,function(d){switch(d.label){case 0:if(!this.hasNavigator)throw new Error("Can't enumerate devices, navigator is not present.");if(!this.canEnumerateDevices)throw new Error("Can't enumerate devices, method not supported.");return[4,navigator.mediaDevices.enumerateDevices()];case 1:e=d.sent(),t=[];try{for(n=Jo(e),i=n.next();!i.done;i=n.next())a=i.value,o=a.kind==="video"?"videoinput":a.kind,o==="videoinput"&&(s=a.deviceId||a.id,f=a.label||"Video device "+(t.length+1),u=a.groupId,c={deviceId:s,label:f,kind:o,groupId:u},t.push(c))}catch(v){l={error:v}}finally{try{i&&!i.done&&(h=n.return)&&h.call(n)}finally{if(l)throw l.error}}return[2,t]}})})},r.prototype.getVideoInputDevices=function(){return Be(this,void 0,void 0,function(){var e;return Fe(this,function(t){switch(t.label){case 0:return[4,this.listVideoInputDevices()];case 1:return e=t.sent(),[2,e.map(function(n){return new Qo(n.deviceId,n.label)})]}})})},r.prototype.findDeviceById=function(e){return Be(this,void 0,void 0,function(){var t;return Fe(this,function(n){switch(n.label){case 0:return[4,this.listVideoInputDevices()];case 1:return t=n.sent(),t?[2,t.find(function(i){return i.deviceId===e})]:[2,null]}})})},r.prototype.decodeFromInputVideoDevice=function(e,t){return Be(this,void 0,void 0,function(){return Fe(this,function(n){switch(n.label){case 0:return[4,this.decodeOnceFromVideoDevice(e,t)];case 1:return[2,n.sent()]}})})},r.prototype.decodeOnceFromVideoDevice=function(e,t){return Be(this,void 0,void 0,function(){var n,i;return Fe(this,function(a){switch(a.label){case 0:return this.reset(),e?n={deviceId:{exact:e}}:n={facingMode:"environment"},i={video:n},[4,this.decodeOnceFromConstraints(i,t)];case 1:return[2,a.sent()]}})})},r.prototype.decodeOnceFromConstraints=function(e,t){return Be(this,void 0,void 0,function(){var n;return Fe(this,function(i){switch(i.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return n=i.sent(),[4,this.decodeOnceFromStream(n,t)];case 2:return[2,i.sent()]}})})},r.prototype.decodeOnceFromStream=function(e,t){return Be(this,void 0,void 0,function(){var n,i;return Fe(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return n=a.sent(),[4,this.decodeOnce(n)];case 2:return i=a.sent(),[2,i]}})})},r.prototype.decodeFromInputVideoDeviceContinuously=function(e,t,n){return Be(this,void 0,void 0,function(){return Fe(this,function(i){switch(i.label){case 0:return[4,this.decodeFromVideoDevice(e,t,n)];case 1:return[2,i.sent()]}})})},r.prototype.decodeFromVideoDevice=function(e,t,n){return Be(this,void 0,void 0,function(){var i,a;return Fe(this,function(o){switch(o.label){case 0:return e?i={deviceId:{exact:e}}:i={facingMode:"environment"},a={video:i},[4,this.decodeFromConstraints(a,t,n)];case 1:return[2,o.sent()]}})})},r.prototype.decodeFromConstraints=function(e,t,n){return Be(this,void 0,void 0,function(){var i;return Fe(this,function(a){switch(a.label){case 0:return[4,navigator.mediaDevices.getUserMedia(e)];case 1:return i=a.sent(),[4,this.decodeFromStream(i,t,n)];case 2:return[2,a.sent()]}})})},r.prototype.decodeFromStream=function(e,t,n){return Be(this,void 0,void 0,function(){var i;return Fe(this,function(a){switch(a.label){case 0:return this.reset(),[4,this.attachStreamToVideo(e,t)];case 1:return i=a.sent(),[4,this.decodeContinuously(i,n)];case 2:return[2,a.sent()]}})})},r.prototype.stopAsyncDecode=function(){this._stopAsyncDecode=!0},r.prototype.stopContinuousDecode=function(){this._stopContinuousDecode=!0},r.prototype.attachStreamToVideo=function(e,t){return Be(this,void 0,void 0,function(){var n;return Fe(this,function(i){switch(i.label){case 0:return n=this.prepareVideoElement(t),this.addVideoSource(n,e),this.videoElement=n,this.stream=e,[4,this.playVideoOnLoadAsync(n)];case 1:return i.sent(),[2,n]}})})},r.prototype.playVideoOnLoadAsync=function(e){var t=this;return new Promise(function(n,i){return t.playVideoOnLoad(e,function(){return n()})})},r.prototype.playVideoOnLoad=function(e,t){var n=this;this.videoEndedListener=function(){return n.stopStreams()},this.videoCanPlayListener=function(){return n.tryPlayVideo(e)},e.addEventListener("ended",this.videoEndedListener),e.addEventListener("canplay",this.videoCanPlayListener),e.addEventListener("playing",t),this.tryPlayVideo(e)},r.prototype.isVideoPlaying=function(e){return e.currentTime>0&&!e.paused&&!e.ended&&e.readyState>2},r.prototype.tryPlayVideo=function(e){return Be(this,void 0,void 0,function(){return Fe(this,function(t){switch(t.label){case 0:if(this.isVideoPlaying(e))return console.warn("Trying to play video that is already playing."),[2];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,e.play()];case 2:return t.sent(),[3,4];case 3:return t.sent(),console.warn("It was not possible to play the video."),[3,4];case 4:return[2]}})})},r.prototype.getMediaElement=function(e,t){var n=document.getElementById(e);if(!n)throw new qe("element with id '"+e+"' not found");if(n.nodeName.toLowerCase()!==t.toLowerCase())throw new qe("element with id '"+e+"' must be an "+t+" element");return n},r.prototype.decodeFromImage=function(e,t){if(!e&&!t)throw new qe("either imageElement with a src set or an url must be provided");return t&&!e?this.decodeFromImageUrl(t):this.decodeFromImageElement(e)},r.prototype.decodeFromVideo=function(e,t){if(!e&&!t)throw new qe("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrl(t):this.decodeFromVideoElement(e)},r.prototype.decodeFromVideoContinuously=function(e,t,n){if(e===void 0&&t===void 0)throw new qe("Either an element with a src set or an URL must be provided");return t&&!e?this.decodeFromVideoUrlContinuously(t,n):this.decodeFromVideoElementContinuously(e,n)},r.prototype.decodeFromImageElement=function(e){if(!e)throw new qe("An image element must be provided.");this.reset();var t=this.prepareImageElement(e);this.imageElement=t;var n;return this.isImageLoaded(t)?n=this.decodeOnce(t,!1,!0):n=this._decodeOnLoadImage(t),n},r.prototype.decodeFromVideoElement=function(e){var t=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideo(t)},r.prototype.decodeFromVideoElementContinuously=function(e,t){var n=this._decodeFromVideoElementSetup(e);return this._decodeOnLoadVideoContinuously(n,t)},r.prototype._decodeFromVideoElementSetup=function(e){if(!e)throw new qe("A video element must be provided.");this.reset();var t=this.prepareVideoElement(e);return this.videoElement=t,t},r.prototype.decodeFromImageUrl=function(e){if(!e)throw new qe("An URL must be provided.");this.reset();var t=this.prepareImageElement();this.imageElement=t;var n=this._decodeOnLoadImage(t);return t.src=e,n},r.prototype.decodeFromVideoUrl=function(e){if(!e)throw new qe("An URL must be provided.");this.reset();var t=this.prepareVideoElement(),n=this.decodeFromVideoElement(t);return t.src=e,n},r.prototype.decodeFromVideoUrlContinuously=function(e,t){if(!e)throw new qe("An URL must be provided.");this.reset();var n=this.prepareVideoElement(),i=this.decodeFromVideoElementContinuously(n,t);return n.src=e,i},r.prototype._decodeOnLoadImage=function(e){var t=this;return new Promise(function(n,i){t.imageLoadedListener=function(){return t.decodeOnce(e,!1,!0).then(n,i)},e.addEventListener("load",t.imageLoadedListener)})},r.prototype._decodeOnLoadVideo=function(e){return Be(this,void 0,void 0,function(){return Fe(this,function(t){switch(t.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return t.sent(),[4,this.decodeOnce(e)];case 2:return[2,t.sent()]}})})},r.prototype._decodeOnLoadVideoContinuously=function(e,t){return Be(this,void 0,void 0,function(){return Fe(this,function(n){switch(n.label){case 0:return[4,this.playVideoOnLoadAsync(e)];case 1:return n.sent(),this.decodeContinuously(e,t),[2]}})})},r.prototype.isImageLoaded=function(e){return!(!e.complete||e.naturalWidth===0)},r.prototype.prepareImageElement=function(e){var t;return typeof e>"u"&&(t=document.createElement("img"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"img")),e instanceof HTMLImageElement&&(t=e),t},r.prototype.prepareVideoElement=function(e){var t;return!e&&typeof document<"u"&&(t=document.createElement("video"),t.width=200,t.height=200),typeof e=="string"&&(t=this.getMediaElement(e,"video")),e instanceof HTMLVideoElement&&(t=e),t.setAttribute("autoplay","true"),t.setAttribute("muted","true"),t.setAttribute("playsinline","true"),t},r.prototype.decodeOnce=function(e,t,n){var i=this;t===void 0&&(t=!0),n===void 0&&(n=!0),this._stopAsyncDecode=!1;var a=function(o,s){if(i._stopAsyncDecode){s(new I("Video stream has ended before any code could be detected.")),i._stopAsyncDecode=void 0;return}try{var f=i.decode(e);o(f)}catch(h){var u=t&&h instanceof I,c=h instanceof me||h instanceof R,l=c&&n;if(u||l)return setTimeout(a,i._timeBetweenDecodingAttempts,o,s);s(h)}};return new Promise(function(o,s){return a(o,s)})},r.prototype.decodeContinuously=function(e,t){var n=this;this._stopContinuousDecode=!1;var i=function(){if(n._stopContinuousDecode){n._stopContinuousDecode=void 0;return}try{var a=n.decode(e);t(a,null),setTimeout(i,n.timeBetweenScansMillis)}catch(f){t(null,f);var o=f instanceof me||f instanceof R,s=f instanceof I;(o||s)&&setTimeout(i,n._timeBetweenDecodingAttempts)}};i()},r.prototype.decode=function(e){var t=this.createBinaryBitmap(e);return this.decodeBitmap(t)},r.prototype.createBinaryBitmap=function(e){this.getCaptureCanvasContext(e);var t=!1;e instanceof HTMLVideoElement?(this.drawFrameOnCanvas(e),t=!0):this.drawImageOnCanvas(e);var n=this.getCaptureCanvas(e),i=new qo(n,t),a=new $o(i);return new Bo(a)},r.prototype.getCaptureCanvasContext=function(e){if(!this.captureCanvasContext){var t=this.getCaptureCanvas(e),n=void 0;try{n=t.getContext("2d",{willReadFrequently:!0})}catch{n=t.getContext("2d")}this.captureCanvasContext=n}return this.captureCanvasContext},r.prototype.getCaptureCanvas=function(e){if(!this.captureCanvas){var t=this.createCaptureCanvas(e);this.captureCanvas=t}return this.captureCanvas},r.prototype.drawFrameOnCanvas=function(e,t,n){t===void 0&&(t={sx:0,sy:0,sWidth:e.videoWidth,sHeight:e.videoHeight,dx:0,dy:0,dWidth:e.videoWidth,dHeight:e.videoHeight}),n===void 0&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.drawImageOnCanvas=function(e,t,n){t===void 0&&(t={sx:0,sy:0,sWidth:e.naturalWidth,sHeight:e.naturalHeight,dx:0,dy:0,dWidth:e.naturalWidth,dHeight:e.naturalHeight}),n===void 0&&(n=this.captureCanvasContext),n.drawImage(e,t.sx,t.sy,t.sWidth,t.sHeight,t.dx,t.dy,t.dWidth,t.dHeight)},r.prototype.decodeBitmap=function(e){return this.reader.decode(e,this._hints)},r.prototype.createCaptureCanvas=function(e){if(typeof document>"u")return this._destroyCaptureCanvas(),null;var t=document.createElement("canvas"),n,i;return typeof e<"u"&&(e instanceof HTMLVideoElement?(n=e.videoWidth,i=e.videoHeight):e instanceof HTMLImageElement&&(n=e.naturalWidth||e.width,i=e.naturalHeight||e.height)),t.style.width=n+"px",t.style.height=i+"px",t.width=n,t.height=i,t},r.prototype.stopStreams=function(){this.stream&&(this.stream.getVideoTracks().forEach(function(e){return e.stop()}),this.stream=void 0),this._stopAsyncDecode===!1&&this.stopAsyncDecode(),this._stopContinuousDecode===!1&&this.stopContinuousDecode()},r.prototype.reset=function(){this.stopStreams(),this._destroyVideoElement(),this._destroyImageElement(),this._destroyCaptureCanvas()},r.prototype._destroyVideoElement=function(){this.videoElement&&(typeof this.videoEndedListener<"u"&&this.videoElement.removeEventListener("ended",this.videoEndedListener),typeof this.videoPlayingEventListener<"u"&&this.videoElement.removeEventListener("playing",this.videoPlayingEventListener),typeof this.videoCanPlayListener<"u"&&this.videoElement.removeEventListener("loadedmetadata",this.videoCanPlayListener),this.cleanVideoSource(this.videoElement),this.videoElement=void 0)},r.prototype._destroyImageElement=function(){this.imageElement&&(this.imageLoadedListener!==void 0&&this.imageElement.removeEventListener("load",this.imageLoadedListener),this.imageElement.src=void 0,this.imageElement.removeAttribute("src"),this.imageElement=void 0)},r.prototype._destroyCaptureCanvas=function(){this.captureCanvasContext=void 0,this.captureCanvas=void 0},r.prototype.addVideoSource=function(e,t){try{e.srcObject=t}catch{e.src=URL.createObjectURL(t)}},r.prototype.cleanVideoSource=function(e){try{e.srcObject=null}catch{e.src=""}this.videoElement.removeAttribute("src")},r})(),Pe=(function(){function r(e,t,n,i,a,o){n===void 0&&(n=t==null?0:8*t.length),o===void 0&&(o=se.currentTimeMillis()),this.text=e,this.rawBytes=t,this.numBits=n,this.resultPoints=i,this.format=a,this.timestamp=o,this.text=e,this.rawBytes=t,n==null?this.numBits=t==null?0:8*t.length:this.numBits=n,this.resultPoints=i,this.format=a,this.resultMetadata=null,o==null?this.timestamp=se.currentTimeMillis():this.timestamp=o}return r.prototype.getText=function(){return this.text},r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.getBarcodeFormat=function(){return this.format},r.prototype.getResultMetadata=function(){return this.resultMetadata},r.prototype.putMetadata=function(e,t){this.resultMetadata===null&&(this.resultMetadata=new Map),this.resultMetadata.set(e,t)},r.prototype.putAllMetadata=function(e){e!==null&&(this.resultMetadata===null?this.resultMetadata=e:this.resultMetadata=new Map(e))},r.prototype.addResultPoints=function(e){var t=this.resultPoints;if(t===null)this.resultPoints=e;else if(e!==null&&e.length>0){var n=new Array(t.length+e.length);se.arraycopy(t,0,n,0,t.length),se.arraycopy(e,0,n,t.length,e.length),this.resultPoints=n}},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.toString=function(){return this.text},r})(),Nr;(function(r){r[r.AZTEC=0]="AZTEC",r[r.CODABAR=1]="CODABAR",r[r.CODE_39=2]="CODE_39",r[r.CODE_93=3]="CODE_93",r[r.CODE_128=4]="CODE_128",r[r.DATA_MATRIX=5]="DATA_MATRIX",r[r.EAN_8=6]="EAN_8",r[r.EAN_13=7]="EAN_13",r[r.ITF=8]="ITF",r[r.MAXICODE=9]="MAXICODE",r[r.PDF_417=10]="PDF_417",r[r.QR_CODE=11]="QR_CODE",r[r.RSS_14=12]="RSS_14",r[r.RSS_EXPANDED=13]="RSS_EXPANDED",r[r.UPC_A=14]="UPC_A",r[r.UPC_E=15]="UPC_E",r[r.UPC_EAN_EXTENSION=16]="UPC_EAN_EXTENSION"})(Nr||(Nr={}));const F=Nr;var Pr;(function(r){r[r.OTHER=0]="OTHER",r[r.ORIENTATION=1]="ORIENTATION",r[r.BYTE_SEGMENTS=2]="BYTE_SEGMENTS",r[r.ERROR_CORRECTION_LEVEL=3]="ERROR_CORRECTION_LEVEL",r[r.ISSUE_NUMBER=4]="ISSUE_NUMBER",r[r.SUGGESTED_PRICE=5]="SUGGESTED_PRICE",r[r.POSSIBLE_COUNTRY=6]="POSSIBLE_COUNTRY",r[r.UPC_EAN_EXTENSION=7]="UPC_EAN_EXTENSION",r[r.PDF417_EXTRA_METADATA=8]="PDF417_EXTRA_METADATA",r[r.STRUCTURED_APPEND_SEQUENCE=9]="STRUCTURED_APPEND_SEQUENCE",r[r.STRUCTURED_APPEND_PARITY=10]="STRUCTURED_APPEND_PARITY"})(Pr||(Pr={}));const Re=Pr;var lr=(function(){function r(e,t,n,i,a,o){a===void 0&&(a=-1),o===void 0&&(o=-1),this.rawBytes=e,this.text=t,this.byteSegments=n,this.ecLevel=i,this.structuredAppendSequenceNumber=a,this.structuredAppendParity=o,this.numBits=e==null?0:8*e.length}return r.prototype.getRawBytes=function(){return this.rawBytes},r.prototype.getNumBits=function(){return this.numBits},r.prototype.setNumBits=function(e){this.numBits=e},r.prototype.getText=function(){return this.text},r.prototype.getByteSegments=function(){return this.byteSegments},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getErrorsCorrected=function(){return this.errorsCorrected},r.prototype.setErrorsCorrected=function(e){this.errorsCorrected=e},r.prototype.getErasures=function(){return this.erasures},r.prototype.setErasures=function(e){this.erasures=e},r.prototype.getOther=function(){return this.other},r.prototype.setOther=function(e){this.other=e},r.prototype.hasStructuredAppend=function(){return this.structuredAppendParity>=0&&this.structuredAppendSequenceNumber>=0},r.prototype.getStructuredAppendParity=function(){return this.structuredAppendParity},r.prototype.getStructuredAppendSequenceNumber=function(){return this.structuredAppendSequenceNumber},r})(),Pt=(function(){function r(){}return r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(e===0)throw new L;return this.logTable[e]},r.addOrSubtract=function(e,t){return e^t},r})(),ct=(function(){function r(e,t){if(t.length===0)throw new L;this.field=e;var n=t.length;if(n>1&&t[0]===0){for(var i=1;i<n&&t[i]===0;)i++;i===n?this.coefficients=Int32Array.from([0]):(this.coefficients=new Int32Array(n-i),se.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return this.coefficients[0]===0},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){if(e===0)return this.getCoefficient(0);var t=this.coefficients,n;if(e===1){n=0;for(var i=0,a=t.length;i!==a;i++){var o=t[i];n=Pt.addOrSubtract(n,o)}return n}n=t[0];for(var s=t.length,f=this.field,i=1;i<s;i++)n=Pt.addOrSubtract(f.multiply(e,n),t[i]);return n},r.prototype.addOrSubtract=function(e){if(!this.field.equals(e.field))throw new L("GenericGFPolys do not have same GenericGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;se.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=Pt.addOrSubtract(t[s-o],n[s]);return new r(this.field,a)},r.prototype.multiply=function(e){if(!this.field.equals(e.field))throw new L("GenericGFPolys do not have same GenericGF field");if(this.isZero()||e.isZero())return this.field.getZero();for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=this.field,f=0;f<n;f++)for(var u=t[f],c=0;c<a;c++)o[f+c]=Pt.addOrSubtract(o[f+c],s.multiply(u,i[c]));return new r(s,o)},r.prototype.multiplyScalar=function(e){if(e===0)return this.field.getZero();if(e===1)return this;for(var t=this.coefficients.length,n=this.field,i=new Int32Array(t),a=this.coefficients,o=0;o<t;o++)i[o]=n.multiply(a[o],e);return new r(n,i)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new L;if(t===0)return this.field.getZero();for(var n=this.coefficients,i=n.length,a=new Int32Array(i+e),o=this.field,s=0;s<i;s++)a[s]=o.multiply(n[s],t);return new r(o,a)},r.prototype.divide=function(e){if(!this.field.equals(e.field))throw new L("GenericGFPolys do not have same GenericGF field");if(e.isZero())throw new L("Divide by 0");for(var t=this.field,n=t.getZero(),i=this,a=e.getCoefficient(e.getDegree()),o=t.inverse(a);i.getDegree()>=e.getDegree()&&!i.isZero();){var s=i.getDegree()-e.getDegree(),f=t.multiply(i.getCoefficient(i.getDegree()),o),u=e.multiplyByMonomial(s,f),c=t.buildMonomial(s,f);n=n.addOrSubtract(c),i=i.addOrSubtract(u)}return[n,i]},r.prototype.toString=function(){for(var e="",t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);if(n!==0){if(n<0?(e+=" - ",n=-n):e.length>0&&(e+=" + "),t===0||n!==1){var i=this.field.log(n);i===0?e+="1":i===1?e+="a":(e+="a^",e+=i)}t!==0&&(t===1?e+="x":(e+="x^",e+=t))}}return e},r})(),es=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ti=(function(r){es(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ArithmeticException",e})(Xe),ts=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ve=(function(r){ts(e,r);function e(t,n,i){var a=r.call(this)||this;a.primitive=t,a.size=n,a.generatorBase=i;for(var o=new Int32Array(n),s=1,f=0;f<n;f++)o[f]=s,s*=2,s>=n&&(s^=t,s&=n-1);a.expTable=o;for(var u=new Int32Array(n),f=0;f<n-1;f++)u[o[f]]=f;return a.logTable=u,a.zero=new ct(a,Int32Array.from([0])),a.one=new ct(a,Int32Array.from([1])),a}return e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new L;if(n===0)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new ct(this,i)},e.prototype.inverse=function(t){if(t===0)throw new Ti;return this.expTable[this.size-this.logTable[t]-1]},e.prototype.multiply=function(t,n){return t===0||n===0?0:this.expTable[(this.logTable[t]+this.logTable[n])%(this.size-1)]},e.prototype.getSize=function(){return this.size},e.prototype.getGeneratorBase=function(){return this.generatorBase},e.prototype.toString=function(){return"GF(0x"+z.toHexString(this.primitive)+","+this.size+")"},e.prototype.equals=function(t){return t===this},e.AZTEC_DATA_12=new e(4201,4096,1),e.AZTEC_DATA_10=new e(1033,1024,1),e.AZTEC_DATA_6=new e(67,64,1),e.AZTEC_PARAM=new e(19,16,1),e.QR_CODE_FIELD_256=new e(285,256,0),e.DATA_MATRIX_FIELD_256=new e(301,256,1),e.AZTEC_DATA_8=e.DATA_MATRIX_FIELD_256,e.MAXICODE_FIELD_64=e.AZTEC_DATA_6,e})(Pt),rs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),zt=(function(r){rs(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ReedSolomonException",e})(Xe),ns=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Vt=(function(r){ns(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="IllegalStateException",e})(Xe),hr=(function(){function r(e){this.field=e}return r.prototype.decode=function(e,t){for(var n=this.field,i=new ct(n,e),a=new Int32Array(t),o=!0,s=0;s<t;s++){var f=i.evaluateAt(n.exp(s+n.getGeneratorBase()));a[a.length-1-s]=f,f!==0&&(o=!1)}if(!o)for(var u=new ct(n,a),c=this.runEuclideanAlgorithm(n.buildMonomial(t,1),u,t),l=c[0],h=c[1],d=this.findErrorLocations(l),v=this.findErrorMagnitudes(h,d),s=0;s<d.length;s++){var p=e.length-1-n.log(d[s]);if(p<0)throw new zt("Bad error location");e[p]=Ve.addOrSubtract(e[p],v[s])}},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=this.field,o=e,s=t,f=a.getZero(),u=a.getOne();s.getDegree()>=(n/2|0);){var c=o,l=f;if(o=s,f=u,o.isZero())throw new zt("r_{i-1} was zero");s=c;for(var h=a.getZero(),d=o.getCoefficient(o.getDegree()),v=a.inverse(d);s.getDegree()>=o.getDegree()&&!s.isZero();){var p=s.getDegree()-o.getDegree(),x=a.multiply(s.getCoefficient(s.getDegree()),v);h=h.addOrSubtract(a.buildMonomial(p,x)),s=s.addOrSubtract(o.multiplyByMonomial(p,x))}if(u=h.multiply(f).addOrSubtract(l),s.getDegree()>=o.getDegree())throw new Vt("Division algorithm failed to reduce polynomial?")}var y=u.getCoefficient(0);if(y===0)throw new zt("sigmaTilde(0) was zero");var w=a.inverse(y),A=u.multiplyScalar(w),C=s.multiplyScalar(w);return[A,C]},r.prototype.findErrorLocations=function(e){var t=e.getDegree();if(t===1)return Int32Array.from([e.getCoefficient(1)]);for(var n=new Int32Array(t),i=0,a=this.field,o=1;o<a.getSize()&&i<t;o++)e.evaluateAt(o)===0&&(n[i]=a.inverse(o),i++);if(i!==t)throw new zt("Error locator degree does not match number of roots");return n},r.prototype.findErrorMagnitudes=function(e,t){for(var n=t.length,i=new Int32Array(n),a=this.field,o=0;o<n;o++){for(var s=a.inverse(t[o]),f=1,u=0;u<n;u++)if(o!==u){var c=a.multiply(t[u],s),l=(c&1)===0?c|1:c&-2;f=a.multiply(f,l)}i[o]=a.multiply(e.evaluateAt(s),a.inverse(f)),a.getGeneratorBase()!==0&&(i[o]=a.multiply(i[o],s))}return i},r})(),be;(function(r){r[r.UPPER=0]="UPPER",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.DIGIT=3]="DIGIT",r[r.PUNCT=4]="PUNCT",r[r.BINARY=5]="BINARY"})(be||(be={}));var mn=(function(){function r(){}return r.prototype.decode=function(e){this.ddata=e;var t=e.getBits(),n=this.extractBits(t),i=this.correctBits(n),a=r.convertBoolArrayToByteArray(i),o=r.getEncodedData(i),s=new lr(a,o,null,null);return s.setNumBits(i.length),s},r.highLevelDecode=function(e){return this.getEncodedData(e)},r.getEncodedData=function(e){for(var t=e.length,n=be.UPPER,i=be.UPPER,a="",o=0;o<t;)if(i===be.BINARY){if(t-o<5)break;var s=r.readCode(e,o,5);if(o+=5,s===0){if(t-o<11)break;s=r.readCode(e,o,11)+31,o+=11}for(var f=0;f<s;f++){if(t-o<8){o=t;break}var u=r.readCode(e,o,8);a+=$.castAsNonUtf8Char(u),o+=8}i=n}else{var c=i===be.DIGIT?4:5;if(t-o<c)break;var u=r.readCode(e,o,c);o+=c;var l=r.getCharacter(i,u);l.startsWith("CTRL_")?(n=i,i=r.getTable(l.charAt(5)),l.charAt(6)==="L"&&(n=i)):(a+=l,i=n)}return a},r.getTable=function(e){switch(e){case"L":return be.LOWER;case"P":return be.PUNCT;case"M":return be.MIXED;case"D":return be.DIGIT;case"B":return be.BINARY;case"U":default:return be.UPPER}},r.getCharacter=function(e,t){switch(e){case be.UPPER:return r.UPPER_TABLE[t];case be.LOWER:return r.LOWER_TABLE[t];case be.MIXED:return r.MIXED_TABLE[t];case be.PUNCT:return r.PUNCT_TABLE[t];case be.DIGIT:return r.DIGIT_TABLE[t];default:throw new Vt("Bad table")}},r.prototype.correctBits=function(e){var t,n;this.ddata.getNbLayers()<=2?(n=6,t=Ve.AZTEC_DATA_6):this.ddata.getNbLayers()<=8?(n=8,t=Ve.AZTEC_DATA_8):this.ddata.getNbLayers()<=22?(n=10,t=Ve.AZTEC_DATA_10):(n=12,t=Ve.AZTEC_DATA_12);var i=this.ddata.getNbDatablocks(),a=e.length/n;if(a<i)throw new R;for(var o=e.length%n,s=new Int32Array(a),f=0;f<a;f++,o+=n)s[f]=r.readCode(e,o,n);try{var u=new hr(t);u.decode(s,a-i)}catch(x){throw new R(x)}for(var c=(1<<n)-1,l=0,f=0;f<i;f++){var h=s[f];if(h===0||h===c)throw new R;(h===1||h===c-1)&&l++}for(var d=new Array(i*n-l),v=0,f=0;f<i;f++){var h=s[f];if(h===1||h===c-1)d.fill(h>1,v,v+n-1),v+=n-1;else for(var p=n-1;p>=0;--p)d[v++]=(h&1<<p)!==0}return d},r.prototype.extractBits=function(e){var t=this.ddata.isCompact(),n=this.ddata.getNbLayers(),i=(t?11:14)+n*4,a=new Int32Array(i),o=new Array(this.totalBitsInLayer(n,t));if(t)for(var s=0;s<a.length;s++)a[s]=s;else for(var f=i+1+2*z.truncDivision(z.truncDivision(i,2)-1,15),u=i/2,c=z.truncDivision(f,2),s=0;s<u;s++){var l=s+z.truncDivision(s,15);a[u-s-1]=c-l-1,a[u+s]=c+l+1}for(var s=0,h=0;s<n;s++){for(var d=(n-s)*4+(t?9:12),v=s*2,p=i-1-v,x=0;x<d;x++)for(var y=x*2,w=0;w<2;w++)o[h+y+w]=e.get(a[v+w],a[v+x]),o[h+2*d+y+w]=e.get(a[v+x],a[p-w]),o[h+4*d+y+w]=e.get(a[p-w],a[p-x]),o[h+6*d+y+w]=e.get(a[p-x],a[v+w]);h+=d*8}return o},r.readCode=function(e,t,n){for(var i=0,a=t;a<t+n;a++)i<<=1,e[a]&&(i|=1);return i},r.readByte=function(e,t){var n=e.length-t;return n>=8?r.readCode(e,t,8):r.readCode(e,t,n)<<8-n},r.convertBoolArrayToByteArray=function(e){for(var t=new Uint8Array((e.length+7)/8),n=0;n<t.length;n++)t[n]=r.readByte(e,8*n);return t},r.prototype.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.UPPER_TABLE=["CTRL_PS"," ","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","CTRL_LL","CTRL_ML","CTRL_DL","CTRL_BS"],r.LOWER_TABLE=["CTRL_PS"," ","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","CTRL_US","CTRL_ML","CTRL_DL","CTRL_BS"],r.MIXED_TABLE=["CTRL_PS"," ","","","","","","","\x07","\b","	",`
`,"\v","\f","\r","\x1B","","","","","@","\\","^","_","`","|","~","","CTRL_LL","CTRL_UL","CTRL_PL","CTRL_BS"],r.PUNCT_TABLE=["","\r",`\r
`,". ",", ",": ","!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}","CTRL_UL"],r.DIGIT_TABLE=["CTRL_PS"," ","0","1","2","3","4","5","6","7","8","9",",",".","CTRL_UL","CTRL_US"],r})(),q=(function(){function r(){}return r.round=function(e){return isNaN(e)?0:e<=Number.MIN_SAFE_INTEGER?Number.MIN_SAFE_INTEGER:e>=Number.MAX_SAFE_INTEGER?Number.MAX_SAFE_INTEGER:e+(e<0?-.5:.5)|0},r.distance=function(e,t,n,i){var a=e-n,o=t-i;return Math.sqrt(a*a+o*o)},r.sum=function(e){for(var t=0,n=0,i=e.length;n!==i;n++){var a=e[n];t+=a}return t},r})(),Mr=(function(){function r(){}return r.floatToIntBits=function(e){return e},r.MAX_VALUE=Number.MAX_SAFE_INTEGER,r})(),N=(function(){function r(e,t){this.x=e,this.y=t}return r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.equals=function(e){if(e instanceof r){var t=e;return this.x===t.x&&this.y===t.y}return!1},r.prototype.hashCode=function(){return 31*Mr.floatToIntBits(this.x)+Mr.floatToIntBits(this.y)},r.prototype.toString=function(){return"("+this.x+","+this.y+")"},r.orderBestPatterns=function(e){var t=this.distance(e[0],e[1]),n=this.distance(e[1],e[2]),i=this.distance(e[0],e[2]),a,o,s;if(n>=t&&n>=i?(o=e[0],a=e[1],s=e[2]):i>=n&&i>=t?(o=e[1],a=e[0],s=e[2]):(o=e[2],a=e[0],s=e[1]),this.crossProductZ(a,o,s)<0){var f=a;a=s,s=f}e[0]=a,e[1]=o,e[2]=s},r.distance=function(e,t){return q.distance(e.x,e.y,t.x,t.y)},r.crossProductZ=function(e,t,n){var i=t.x,a=t.y;return(n.x-i)*(e.y-a)-(n.y-a)*(e.x-i)},r})(),Kr=(function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r})(),is=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),as=(function(r){is(e,r);function e(t,n,i,a,o){var s=r.call(this,t,n)||this;return s.compact=i,s.nbDatablocks=a,s.nbLayers=o,s}return e.prototype.getNbLayers=function(){return this.nbLayers},e.prototype.getNbDatablocks=function(){return this.nbDatablocks},e.prototype.isCompact=function(){return this.compact},e})(Kr),Br=(function(){function r(e,t,n,i){this.image=e,this.height=e.getHeight(),this.width=e.getWidth(),t==null&&(t=r.INIT_SIZE),n==null&&(n=e.getWidth()/2|0),i==null&&(i=e.getHeight()/2|0);var a=t/2|0;if(this.leftInit=n-a,this.rightInit=n+a,this.upInit=i-a,this.downInit=i+a,this.upInit<0||this.leftInit<0||this.downInit>=this.height||this.rightInit>=this.width)throw new I}return r.prototype.detect=function(){for(var e=this.leftInit,t=this.rightInit,n=this.upInit,i=this.downInit,a=!1,o=!0,s=!1,f=!1,u=!1,c=!1,l=!1,h=this.width,d=this.height;o;){o=!1;for(var v=!0;(v||!f)&&t<h;)v=this.containsBlackPoint(n,i,t,!1),v?(t++,o=!0,f=!0):f||t++;if(t>=h){a=!0;break}for(var p=!0;(p||!u)&&i<d;)p=this.containsBlackPoint(e,t,i,!0),p?(i++,o=!0,u=!0):u||i++;if(i>=d){a=!0;break}for(var x=!0;(x||!c)&&e>=0;)x=this.containsBlackPoint(n,i,e,!1),x?(e--,o=!0,c=!0):c||e--;if(e<0){a=!0;break}for(var y=!0;(y||!l)&&n>=0;)y=this.containsBlackPoint(e,t,n,!0),y?(n--,o=!0,l=!0):l||n--;if(n<0){a=!0;break}o&&(s=!0)}if(!a&&s){for(var w=t-e,A=null,C=1;A===null&&C<w;C++)A=this.getBlackPointOnSegment(e,i-C,e+C,i);if(A==null)throw new I;for(var O=null,C=1;O===null&&C<w;C++)O=this.getBlackPointOnSegment(e,n+C,e+C,n);if(O==null)throw new I;for(var b=null,C=1;b===null&&C<w;C++)b=this.getBlackPointOnSegment(t,n+C,t-C,n);if(b==null)throw new I;for(var T=null,C=1;T===null&&C<w;C++)T=this.getBlackPointOnSegment(t,i-C,t-C,i);if(T==null)throw new I;return this.centerEdges(T,A,b,O)}else throw new I},r.prototype.getBlackPointOnSegment=function(e,t,n,i){for(var a=q.round(q.distance(e,t,n,i)),o=(n-e)/a,s=(i-t)/a,f=this.image,u=0;u<a;u++){var c=q.round(e+u*o),l=q.round(t+u*s);if(f.get(c,l))return new N(c,l)}return null},r.prototype.centerEdges=function(e,t,n,i){var a=e.getX(),o=e.getY(),s=t.getX(),f=t.getY(),u=n.getX(),c=n.getY(),l=i.getX(),h=i.getY(),d=r.CORR;return a<this.width/2?[new N(l-d,h+d),new N(s+d,f+d),new N(u-d,c-d),new N(a+d,o-d)]:[new N(l+d,h+d),new N(s+d,f-d),new N(u-d,c+d),new N(a-d,o-d)]},r.prototype.containsBlackPoint=function(e,t,n,i){var a=this.image;if(i){for(var o=e;o<=t;o++)if(a.get(o,n))return!0}else for(var s=e;s<=t;s++)if(a.get(n,s))return!0;return!1},r.INIT_SIZE=10,r.CORR=1,r})(),Sn=(function(){function r(){}return r.checkAndNudgePoints=function(e,t){for(var n=e.getWidth(),i=e.getHeight(),a=!0,o=0;o<t.length&&a;o+=2){var s=Math.floor(t[o]),f=Math.floor(t[o+1]);if(s<-1||s>n||f<-1||f>i)throw new I;a=!1,s===-1?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),f===-1?(t[o+1]=0,a=!0):f===i&&(t[o+1]=i-1,a=!0)}a=!0;for(var o=t.length-2;o>=0&&a;o-=2){var s=Math.floor(t[o]),f=Math.floor(t[o+1]);if(s<-1||s>n||f<-1||f>i)throw new I;a=!1,s===-1?(t[o]=0,a=!0):s===n&&(t[o]=n-1,a=!0),f===-1?(t[o+1]=0,a=!0):f===i&&(t[o+1]=i-1,a=!0)}},r})(),Ri=(function(){function r(e,t,n,i,a,o,s,f,u){this.a11=e,this.a21=t,this.a31=n,this.a12=i,this.a22=a,this.a32=o,this.a13=s,this.a23=f,this.a33=u}return r.quadrilateralToQuadrilateral=function(e,t,n,i,a,o,s,f,u,c,l,h,d,v,p,x){var y=r.quadrilateralToSquare(e,t,n,i,a,o,s,f),w=r.squareToQuadrilateral(u,c,l,h,d,v,p,x);return w.times(y)},r.prototype.transformPoints=function(e){for(var t=e.length,n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,f=this.a23,u=this.a31,c=this.a32,l=this.a33,h=0;h<t;h+=2){var d=e[h],v=e[h+1],p=a*d+f*v+l;e[h]=(n*d+o*v+u)/p,e[h+1]=(i*d+s*v+c)/p}},r.prototype.transformPointsWithValues=function(e,t){for(var n=this.a11,i=this.a12,a=this.a13,o=this.a21,s=this.a22,f=this.a23,u=this.a31,c=this.a32,l=this.a33,h=e.length,d=0;d<h;d++){var v=e[d],p=t[d],x=a*v+f*p+l;e[d]=(n*v+o*p+u)/x,t[d]=(i*v+s*p+c)/x}},r.squareToQuadrilateral=function(e,t,n,i,a,o,s,f){var u=e-n+a-s,c=t-i+o-f;if(u===0&&c===0)return new r(n-e,a-n,e,i-t,o-i,t,0,0,1);var l=n-a,h=s-a,d=i-o,v=f-o,p=l*v-h*d,x=(u*v-h*c)/p,y=(l*c-u*d)/p;return new r(n-e+x*n,s-e+y*s,e,i-t+x*i,f-t+y*f,t,x,y,1)},r.quadrilateralToSquare=function(e,t,n,i,a,o,s,f){return r.squareToQuadrilateral(e,t,n,i,a,o,s,f).buildAdjoint()},r.prototype.buildAdjoint=function(){return new r(this.a22*this.a33-this.a23*this.a32,this.a23*this.a31-this.a21*this.a33,this.a21*this.a32-this.a22*this.a31,this.a13*this.a32-this.a12*this.a33,this.a11*this.a33-this.a13*this.a31,this.a12*this.a31-this.a11*this.a32,this.a12*this.a23-this.a13*this.a22,this.a13*this.a21-this.a11*this.a23,this.a11*this.a22-this.a12*this.a21)},r.prototype.times=function(e){return new r(this.a11*e.a11+this.a21*e.a12+this.a31*e.a13,this.a11*e.a21+this.a21*e.a22+this.a31*e.a23,this.a11*e.a31+this.a21*e.a32+this.a31*e.a33,this.a12*e.a11+this.a22*e.a12+this.a32*e.a13,this.a12*e.a21+this.a22*e.a22+this.a32*e.a23,this.a12*e.a31+this.a22*e.a32+this.a32*e.a33,this.a13*e.a11+this.a23*e.a12+this.a33*e.a13,this.a13*e.a21+this.a23*e.a22+this.a33*e.a23,this.a13*e.a31+this.a23*e.a32+this.a33*e.a33)},r})(),os=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),ss=(function(r){os(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.sampleGrid=function(t,n,i,a,o,s,f,u,c,l,h,d,v,p,x,y,w,A,C){var O=Ri.quadrilateralToQuadrilateral(a,o,s,f,u,c,l,h,d,v,p,x,y,w,A,C);return this.sampleGridWithTransform(t,n,i,O)},e.prototype.sampleGridWithTransform=function(t,n,i,a){if(n<=0||i<=0)throw new I;for(var o=new it(n,i),s=new Float32Array(2*n),f=0;f<i;f++){for(var u=s.length,c=f+.5,l=0;l<u;l+=2)s[l]=l/2+.5,s[l+1]=c;a.transformPoints(s),Sn.checkAndNudgePoints(t,s);try{for(var l=0;l<u;l+=2)t.get(Math.floor(s[l]),Math.floor(s[l+1]))&&o.set(l/2,f)}catch{throw new I}}return o},e})(Sn),qr=(function(){function r(){}return r.setGridSampler=function(e){r.gridSampler=e},r.getInstance=function(){return r.gridSampler},r.gridSampler=new ss,r})(),He=(function(){function r(e,t){this.x=e,this.y=t}return r.prototype.toResultPoint=function(){return new N(this.getX(),this.getY())},r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r})(),fs=(function(){function r(e){this.EXPECTED_CORNER_BITS=new Int32Array([3808,476,2107,1799]),this.image=e}return r.prototype.detect=function(){return this.detectMirror(!1)},r.prototype.detectMirror=function(e){var t=this.getMatrixCenter(),n=this.getBullsEyeCorners(t);if(e){var i=n[0];n[0]=n[2],n[2]=i}this.extractParameters(n);var a=this.sampleGrid(this.image,n[this.shift%4],n[(this.shift+1)%4],n[(this.shift+2)%4],n[(this.shift+3)%4]),o=this.getMatrixCornerPoints(n);return new as(a,o,this.compact,this.nbDataBlocks,this.nbLayers)},r.prototype.extractParameters=function(e){if(!this.isValidPoint(e[0])||!this.isValidPoint(e[1])||!this.isValidPoint(e[2])||!this.isValidPoint(e[3]))throw new I;var t=2*this.nbCenterLayers,n=new Int32Array([this.sampleLine(e[0],e[1],t),this.sampleLine(e[1],e[2],t),this.sampleLine(e[2],e[3],t),this.sampleLine(e[3],e[0],t)]);this.shift=this.getRotation(n,t);for(var i=0,a=0;a<4;a++){var o=n[(this.shift+a)%4];this.compact?(i<<=7,i+=o>>1&127):(i<<=10,i+=(o>>2&992)+(o>>1&31))}var s=this.getCorrectedParameterData(i,this.compact);this.compact?(this.nbLayers=(s>>6)+1,this.nbDataBlocks=(s&63)+1):(this.nbLayers=(s>>11)+1,this.nbDataBlocks=(s&2047)+1)},r.prototype.getRotation=function(e,t){var n=0;e.forEach(function(a,o,s){var f=(a>>t-2<<1)+(a&1);n=(n<<3)+f}),n=((n&1)<<11)+(n>>1);for(var i=0;i<4;i++)if(z.bitCount(n^this.EXPECTED_CORNER_BITS[i])<=2)return i;throw new I},r.prototype.getCorrectedParameterData=function(e,t){var n,i;t?(n=7,i=2):(n=10,i=4);for(var a=n-i,o=new Int32Array(n),s=n-1;s>=0;--s)o[s]=e&15,e>>=4;try{var f=new hr(Ve.AZTEC_PARAM);f.decode(o,a)}catch{throw new I}for(var u=0,s=0;s<i;s++)u=(u<<4)+o[s];return u},r.prototype.getBullsEyeCorners=function(e){var t=e,n=e,i=e,a=e,o=!0;for(this.nbCenterLayers=1;this.nbCenterLayers<9;this.nbCenterLayers++){var s=this.getFirstDifferent(t,o,1,-1),f=this.getFirstDifferent(n,o,1,1),u=this.getFirstDifferent(i,o,-1,1),c=this.getFirstDifferent(a,o,-1,-1);if(this.nbCenterLayers>2){var l=this.distancePoint(c,s)*this.nbCenterLayers/(this.distancePoint(a,t)*(this.nbCenterLayers+2));if(l<.75||l>1.25||!this.isWhiteOrBlackRectangle(s,f,u,c))break}t=s,n=f,i=u,a=c,o=!o}if(this.nbCenterLayers!==5&&this.nbCenterLayers!==7)throw new I;this.compact=this.nbCenterLayers===5;var h=new N(t.getX()+.5,t.getY()-.5),d=new N(n.getX()+.5,n.getY()+.5),v=new N(i.getX()-.5,i.getY()+.5),p=new N(a.getX()-.5,a.getY()-.5);return this.expandSquare([h,d,v,p],2*this.nbCenterLayers-3,2*this.nbCenterLayers)},r.prototype.getMatrixCenter=function(){var e,t,n,i;try{var a=new Br(this.image).detect();e=a[0],t=a[1],n=a[2],i=a[3]}catch{var o=this.image.getWidth()/2,s=this.image.getHeight()/2;e=this.getFirstDifferent(new He(o+7,s-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new He(o+7,s+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new He(o-7,s+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new He(o-7,s-7),!1,-1,-1).toResultPoint()}var f=q.round((e.getX()+i.getX()+t.getX()+n.getX())/4),u=q.round((e.getY()+i.getY()+t.getY()+n.getY())/4);try{var a=new Br(this.image,15,f,u).detect();e=a[0],t=a[1],n=a[2],i=a[3]}catch{e=this.getFirstDifferent(new He(f+7,u-7),!1,1,-1).toResultPoint(),t=this.getFirstDifferent(new He(f+7,u+7),!1,1,1).toResultPoint(),n=this.getFirstDifferent(new He(f-7,u+7),!1,-1,1).toResultPoint(),i=this.getFirstDifferent(new He(f-7,u-7),!1,-1,-1).toResultPoint()}return f=q.round((e.getX()+i.getX()+t.getX()+n.getX())/4),u=q.round((e.getY()+i.getY()+t.getY()+n.getY())/4),new He(f,u)},r.prototype.getMatrixCornerPoints=function(e){return this.expandSquare(e,2*this.nbCenterLayers,this.getDimension())},r.prototype.sampleGrid=function(e,t,n,i,a){var o=qr.getInstance(),s=this.getDimension(),f=s/2-this.nbCenterLayers,u=s/2+this.nbCenterLayers;return o.sampleGrid(e,s,s,f,f,u,f,u,u,f,u,t.getX(),t.getY(),n.getX(),n.getY(),i.getX(),i.getY(),a.getX(),a.getY())},r.prototype.sampleLine=function(e,t,n){for(var i=0,a=this.distanceResultPoint(e,t),o=a/n,s=e.getX(),f=e.getY(),u=o*(t.getX()-e.getX())/a,c=o*(t.getY()-e.getY())/a,l=0;l<n;l++)this.image.get(q.round(s+l*u),q.round(f+l*c))&&(i|=1<<n-l-1);return i},r.prototype.isWhiteOrBlackRectangle=function(e,t,n,i){var a=3;e=new He(e.getX()-a,e.getY()+a),t=new He(t.getX()-a,t.getY()-a),n=new He(n.getX()+a,n.getY()-a),i=new He(i.getX()+a,i.getY()+a);var o=this.getColor(i,e);if(o===0)return!1;var s=this.getColor(e,t);return s!==o||(s=this.getColor(t,n),s!==o)?!1:(s=this.getColor(n,i),s===o)},r.prototype.getColor=function(e,t){for(var n=this.distancePoint(e,t),i=(t.getX()-e.getX())/n,a=(t.getY()-e.getY())/n,o=0,s=e.getX(),f=e.getY(),u=this.image.get(e.getX(),e.getY()),c=Math.ceil(n),l=0;l<c;l++)s+=i,f+=a,this.image.get(q.round(s),q.round(f))!==u&&o++;var h=o/n;return h>.1&&h<.9?0:h<=.1===u?1:-1},r.prototype.getFirstDifferent=function(e,t,n,i){for(var a=e.getX()+n,o=e.getY()+i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n,o+=i;for(a-=n,o-=i;this.isValid(a,o)&&this.image.get(a,o)===t;)a+=n;for(a-=n;this.isValid(a,o)&&this.image.get(a,o)===t;)o+=i;return o-=i,new He(a,o)},r.prototype.expandSquare=function(e,t,n){var i=n/(2*t),a=e[0].getX()-e[2].getX(),o=e[0].getY()-e[2].getY(),s=(e[0].getX()+e[2].getX())/2,f=(e[0].getY()+e[2].getY())/2,u=new N(s+i*a,f+i*o),c=new N(s-i*a,f-i*o);a=e[1].getX()-e[3].getX(),o=e[1].getY()-e[3].getY(),s=(e[1].getX()+e[3].getX())/2,f=(e[1].getY()+e[3].getY())/2;var l=new N(s+i*a,f+i*o),h=new N(s-i*a,f-i*o),d=[u,l,c,h];return d},r.prototype.isValid=function(e,t){return e>=0&&e<this.image.getWidth()&&t>0&&t<this.image.getHeight()},r.prototype.isValidPoint=function(e){var t=q.round(e.getX()),n=q.round(e.getY());return this.isValid(t,n)},r.prototype.distancePoint=function(e,t){return q.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.distanceResultPoint=function(e,t){return q.distance(e.getX(),e.getY(),t.getX(),t.getY())},r.prototype.getDimension=function(){return this.compact?4*this.nbLayers+11:this.nbLayers<=4?4*this.nbLayers+15:4*this.nbLayers+2*(z.truncDivision(this.nbLayers-4,8)+1)+15},r})(),Fr=(function(){function r(){}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n=null,i=new fs(e.getBlackMatrix()),a=null,o=null;try{var s=i.detectMirror(!1);a=s.getPoints(),this.reportFoundResultPoints(t,a),o=new mn().decode(s)}catch(l){n=l}if(o==null)try{var s=i.detectMirror(!0);a=s.getPoints(),this.reportFoundResultPoints(t,a),o=new mn().decode(s)}catch(l){throw n??l}var f=new Pe(o.getText(),o.getRawBytes(),o.getNumBits(),a,F.AZTEC,se.currentTimeMillis()),u=o.getByteSegments();u!=null&&f.putMetadata(Re.BYTE_SEGMENTS,u);var c=o.getECLevel();return c!=null&&f.putMetadata(Re.ERROR_CORRECTION_LEVEL,c),f},r.prototype.reportFoundResultPoints=function(e,t){if(e!=null){var n=e.get(le.NEED_RESULT_POINT_CALLBACK);n!=null&&t.forEach(function(i,a,o){n.foundPossibleResultPoint(i)})}},r.prototype.reset=function(){},r})(),us=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){us(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new Fr,t)||this}return e})(bt);var Ae=(function(){function r(){}return r.prototype.decode=function(e,t){try{return this.doDecode(e,t)}catch{var n=t&&t.get(le.TRY_HARDER)===!0;if(n&&e.isRotateSupported()){var i=e.rotateCounterClockwise(),a=this.doDecode(i,t),o=a.getResultMetadata(),s=270;o!==null&&o.get(Re.ORIENTATION)===!0&&(s=s+o.get(Re.ORIENTATION)%360),a.putMetadata(Re.ORIENTATION,s);var f=a.getResultPoints();if(f!==null)for(var u=i.getHeight(),c=0;c<f.length;c++)f[c]=new N(u-f[c].getY()-1,f[c].getX());return a}else throw new I}},r.prototype.reset=function(){},r.prototype.doDecode=function(e,t){var n=e.getWidth(),i=e.getHeight(),a=new Se(n),o=t&&t.get(le.TRY_HARDER)===!0,s=Math.max(1,i>>(o?8:5)),f;o?f=i:f=15;for(var u=Math.trunc(i/2),c=0;c<f;c++){var l=Math.trunc((c+1)/2),h=(c&1)===0,d=u+s*(h?l:-l);if(d<0||d>=i)break;try{a=e.getBlackRow(d,a)}catch{continue}for(var v=function(w){if(w===1&&(a.reverse(),t&&t.get(le.NEED_RESULT_POINT_CALLBACK)===!0)){var A=new Map;t.forEach(function(b,T){return A.set(T,b)}),A.delete(le.NEED_RESULT_POINT_CALLBACK),t=A}try{var C=p.decodeRow(d,a,t);if(w===1){C.putMetadata(Re.ORIENTATION,180);var O=C.getResultPoints();O!==null&&(O[0]=new N(n-O[0].getX()-1,O[0].getY()),O[1]=new N(n-O[1].getX()-1,O[1].getY()))}return{value:C}}catch{}},p=this,x=0;x<2;x++){var y=v(x);if(typeof y=="object")return y.value}}throw new I},r.recordPattern=function(e,t,n){for(var i=n.length,a=0;a<i;a++)n[a]=0;var o=e.getSize();if(t>=o)throw new I;for(var s=!e.get(t),f=0,u=t;u<o;){if(e.get(u)!==s)n[f]++;else{if(++f===i)break;n[f]=1,s=!s}u++}if(!(f===i||f===i-1&&u===o))throw new I},r.recordPatternInReverse=function(e,t,n){for(var i=n.length,a=e.get(t);t>0&&i>=0;)e.get(--t)!==a&&(i--,a=!a);if(i>=0)throw new I;r.recordPattern(e,t+1,n)},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return Number.POSITIVE_INFINITY;var f=a/o;n*=f;for(var u=0,c=0;c<i;c++){var l=e[c],h=t[c]*f,d=l>h?l-h:h-l;if(d>n)return Number.POSITIVE_INFINITY;u+=d}return u/a},r})(),cs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),In=(function(r){cs(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.findStartPattern=function(t){for(var n=t.getSize(),i=t.getNextSet(0),a=0,o=Int32Array.from([0,0,0,0,0,0]),s=i,f=!1,u=6,c=i;c<n;c++)if(t.get(c)!==f)o[a]++;else{if(a===u-1){for(var l=e.MAX_AVG_VARIANCE,h=-1,d=e.CODE_START_A;d<=e.CODE_START_C;d++){var v=Ae.patternMatchVariance(o,e.CODE_PATTERNS[d],e.MAX_INDIVIDUAL_VARIANCE);v<l&&(l=v,h=d)}if(h>=0&&t.isRange(Math.max(0,s-(c-s)/2),s,!1))return Int32Array.from([s,c,h]);s+=o[0]+o[1],o=o.slice(2,o.length),o[a-1]=0,o[a]=0,a--}else a++;o[a]=1,f=!f}throw new I},e.decodeCode=function(t,n,i){Ae.recordPattern(t,i,n);for(var a=e.MAX_AVG_VARIANCE,o=-1,s=0;s<e.CODE_PATTERNS.length;s++){var f=e.CODE_PATTERNS[s],u=this.patternMatchVariance(n,f,e.MAX_INDIVIDUAL_VARIANCE);u<a&&(a=u,o=s)}if(o>=0)return o;throw new I},e.prototype.decodeRow=function(t,n,i){var a=i&&i.get(le.ASSUME_GS1)===!0,o=e.findStartPattern(n),s=o[2],f=0,u=new Uint8Array(20);u[f++]=s;var c;switch(s){case e.CODE_START_A:c=e.CODE_CODE_A;break;case e.CODE_START_B:c=e.CODE_CODE_B;break;case e.CODE_START_C:c=e.CODE_CODE_C;break;default:throw new R}for(var l=!1,h=!1,d="",v=o[0],p=o[1],x=Int32Array.from([0,0,0,0,0,0]),y=0,w=0,A=s,C=0,O=!0,b=!1,T=!1;!l;){var D=h;switch(h=!1,y=w,w=e.decodeCode(n,x,p),u[f++]=w,w!==e.CODE_STOP&&(O=!0),w!==e.CODE_STOP&&(C++,A+=C*w),v=p,p+=x.reduce(function(ot,ke){return ot+ke},0),w){case e.CODE_START_A:case e.CODE_START_B:case e.CODE_START_C:throw new R}switch(c){case e.CODE_CODE_A:if(w<64)T===b?d+=String.fromCharCode(32+w):d+=String.fromCharCode(32+w+128),T=!1;else if(w<96)T===b?d+=String.fromCharCode(w-64):d+=String.fromCharCode(w+64),T=!1;else switch(w!==e.CODE_STOP&&(O=!1),w){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_A:!b&&T?(b=!0,T=!1):b&&T?(b=!1,T=!1):T=!0;break;case e.CODE_SHIFT:h=!0,c=e.CODE_CODE_B;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:l=!0;break}break;case e.CODE_CODE_B:if(w<96)T===b?d+=String.fromCharCode(32+w):d+=String.fromCharCode(32+w+128),T=!1;else switch(w!==e.CODE_STOP&&(O=!1),w){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_FNC_2:case e.CODE_FNC_3:break;case e.CODE_FNC_4_B:!b&&T?(b=!0,T=!1):b&&T?(b=!1,T=!1):T=!0;break;case e.CODE_SHIFT:h=!0,c=e.CODE_CODE_A;break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_C:c=e.CODE_CODE_C;break;case e.CODE_STOP:l=!0;break}break;case e.CODE_CODE_C:if(w<100)w<10&&(d+="0"),d+=w;else switch(w!==e.CODE_STOP&&(O=!1),w){case e.CODE_FNC_1:a&&(d.length===0?d+="]C1":d+="");break;case e.CODE_CODE_A:c=e.CODE_CODE_A;break;case e.CODE_CODE_B:c=e.CODE_CODE_B;break;case e.CODE_STOP:l=!0;break}break}D&&(c=c===e.CODE_CODE_A?e.CODE_CODE_B:e.CODE_CODE_A)}var B=p-v;if(p=n.getNextUnset(p),!n.isRange(p,Math.min(n.getSize(),p+(p-v)/2),!1))throw new I;if(A-=C*y,A%103!==y)throw new me;var M=d.length;if(M===0)throw new I;M>0&&O&&(c===e.CODE_CODE_C?d=d.substring(0,M-2):d=d.substring(0,M-1));for(var ae=(o[1]+o[0])/2,W=v+B/2,Q=u.length,he=new Uint8Array(Q),Me=0;Me<Q;Me++)he[Me]=u[Me];var Ie=[new N(ae,t),new N(W,t)];return new Pe(d,he,0,Ie,F.CODE_128,new Date().getTime())},e.CODE_PATTERNS=[Int32Array.from([2,1,2,2,2,2]),Int32Array.from([2,2,2,1,2,2]),Int32Array.from([2,2,2,2,2,1]),Int32Array.from([1,2,1,2,2,3]),Int32Array.from([1,2,1,3,2,2]),Int32Array.from([1,3,1,2,2,2]),Int32Array.from([1,2,2,2,1,3]),Int32Array.from([1,2,2,3,1,2]),Int32Array.from([1,3,2,2,1,2]),Int32Array.from([2,2,1,2,1,3]),Int32Array.from([2,2,1,3,1,2]),Int32Array.from([2,3,1,2,1,2]),Int32Array.from([1,1,2,2,3,2]),Int32Array.from([1,2,2,1,3,2]),Int32Array.from([1,2,2,2,3,1]),Int32Array.from([1,1,3,2,2,2]),Int32Array.from([1,2,3,1,2,2]),Int32Array.from([1,2,3,2,2,1]),Int32Array.from([2,2,3,2,1,1]),Int32Array.from([2,2,1,1,3,2]),Int32Array.from([2,2,1,2,3,1]),Int32Array.from([2,1,3,2,1,2]),Int32Array.from([2,2,3,1,1,2]),Int32Array.from([3,1,2,1,3,1]),Int32Array.from([3,1,1,2,2,2]),Int32Array.from([3,2,1,1,2,2]),Int32Array.from([3,2,1,2,2,1]),Int32Array.from([3,1,2,2,1,2]),Int32Array.from([3,2,2,1,1,2]),Int32Array.from([3,2,2,2,1,1]),Int32Array.from([2,1,2,1,2,3]),Int32Array.from([2,1,2,3,2,1]),Int32Array.from([2,3,2,1,2,1]),Int32Array.from([1,1,1,3,2,3]),Int32Array.from([1,3,1,1,2,3]),Int32Array.from([1,3,1,3,2,1]),Int32Array.from([1,1,2,3,1,3]),Int32Array.from([1,3,2,1,1,3]),Int32Array.from([1,3,2,3,1,1]),Int32Array.from([2,1,1,3,1,3]),Int32Array.from([2,3,1,1,1,3]),Int32Array.from([2,3,1,3,1,1]),Int32Array.from([1,1,2,1,3,3]),Int32Array.from([1,1,2,3,3,1]),Int32Array.from([1,3,2,1,3,1]),Int32Array.from([1,1,3,1,2,3]),Int32Array.from([1,1,3,3,2,1]),Int32Array.from([1,3,3,1,2,1]),Int32Array.from([3,1,3,1,2,1]),Int32Array.from([2,1,1,3,3,1]),Int32Array.from([2,3,1,1,3,1]),Int32Array.from([2,1,3,1,1,3]),Int32Array.from([2,1,3,3,1,1]),Int32Array.from([2,1,3,1,3,1]),Int32Array.from([3,1,1,1,2,3]),Int32Array.from([3,1,1,3,2,1]),Int32Array.from([3,3,1,1,2,1]),Int32Array.from([3,1,2,1,1,3]),Int32Array.from([3,1,2,3,1,1]),Int32Array.from([3,3,2,1,1,1]),Int32Array.from([3,1,4,1,1,1]),Int32Array.from([2,2,1,4,1,1]),Int32Array.from([4,3,1,1,1,1]),Int32Array.from([1,1,1,2,2,4]),Int32Array.from([1,1,1,4,2,2]),Int32Array.from([1,2,1,1,2,4]),Int32Array.from([1,2,1,4,2,1]),Int32Array.from([1,4,1,1,2,2]),Int32Array.from([1,4,1,2,2,1]),Int32Array.from([1,1,2,2,1,4]),Int32Array.from([1,1,2,4,1,2]),Int32Array.from([1,2,2,1,1,4]),Int32Array.from([1,2,2,4,1,1]),Int32Array.from([1,4,2,1,1,2]),Int32Array.from([1,4,2,2,1,1]),Int32Array.from([2,4,1,2,1,1]),Int32Array.from([2,2,1,1,1,4]),Int32Array.from([4,1,3,1,1,1]),Int32Array.from([2,4,1,1,1,2]),Int32Array.from([1,3,4,1,1,1]),Int32Array.from([1,1,1,2,4,2]),Int32Array.from([1,2,1,1,4,2]),Int32Array.from([1,2,1,2,4,1]),Int32Array.from([1,1,4,2,1,2]),Int32Array.from([1,2,4,1,1,2]),Int32Array.from([1,2,4,2,1,1]),Int32Array.from([4,1,1,2,1,2]),Int32Array.from([4,2,1,1,1,2]),Int32Array.from([4,2,1,2,1,1]),Int32Array.from([2,1,2,1,4,1]),Int32Array.from([2,1,4,1,2,1]),Int32Array.from([4,1,2,1,2,1]),Int32Array.from([1,1,1,1,4,3]),Int32Array.from([1,1,1,3,4,1]),Int32Array.from([1,3,1,1,4,1]),Int32Array.from([1,1,4,1,1,3]),Int32Array.from([1,1,4,3,1,1]),Int32Array.from([4,1,1,1,1,3]),Int32Array.from([4,1,1,3,1,1]),Int32Array.from([1,1,3,1,4,1]),Int32Array.from([1,1,4,1,3,1]),Int32Array.from([3,1,1,1,4,1]),Int32Array.from([4,1,1,1,3,1]),Int32Array.from([2,1,1,4,1,2]),Int32Array.from([2,1,1,2,1,4]),Int32Array.from([2,1,1,2,3,2]),Int32Array.from([2,3,3,1,1,1,2])],e.MAX_AVG_VARIANCE=.25,e.MAX_INDIVIDUAL_VARIANCE=.7,e.CODE_SHIFT=98,e.CODE_CODE_C=99,e.CODE_CODE_B=100,e.CODE_CODE_A=101,e.CODE_FNC_1=102,e.CODE_FNC_2=97,e.CODE_FNC_3=96,e.CODE_FNC_4_A=101,e.CODE_FNC_4_B=100,e.CODE_START_A=103,e.CODE_START_B=104,e.CODE_START_C=105,e.CODE_STOP=106,e})(Ae),ls=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),xr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},On=(function(r){ls(e,r);function e(t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var i=r.call(this)||this;return i.usingCheckDigit=t,i.extendedMode=n,i.decodeRowResult="",i.counters=new Int32Array(9),i}return e.prototype.decodeRow=function(t,n,i){var a,o,s,f,u=this.counters;u.fill(0),this.decodeRowResult="";var c=e.findAsteriskPattern(n,u),l=n.getNextSet(c[1]),h=n.getSize(),d,v;do{e.recordPattern(n,l,u);var p=e.toNarrowWidePattern(u);if(p<0)throw new I;d=e.patternToChar(p),this.decodeRowResult+=d,v=l;try{for(var x=(a=void 0,xr(u)),y=x.next();!y.done;y=x.next()){var w=y.value;l+=w}}catch(Q){a={error:Q}}finally{try{y&&!y.done&&(o=x.return)&&o.call(x)}finally{if(a)throw a.error}}l=n.getNextSet(l)}while(d!=="*");this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var A=0;try{for(var C=xr(u),O=C.next();!O.done;O=C.next()){var w=O.value;A+=w}}catch(Q){s={error:Q}}finally{try{O&&!O.done&&(f=C.return)&&f.call(C)}finally{if(s)throw s.error}}var b=l-v-A;if(l!==h&&b*2<A)throw new I;if(this.usingCheckDigit){for(var T=this.decodeRowResult.length-1,D=0,B=0;B<T;B++)D+=e.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(B));if(this.decodeRowResult.charAt(T)!==e.ALPHABET_STRING.charAt(D%43))throw new me;this.decodeRowResult=this.decodeRowResult.substring(0,T)}if(this.decodeRowResult.length===0)throw new I;var M;this.extendedMode?M=e.decodeExtended(this.decodeRowResult):M=this.decodeRowResult;var ae=(c[1]+c[0])/2,W=v+A/2;return new Pe(M,null,0,[new N(ae,t),new N(W,t)],F.CODE_39,new Date().getTime())},e.findAsteriskPattern=function(t,n){for(var i=t.getSize(),a=t.getNextSet(0),o=0,s=a,f=!1,u=n.length,c=a;c<i;c++)if(t.get(c)!==f)n[o]++;else{if(o===u-1){if(this.toNarrowWidePattern(n)===e.ASTERISK_ENCODING&&t.isRange(Math.max(0,s-Math.floor((c-s)/2)),s,!1))return[s,c];s+=n[0]+n[1],n.copyWithin(0,2,2+o-1),n[o-1]=0,n[o]=0,o--}else o++;n[o]=1,f=!f}throw new I},e.toNarrowWidePattern=function(t){var n,i,a=t.length,o=0,s;do{var f=2147483647;try{for(var u=(n=void 0,xr(t)),c=u.next();!c.done;c=u.next()){var l=c.value;l<f&&l>o&&(f=l)}}catch(p){n={error:p}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(n)throw n.error}}o=f,s=0;for(var h=0,d=0,v=0;v<a;v++){var l=t[v];l>o&&(d|=1<<a-1-v,s++,h+=l)}if(s===3){for(var v=0;v<a&&s>0;v++){var l=t[v];if(l>o&&(s--,l*2>=h))return-1}return d}}while(s>3);return-1},e.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);if(t===e.ASTERISK_ENCODING)return"*";throw new I},e.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if(o==="+"||o==="$"||o==="%"||o==="/"){var s=t.charAt(a+1),f="\0";switch(o){case"+":if(s>="A"&&s<="Z")f=String.fromCharCode(s.charCodeAt(0)+32);else throw new R;break;case"$":if(s>="A"&&s<="Z")f=String.fromCharCode(s.charCodeAt(0)-64);else throw new R;break;case"%":if(s>="A"&&s<="E")f=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")f=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")f=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")f=String.fromCharCode(s.charCodeAt(0)+43);else if(s==="U")f="\0";else if(s==="V")f="@";else if(s==="W")f="`";else if(s==="X"||s==="Y"||s==="Z")f="";else throw new R;break;case"/":if(s>="A"&&s<="O")f=String.fromCharCode(s.charCodeAt(0)-32);else if(s==="Z")f=":";else throw new R;break}i+=f,a++}else i+=o}return i},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%",e.CHARACTER_ENCODINGS=[52,289,97,352,49,304,112,37,292,100,265,73,328,25,280,88,13,268,76,28,259,67,322,19,274,82,7,262,70,22,385,193,448,145,400,208,133,388,196,168,162,138,42],e.ASTERISK_ENCODING=148,e})(Ae),hs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),yr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},bn=(function(r){hs(e,r);function e(){var t=r.call(this)||this;return t.decodeRowResult="",t.counters=new Int32Array(6),t}return e.prototype.decodeRow=function(t,n,i){var a,o,s,f,u=this.findAsteriskPattern(n),c=n.getNextSet(u[1]),l=n.getSize(),h=this.counters;h.fill(0),this.decodeRowResult="";var d,v;do{e.recordPattern(n,c,h);var p=this.toPattern(h);if(p<0)throw new I;d=this.patternToChar(p),this.decodeRowResult+=d,v=c;try{for(var x=(a=void 0,yr(h)),y=x.next();!y.done;y=x.next()){var w=y.value;c+=w}}catch(B){a={error:B}}finally{try{y&&!y.done&&(o=x.return)&&o.call(x)}finally{if(a)throw a.error}}c=n.getNextSet(c)}while(d!=="*");this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-1);var A=0;try{for(var C=yr(h),O=C.next();!O.done;O=C.next()){var w=O.value;A+=w}}catch(B){s={error:B}}finally{try{O&&!O.done&&(f=C.return)&&f.call(C)}finally{if(s)throw s.error}}if(c===l||!n.get(c))throw new I;if(this.decodeRowResult.length<2)throw new I;this.checkChecksums(this.decodeRowResult),this.decodeRowResult=this.decodeRowResult.substring(0,this.decodeRowResult.length-2);var b=this.decodeExtended(this.decodeRowResult),T=(u[1]+u[0])/2,D=v+A/2;return new Pe(b,null,0,[new N(T,t),new N(D,t)],F.CODE_93,new Date().getTime())},e.prototype.findAsteriskPattern=function(t){var n=t.getSize(),i=t.getNextSet(0);this.counters.fill(0);for(var a=this.counters,o=i,s=!1,f=a.length,u=0,c=i;c<n;c++)if(t.get(c)!==s)a[u]++;else{if(u===f-1){if(this.toPattern(a)===e.ASTERISK_ENCODING)return new Int32Array([o,c]);o+=a[0]+a[1],a.copyWithin(0,2,2+u-1),a[u-1]=0,a[u]=0,u--}else u++;a[u]=1,s=!s}throw new I},e.prototype.toPattern=function(t){var n,i,a=0;try{for(var o=yr(t),s=o.next();!s.done;s=o.next()){var f=s.value;a+=f}}catch(v){n={error:v}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}for(var u=0,c=t.length,l=0;l<c;l++){var h=Math.round(t[l]*9/a);if(h<1||h>4)return-1;if((l&1)===0)for(var d=0;d<h;d++)u=u<<1|1;else u<<=h}return u},e.prototype.patternToChar=function(t){for(var n=0;n<e.CHARACTER_ENCODINGS.length;n++)if(e.CHARACTER_ENCODINGS[n]===t)return e.ALPHABET_STRING.charAt(n);throw new I},e.prototype.decodeExtended=function(t){for(var n=t.length,i="",a=0;a<n;a++){var o=t.charAt(a);if(o>="a"&&o<="d"){if(a>=n-1)throw new R;var s=t.charAt(a+1),f="\0";switch(o){case"d":if(s>="A"&&s<="Z")f=String.fromCharCode(s.charCodeAt(0)+32);else throw new R;break;case"a":if(s>="A"&&s<="Z")f=String.fromCharCode(s.charCodeAt(0)-64);else throw new R;break;case"b":if(s>="A"&&s<="E")f=String.fromCharCode(s.charCodeAt(0)-38);else if(s>="F"&&s<="J")f=String.fromCharCode(s.charCodeAt(0)-11);else if(s>="K"&&s<="O")f=String.fromCharCode(s.charCodeAt(0)+16);else if(s>="P"&&s<="T")f=String.fromCharCode(s.charCodeAt(0)+43);else if(s==="U")f="\0";else if(s==="V")f="@";else if(s==="W")f="`";else if(s>="X"&&s<="Z")f="";else throw new R;break;case"c":if(s>="A"&&s<="O")f=String.fromCharCode(s.charCodeAt(0)-32);else if(s==="Z")f=":";else throw new R;break}i+=f,a++}else i+=o}return i},e.prototype.checkChecksums=function(t){var n=t.length;this.checkOneChecksum(t,n-2,20),this.checkOneChecksum(t,n-1,15)},e.prototype.checkOneChecksum=function(t,n,i){for(var a=1,o=0,s=n-1;s>=0;s--)o+=a*e.ALPHABET_STRING.indexOf(t.charAt(s)),++a>i&&(a=1);if(t.charAt(n)!==e.ALPHABET_STRING[o%47])throw new me},e.ALPHABET_STRING="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*",e.CHARACTER_ENCODINGS=[276,328,324,322,296,292,290,336,274,266,424,420,418,404,402,394,360,356,354,308,282,344,332,326,300,278,436,434,428,422,406,410,364,358,310,314,302,468,466,458,366,374,430,294,474,470,306,350],e.ASTERISK_ENCODING=e.CHARACTER_ENCODINGS[47],e})(Ae),ds=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),vs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Tn=(function(r){ds(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.narrowLineWidth=-1,t}return e.prototype.decodeRow=function(t,n,i){var a,o,s=this.decodeStart(n),f=this.decodeEnd(n),u=new X;e.decodeMiddle(n,s[1],f[0],u);var c=u.toString(),l=null;i!=null&&(l=i.get(le.ALLOWED_LENGTHS)),l==null&&(l=e.DEFAULT_ALLOWED_LENGTHS);var h=c.length,d=!1,v=0;try{for(var p=vs(l),x=p.next();!x.done;x=p.next()){var y=x.value;if(h===y){d=!0;break}y>v&&(v=y)}}catch(C){a={error:C}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}if(!d&&h>v&&(d=!0),!d)throw new R;var w=[new N(s[1],t),new N(f[0],t)],A=new Pe(c,null,0,w,F.ITF,new Date().getTime());return A},e.decodeMiddle=function(t,n,i,a){var o=new Int32Array(10),s=new Int32Array(5),f=new Int32Array(5);for(o.fill(0),s.fill(0),f.fill(0);n<i;){Ae.recordPattern(t,n,o);for(var u=0;u<5;u++){var c=2*u;s[u]=o[c],f[u]=o[c+1]}var l=e.decodeDigit(s);a.append(l.toString()),l=this.decodeDigit(f),a.append(l.toString()),o.forEach(function(h){n+=h})}},e.prototype.decodeStart=function(t){var n=e.skipWhiteSpace(t),i=e.findGuardPattern(t,n,e.START_PATTERN);return this.narrowLineWidth=(i[1]-i[0])/4,this.validateQuietZone(t,i[0]),i},e.prototype.validateQuietZone=function(t,n){var i=this.narrowLineWidth*10;i=i<n?i:n;for(var a=n-1;i>0&&a>=0&&!t.get(a);a--)i--;if(i!==0)throw new I},e.skipWhiteSpace=function(t){var n=t.getSize(),i=t.getNextSet(0);if(i===n)throw new I;return i},e.prototype.decodeEnd=function(t){t.reverse();try{var n=e.skipWhiteSpace(t),i=void 0;try{i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[0])}catch(o){o instanceof I&&(i=e.findGuardPattern(t,n,e.END_PATTERN_REVERSED[1]))}this.validateQuietZone(t,i[0]);var a=i[0];return i[0]=t.getSize()-i[1],i[1]=t.getSize()-a,i}finally{t.reverse()}},e.findGuardPattern=function(t,n,i){var a=i.length,o=new Int32Array(a),s=t.getSize(),f=!1,u=0,c=n;o.fill(0);for(var l=n;l<s;l++)if(t.get(l)!==f)o[u]++;else{if(u===a-1){if(Ae.patternMatchVariance(o,i,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return[c,l];c+=o[0]+o[1],se.arraycopy(o,2,o,0,u-1),o[u-1]=0,o[u]=0,u--}else u++;o[u]=1,f=!f}throw new I},e.decodeDigit=function(t){for(var n=e.MAX_AVG_VARIANCE,i=-1,a=e.PATTERNS.length,o=0;o<a;o++){var s=e.PATTERNS[o],f=Ae.patternMatchVariance(t,s,e.MAX_INDIVIDUAL_VARIANCE);f<n?(n=f,i=o):f===n&&(i=-1)}if(i>=0)return i%10;throw new I},e.PATTERNS=[Int32Array.from([1,1,2,2,1]),Int32Array.from([2,1,1,1,2]),Int32Array.from([1,2,1,1,2]),Int32Array.from([2,2,1,1,1]),Int32Array.from([1,1,2,1,2]),Int32Array.from([2,1,2,1,1]),Int32Array.from([1,2,2,1,1]),Int32Array.from([1,1,1,2,2]),Int32Array.from([2,1,1,2,1]),Int32Array.from([1,2,1,2,1]),Int32Array.from([1,1,3,3,1]),Int32Array.from([3,1,1,1,3]),Int32Array.from([1,3,1,1,3]),Int32Array.from([3,3,1,1,1]),Int32Array.from([1,1,3,1,3]),Int32Array.from([3,1,3,1,1]),Int32Array.from([1,3,3,1,1]),Int32Array.from([1,1,1,3,3]),Int32Array.from([3,1,1,3,1]),Int32Array.from([1,3,1,3,1])],e.MAX_AVG_VARIANCE=.38,e.MAX_INDIVIDUAL_VARIANCE=.5,e.DEFAULT_ALLOWED_LENGTHS=[6,8,10,12,14],e.START_PATTERN=Int32Array.from([1,1,1,1]),e.END_PATTERN_REVERSED=[Int32Array.from([1,1,2]),Int32Array.from([1,1,3])],e})(Ae),ps=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),St=(function(r){ps(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.decodeRowStringBuffer="",t}return e.findStartGuardPattern=function(t){for(var n=!1,i,a=0,o=Int32Array.from([0,0,0]);!n;){o=Int32Array.from([0,0,0]),i=e.findGuardPattern(t,a,!1,this.START_END_PATTERN,o);var s=i[0];a=i[1];var f=s-(a-s);f>=0&&(n=t.isRange(f,s,!1))}return i},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(n===0)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new R;i+=o}i*=3;for(var a=n-2;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new R;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e.findGuardPatternWithoutCounters=function(t,n,i,a){return this.findGuardPattern(t,n,i,a,new Int32Array(a.length))},e.findGuardPattern=function(t,n,i,a,o){var s=t.getSize();n=i?t.getNextUnset(n):t.getNextSet(n);for(var f=0,u=n,c=a.length,l=i,h=n;h<s;h++)if(t.get(h)!==l)o[f]++;else{if(f===c-1){if(Ae.patternMatchVariance(o,a,e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return Int32Array.from([u,h]);u+=o[0]+o[1];for(var d=o.slice(2,o.length),v=0;v<f-1;v++)o[v]=d[v];o[f-1]=0,o[f]=0,f--}else f++;o[f]=1,l=!l}throw new I},e.decodeDigit=function(t,n,i,a){this.recordPattern(t,i,n);for(var o=this.MAX_AVG_VARIANCE,s=-1,f=a.length,u=0;u<f;u++){var c=a[u],l=Ae.patternMatchVariance(n,c,e.MAX_INDIVIDUAL_VARIANCE);l<o&&(o=l,s=u)}if(s>=0)return s;throw new I},e.MAX_AVG_VARIANCE=.48,e.MAX_INDIVIDUAL_VARIANCE=.7,e.START_END_PATTERN=Int32Array.from([1,1,1]),e.MIDDLE_PATTERN=Int32Array.from([1,1,1,1,1]),e.END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.L_PATTERNS=[Int32Array.from([3,2,1,1]),Int32Array.from([2,2,2,1]),Int32Array.from([2,1,2,2]),Int32Array.from([1,4,1,1]),Int32Array.from([1,1,3,2]),Int32Array.from([1,2,3,1]),Int32Array.from([1,1,1,4]),Int32Array.from([1,3,1,2]),Int32Array.from([1,2,1,3]),Int32Array.from([3,1,1,2])],e})(Ae),gs=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},xs=(function(){function r(){this.CHECK_DIGIT_ENCODINGS=[24,20,18,17,12,6,3,10,9,5],this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),f=[new N((n[0]+n[1])/2,e),new N(a,e)],u=new Pe(o,null,0,f,F.UPC_EAN_EXTENSION,new Date().getTime());return s!=null&&u.putAllMetadata(s),u},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),f=t[1],u=0,c=0;c<5&&f<s;c++){var l=St.decodeDigit(e,o,f,St.L_AND_G_PATTERNS);n+=String.fromCharCode(48+l%10);try{for(var h=(i=void 0,gs(o)),d=h.next();!d.done;d=h.next()){var v=d.value;f+=v}}catch(x){i={error:x}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}l>=10&&(u|=1<<4-c),c!==4&&(f=e.getNextSet(f),f=e.getNextUnset(f))}if(n.length!==5)throw new I;var p=this.determineCheckDigit(u);if(r.extensionChecksum(n.toString())!==p)throw new I;return f},r.extensionChecksum=function(e){for(var t=e.length,n=0,i=t-2;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-48;n*=3;for(var i=t-1;i>=0;i-=2)n+=e.charAt(i).charCodeAt(0)-48;return n*=3,n%10},r.prototype.determineCheckDigit=function(e){for(var t=0;t<10;t++)if(e===this.CHECK_DIGIT_ENCODINGS[t])return t;throw new I},r.parseExtensionString=function(e){if(e.length!==5)return null;var t=r.parseExtension5String(e);return t==null?null:new Map([[Re.SUGGESTED_PRICE,t]])},r.parseExtension5String=function(e){var t;switch(e.charAt(0)){case"0":t="£";break;case"5":t="$";break;case"9":switch(e){case"90000":return null;case"99991":return"0.00";case"99990":return"Used"}t="";break;default:t="";break}var n=parseInt(e.substring(1)),i=(n/100).toString(),a=n%100,o=a<10?"0"+a:a.toString();return t+i+"."+o},r})(),ys=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ws=(function(){function r(){this.decodeMiddleCounters=Int32Array.from([0,0,0,0]),this.decodeRowStringBuffer=""}return r.prototype.decodeRow=function(e,t,n){var i=this.decodeRowStringBuffer,a=this.decodeMiddle(t,n,i),o=i.toString(),s=r.parseExtensionString(o),f=[new N((n[0]+n[1])/2,e),new N(a,e)],u=new Pe(o,null,0,f,F.UPC_EAN_EXTENSION,new Date().getTime());return s!=null&&u.putAllMetadata(s),u},r.prototype.decodeMiddle=function(e,t,n){var i,a,o=this.decodeMiddleCounters;o[0]=0,o[1]=0,o[2]=0,o[3]=0;for(var s=e.getSize(),f=t[1],u=0,c=0;c<2&&f<s;c++){var l=St.decodeDigit(e,o,f,St.L_AND_G_PATTERNS);n+=String.fromCharCode(48+l%10);try{for(var h=(i=void 0,ys(o)),d=h.next();!d.done;d=h.next()){var v=d.value;f+=v}}catch(p){i={error:p}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}l>=10&&(u|=1<<1-c),c!==1&&(f=e.getNextSet(f),f=e.getNextUnset(f))}if(n.length!==2)throw new I;if(parseInt(n.toString())%4!==u)throw new I;return f},r.parseExtensionString=function(e){return e.length!==2?null:new Map([[Re.ISSUE_NUMBER,parseInt(e)]])},r})(),_s=(function(){function r(){}return r.decodeRow=function(e,t,n){var i=St.findGuardPattern(t,n,!1,this.EXTENSION_START_PATTERN,new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));try{var a=new xs;return a.decodeRow(e,t,i)}catch{var o=new ws;return o.decodeRow(e,t,i)}},r.EXTENSION_START_PATTERN=Int32Array.from([1,1,2]),r})(),As=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),ye=(function(r){As(e,r);function e(){var t=r.call(this)||this;t.decodeRowStringBuffer="",e.L_AND_G_PATTERNS=e.L_PATTERNS.map(function(s){return Int32Array.from(s)});for(var n=10;n<20;n++){for(var i=e.L_PATTERNS[n-10],a=new Int32Array(i.length),o=0;o<i.length;o++)a[o]=i[i.length-o-1];e.L_AND_G_PATTERNS[n]=a}return t}return e.prototype.decodeRow=function(t,n,i){var a=e.findStartGuardPattern(n),o=i==null?null:i.get(le.NEED_RESULT_POINT_CALLBACK);if(o!=null){var s=new N((a[0]+a[1])/2,t);o.foundPossibleResultPoint(s)}var f=this.decodeMiddle(n,a,this.decodeRowStringBuffer),u=f.rowOffset,c=f.resultString;if(o!=null){var l=new N(u,t);o.foundPossibleResultPoint(l)}var h=e.decodeEnd(n,u);if(o!=null){var d=new N((h[0]+h[1])/2,t);o.foundPossibleResultPoint(d)}var v=h[1],p=v+(v-h[0]);if(p>=n.getSize()||!n.isRange(v,p,!1))throw new I;var x=c.toString();if(x.length<8)throw new R;if(!e.checkChecksum(x))throw new me;var y=(a[1]+a[0])/2,w=(h[1]+h[0])/2,A=this.getBarcodeFormat(),C=[new N(y,t),new N(w,t)],O=new Pe(x,null,0,C,A,new Date().getTime()),b=0;try{var T=_s.decodeRow(t,n,h[1]);O.putMetadata(Re.UPC_EAN_EXTENSION,T.getText()),O.putAllMetadata(T.getResultMetadata()),O.addResultPoints(T.getResultPoints()),b=T.getText().length}catch{}var D=i==null?null:i.get(le.ALLOWED_EAN_EXTENSIONS);if(D!=null){var B=!1;for(var M in D)if(b.toString()===M){B=!0;break}if(!B)throw new I}return A===F.EAN_13||F.UPC_A,O},e.checkChecksum=function(t){return e.checkStandardUPCEANChecksum(t)},e.checkStandardUPCEANChecksum=function(t){var n=t.length;if(n===0)return!1;var i=parseInt(t.charAt(n-1),10);return e.getStandardUPCEANChecksum(t.substring(0,n-1))===i},e.getStandardUPCEANChecksum=function(t){for(var n=t.length,i=0,a=n-1;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new R;i+=o}i*=3;for(var a=n-2;a>=0;a-=2){var o=t.charAt(a).charCodeAt(0)-48;if(o<0||o>9)throw new R;i+=o}return(1e3-i)%10},e.decodeEnd=function(t,n){return e.findGuardPattern(t,n,!1,e.START_END_PATTERN,new Int32Array(e.START_END_PATTERN.length).fill(0))},e})(St),Es=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Rn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Lr=(function(r){Es(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s,f,u=this.decodeMiddleCounters;u[0]=0,u[1]=0,u[2]=0,u[3]=0;for(var c=t.getSize(),l=n[1],h=0,d=0;d<6&&l<c;d++){var v=ye.decodeDigit(t,u,l,ye.L_AND_G_PATTERNS);i+=String.fromCharCode(48+v%10);try{for(var p=(a=void 0,Rn(u)),x=p.next();!x.done;x=p.next()){var y=x.value;l+=y}}catch(O){a={error:O}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}v>=10&&(h|=1<<5-d)}i=e.determineFirstDigit(i,h);var w=ye.findGuardPattern(t,l,!0,ye.MIDDLE_PATTERN,new Int32Array(ye.MIDDLE_PATTERN.length).fill(0));l=w[1];for(var d=0;d<6&&l<c;d++){var v=ye.decodeDigit(t,u,l,ye.L_PATTERNS);i+=String.fromCharCode(48+v);try{for(var A=(s=void 0,Rn(u)),C=A.next();!C.done;C=A.next()){var y=C.value;l+=y}}catch(T){s={error:T}}finally{try{C&&!C.done&&(f=A.return)&&f.call(A)}finally{if(s)throw s.error}}}return{rowOffset:l,resultString:i}},e.prototype.getBarcodeFormat=function(){return F.EAN_13},e.determineFirstDigit=function(t,n){for(var i=0;i<10;i++)if(n===this.FIRST_DIGIT_ENCODINGS[i])return t=String.fromCharCode(48+i)+t,t;throw new I},e.FIRST_DIGIT_ENCODINGS=[0,11,13,14,19,25,28,21,22,26],e})(ye),Cs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Dn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Nn=(function(r){Cs(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=Int32Array.from([0,0,0,0]),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s,f,u=this.decodeMiddleCounters;u[0]=0,u[1]=0,u[2]=0,u[3]=0;for(var c=t.getSize(),l=n[1],h=0;h<4&&l<c;h++){var d=ye.decodeDigit(t,u,l,ye.L_PATTERNS);i+=String.fromCharCode(48+d);try{for(var v=(a=void 0,Dn(u)),p=v.next();!p.done;p=v.next()){var x=p.value;l+=x}}catch(C){a={error:C}}finally{try{p&&!p.done&&(o=v.return)&&o.call(v)}finally{if(a)throw a.error}}}var y=ye.findGuardPattern(t,l,!0,ye.MIDDLE_PATTERN,new Int32Array(ye.MIDDLE_PATTERN.length).fill(0));l=y[1];for(var h=0;h<4&&l<c;h++){var d=ye.decodeDigit(t,u,l,ye.L_PATTERNS);i+=String.fromCharCode(48+d);try{for(var w=(s=void 0,Dn(u)),A=w.next();!A.done;A=w.next()){var x=A.value;l+=x}}catch(b){s={error:b}}finally{try{A&&!A.done&&(f=w.return)&&f.call(w)}finally{if(s)throw s.error}}}return{rowOffset:l,resultString:i}},e.prototype.getBarcodeFormat=function(){return F.EAN_8},e})(ye),ms=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Pn=(function(r){ms(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.ean13Reader=new Lr,t}return e.prototype.getBarcodeFormat=function(){return F.UPC_A},e.prototype.decode=function(t,n){return this.maybeReturnResult(this.ean13Reader.decode(t))},e.prototype.decodeRow=function(t,n,i){return this.maybeReturnResult(this.ean13Reader.decodeRow(t,n,i))},e.prototype.decodeMiddle=function(t,n,i){return this.ean13Reader.decodeMiddle(t,n,i)},e.prototype.maybeReturnResult=function(t){var n=t.getText();if(n.charAt(0)==="0"){var i=new Pe(n.substring(1),null,null,t.getResultPoints(),F.UPC_A);return t.getResultMetadata()!=null&&i.putAllMetadata(t.getResultMetadata()),i}else throw new I},e.prototype.reset=function(){this.ean13Reader.reset()},e})(ye),Ss=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Is=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Mn=(function(r){Ss(e,r);function e(){var t=r.call(this)||this;return t.decodeMiddleCounters=new Int32Array(4),t}return e.prototype.decodeMiddle=function(t,n,i){var a,o,s=this.decodeMiddleCounters.map(function(x){return x});s[0]=0,s[1]=0,s[2]=0,s[3]=0;for(var f=t.getSize(),u=n[1],c=0,l=0;l<6&&u<f;l++){var h=e.decodeDigit(t,s,u,e.L_AND_G_PATTERNS);i+=String.fromCharCode(48+h%10);try{for(var d=(a=void 0,Is(s)),v=d.next();!v.done;v=d.next()){var p=v.value;u+=p}}catch(x){a={error:x}}finally{try{v&&!v.done&&(o=d.return)&&o.call(d)}finally{if(a)throw a.error}}h>=10&&(c|=1<<5-l)}return e.determineNumSysAndCheckDigit(new X(i),c),u},e.prototype.decodeEnd=function(t,n){return e.findGuardPatternWithoutCounters(t,n,!0,e.MIDDLE_END_PATTERN)},e.prototype.checkChecksum=function(t){return ye.checkChecksum(e.convertUPCEtoUPCA(t))},e.determineNumSysAndCheckDigit=function(t,n){for(var i=0;i<=1;i++)for(var a=0;a<10;a++)if(n===this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[i][a]){t.insert(0,"0"+i),t.append("0"+a);return}throw I.getNotFoundInstance()},e.prototype.getBarcodeFormat=function(){return F.UPC_E},e.convertUPCEtoUPCA=function(t){var n=t.slice(1,7).split("").map(function(o){return o.charCodeAt(0)}),i=new X;i.append(t.charAt(0));var a=n[5];switch(a){case 0:case 1:case 2:i.appendChars(n,0,2),i.append(a),i.append("0000"),i.appendChars(n,2,3);break;case 3:i.appendChars(n,0,3),i.append("00000"),i.appendChars(n,3,2);break;case 4:i.appendChars(n,0,4),i.append("00000"),i.append(n[4]);break;default:i.appendChars(n,0,5),i.append("0000"),i.append(a);break}return t.length>=8&&i.append(t.charAt(7)),i.toString()},e.MIDDLE_END_PATTERN=Int32Array.from([1,1,1,1,1,1]),e.NUMSYS_AND_CHECK_DIGIT_PATTERNS=[Int32Array.from([56,52,50,49,44,38,35,42,41,37]),Int32Array.from([7,11,13,14,19,25,28,21,22,1])],e})(ye),Os=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Bn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},wr=(function(r){Os(e,r);function e(t){var n=r.call(this)||this,i=t==null?null:t.get(le.POSSIBLE_FORMATS),a=[];return i!=null&&(i.indexOf(F.EAN_13)>-1&&a.push(new Lr),i.indexOf(F.UPC_A)>-1&&a.push(new Pn),i.indexOf(F.EAN_8)>-1&&a.push(new Nn),i.indexOf(F.UPC_E)>-1&&a.push(new Mn)),a.length===0&&(a.push(new Lr),a.push(new Pn),a.push(new Nn),a.push(new Mn)),n.readers=a,n}return e.prototype.decodeRow=function(t,n,i){var a,o;try{for(var s=Bn(this.readers),f=s.next();!f.done;f=s.next()){var u=f.value;try{var c=u.decodeRow(t,n,i),l=c.getBarcodeFormat()===F.EAN_13&&c.getText().charAt(0)==="0",h=i==null?null:i.get(le.POSSIBLE_FORMATS),d=h==null||h.includes(F.UPC_A);if(l&&d){var v=c.getRawBytes(),p=new Pe(c.getText().substring(1),v,v?v.length:null,c.getResultPoints(),F.UPC_A);return p.putAllMetadata(c.getResultMetadata()),p}return c}catch{}}}catch(x){a={error:x}}finally{try{f&&!f.done&&(o=s.return)&&o.call(s)}finally{if(a)throw a.error}}throw new I},e.prototype.reset=function(){var t,n;try{for(var i=Bn(this.readers),a=i.next();!a.done;a=i.next()){var o=a.value;o.reset()}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e})(Ae),bs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ts=(function(r){bs(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.CODA_BAR_CHAR_SET={nnnnnww:"0",nnnnwwn:"1",nnnwnnw:"2",wwnnnnn:"3",nnwnnwn:"4",wnnnnwn:"5",nwnnnnw:"6",nwnnwnn:"7",nwwnnnn:"8",wnnwnnn:"9",nnnwwnn:"-",nnwwnnn:"$",wnnnwnw:":",wnwnnnw:"/",wnwnwnn:".",nnwwwww:"+",nnwwnwn:"A",nwnwnnw:"B",nnnwnww:"C",nnnwwwn:"D"},t}return e.prototype.decodeRow=function(t,n,i){var a=this.getValidRowData(n);if(!a)throw new I;var o=this.codaBarDecodeRow(a.row);if(!o)throw new I;return new Pe(o,null,0,[new N(a.left,t),new N(a.right,t)],F.CODABAR,new Date().getTime())},e.prototype.getValidRowData=function(t){var n=t.toArray(),i=n.indexOf(!0);if(i===-1)return null;var a=n.lastIndexOf(!0);if(a<=i)return null;n=n.slice(i,a+1);for(var o=[],s=n[0],f=1,u=1;u<n.length;u++)n[u]===s?f++:(s=n[u],o.push(f),f=1);return o.push(f),o.length<23&&(o.length+1)%8!==0?null:{row:o,left:i,right:a}},e.prototype.codaBarDecodeRow=function(t){for(var n=[],i=Math.ceil(t.reduce(function(f,u){return(f+u)/2},0));t.length>0;){var a=t.splice(0,8).splice(0,7),o=a.map(function(f){return f<i?"n":"w"}).join("");if(this.CODA_BAR_CHAR_SET[o]===void 0)return null;n.push(this.CODA_BAR_CHAR_SET[o])}var s=n.join("");return this.validCodaBarString(s)?s:null},e.prototype.validCodaBarString=function(t){var n=/^[A-D].{1,}[A-D]$/;return n.test(t)},e})(Ae),Rs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ds=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},pt=(function(r){Rs(e,r);function e(){var t=r.call(this)||this;return t.decodeFinderCounters=new Int32Array(4),t.dataCharacterCounters=new Int32Array(8),t.oddRoundingErrors=new Array(4),t.evenRoundingErrors=new Array(4),t.oddCounts=new Array(t.dataCharacterCounters.length/2),t.evenCounts=new Array(t.dataCharacterCounters.length/2),t}return e.prototype.getDecodeFinderCounters=function(){return this.decodeFinderCounters},e.prototype.getDataCharacterCounters=function(){return this.dataCharacterCounters},e.prototype.getOddRoundingErrors=function(){return this.oddRoundingErrors},e.prototype.getEvenRoundingErrors=function(){return this.evenRoundingErrors},e.prototype.getOddCounts=function(){return this.oddCounts},e.prototype.getEvenCounts=function(){return this.evenCounts},e.prototype.parseFinderValue=function(t,n){for(var i=0;i<n.length;i++)if(Ae.patternMatchVariance(t,n[i],e.MAX_INDIVIDUAL_VARIANCE)<e.MAX_AVG_VARIANCE)return i;throw new I},e.count=function(t){return q.sum(new Int32Array(t))},e.increment=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]>a&&(a=n[o],i=o);t[i]++},e.decrement=function(t,n){for(var i=0,a=n[0],o=1;o<t.length;o++)n[o]<a&&(a=n[o],i=o);t[i]--},e.isFinderPattern=function(t){var n,i,a=t[0]+t[1],o=a+t[2]+t[3],s=a/o;if(s>=e.MIN_FINDER_PATTERN_RATIO&&s<=e.MAX_FINDER_PATTERN_RATIO){var f=Number.MAX_SAFE_INTEGER,u=Number.MIN_SAFE_INTEGER;try{for(var c=Ds(t),l=c.next();!l.done;l=c.next()){var h=l.value;h>u&&(u=h),h<f&&(f=h)}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}return u<10*f}return!1},e.MAX_AVG_VARIANCE=.2,e.MAX_INDIVIDUAL_VARIANCE=.45,e.MIN_FINDER_PATTERN_RATIO=9.5/12,e.MAX_FINDER_PATTERN_RATIO=12.5/14,e})(Ae),nr=(function(){function r(e,t){this.value=e,this.checksumPortion=t}return r.prototype.getValue=function(){return this.value},r.prototype.getChecksumPortion=function(){return this.checksumPortion},r.prototype.toString=function(){return this.value+"("+this.checksumPortion+")"},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value&&this.checksumPortion===t.checksumPortion},r.prototype.hashCode=function(){return this.value^this.checksumPortion},r})(),Di=(function(){function r(e,t,n,i,a){this.value=e,this.startEnd=t,this.value=e,this.startEnd=t,this.resultPoints=new Array,this.resultPoints.push(new N(n,a)),this.resultPoints.push(new N(i,a))}return r.prototype.getValue=function(){return this.value},r.prototype.getStartEnd=function(){return this.startEnd},r.prototype.getResultPoints=function(){return this.resultPoints},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.prototype.hashCode=function(){return this.value},r})(),Ns=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Et=(function(){function r(){}return r.getRSSvalue=function(e,t,n){var i,a,o=0;try{for(var s=Ns(e),f=s.next();!f.done;f=s.next()){var u=f.value;o+=u}}catch(w){i={error:w}}finally{try{f&&!f.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}for(var c=0,l=0,h=e.length,d=0;d<h-1;d++){var v=void 0;for(v=1,l|=1<<d;v<e[d];v++,l&=~(1<<d)){var p=r.combins(o-v-1,h-d-2);if(n&&l===0&&o-v-(h-d-1)>=h-d-1&&(p-=r.combins(o-v-(h-d),h-d-2)),h-d-1>1){for(var x=0,y=o-v-(h-d-2);y>t;y--)x+=r.combins(o-v-y-1,h-d-3);p-=x*(h-1-d)}else o-v>t&&p--;c+=p}o-=v}return c},r.combins=function(e,t){var n,i;e-t>t?(i=t,n=e-t):(i=e-t,n=t);for(var a=1,o=1,s=e;s>n;s--)a*=s,o<=i&&(a/=o,o++);for(;o<=i;)a/=o,o++;return a},r})(),Ps=(function(){function r(){}return r.buildBitArray=function(e){var t=e.length*2-1;e[e.length-1].getRightChar()==null&&(t-=1);for(var n=12*t,i=new Se(n),a=0,o=e[0],s=o.getRightChar().getValue(),f=11;f>=0;--f)(s&1<<f)!==0&&i.set(a),a++;for(var f=1;f<e.length;++f){for(var u=e[f],c=u.getLeftChar().getValue(),l=11;l>=0;--l)(c&1<<l)!==0&&i.set(a),a++;if(u.getRightChar()!==null)for(var h=u.getRightChar().getValue(),l=11;l>=0;--l)(h&1<<l)!==0&&i.set(a),a++}return i},r})(),dt=(function(){function r(e,t){t?this.decodedInformation=null:(this.finished=e,this.decodedInformation=t)}return r.prototype.getDecodedInformation=function(){return this.decodedInformation},r.prototype.isFinished=function(){return this.finished},r})(),Qr=(function(){function r(e){this.newPosition=e}return r.prototype.getNewPosition=function(){return this.newPosition},r})(),Ms=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ke=(function(r){Ms(e,r);function e(t,n){var i=r.call(this,t)||this;return i.value=n,i}return e.prototype.getValue=function(){return this.value},e.prototype.isFNC1=function(){return this.value===e.FNC1},e.FNC1="$",e})(Qr),Bs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),vt=(function(r){Bs(e,r);function e(t,n,i){var a=r.call(this,t)||this;return i?(a.remaining=!0,a.remainingValue=a.remainingValue):(a.remaining=!1,a.remainingValue=0),a.newString=n,a}return e.prototype.getNewString=function(){return this.newString},e.prototype.isRemaining=function(){return this.remaining},e.prototype.getRemainingValue=function(){return this.remainingValue},e})(Qr),Fs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),At=(function(r){Fs(e,r);function e(t,n,i){var a=r.call(this,t)||this;if(n<0||n>10||i<0||i>10)throw new R;return a.firstDigit=n,a.secondDigit=i,a}return e.prototype.getFirstDigit=function(){return this.firstDigit},e.prototype.getSecondDigit=function(){return this.secondDigit},e.prototype.getValue=function(){return this.firstDigit*10+this.secondDigit},e.prototype.isFirstDigitFNC1=function(){return this.firstDigit===e.FNC1},e.prototype.isSecondDigitFNC1=function(){return this.secondDigit===e.FNC1},e.prototype.isAnyFNC1=function(){return this.firstDigit===e.FNC1||this.secondDigit===e.FNC1},e.FNC1=10,e})(Qr),jt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ls=(function(){function r(){}return r.parseFieldsInGeneralPurpose=function(e){var t,n,i,a,o,s,f,u;if(!e)return null;if(e.length<2)throw new I;var c=e.substring(0,2);try{for(var l=jt(r.TWO_DIGIT_DATA_LENGTH),h=l.next();!h.done;h=l.next()){var d=h.value;if(d[0]===c)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(2,d[2],e):r.processFixedAI(2,d[1],e)}}catch(b){t={error:b}}finally{try{h&&!h.done&&(n=l.return)&&n.call(l)}finally{if(t)throw t.error}}if(e.length<3)throw new I;var v=e.substring(0,3);try{for(var p=jt(r.THREE_DIGIT_DATA_LENGTH),x=p.next();!x.done;x=p.next()){var d=x.value;if(d[0]===v)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(3,d[2],e):r.processFixedAI(3,d[1],e)}}catch(b){i={error:b}}finally{try{x&&!x.done&&(a=p.return)&&a.call(p)}finally{if(i)throw i.error}}try{for(var y=jt(r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH),w=y.next();!w.done;w=y.next()){var d=w.value;if(d[0]===v)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,d[2],e):r.processFixedAI(4,d[1],e)}}catch(b){o={error:b}}finally{try{w&&!w.done&&(s=y.return)&&s.call(y)}finally{if(o)throw o.error}}if(e.length<4)throw new I;var A=e.substring(0,4);try{for(var C=jt(r.FOUR_DIGIT_DATA_LENGTH),O=C.next();!O.done;O=C.next()){var d=O.value;if(d[0]===A)return d[1]===r.VARIABLE_LENGTH?r.processVariableAI(4,d[2],e):r.processFixedAI(4,d[1],e)}}catch(b){f={error:b}}finally{try{O&&!O.done&&(u=C.return)&&u.call(C)}finally{if(f)throw f.error}}throw new I},r.processFixedAI=function(e,t,n){if(n.length<e)throw new I;var i=n.substring(0,e);if(n.length<e+t)throw new I;var a=n.substring(e,e+t),o=n.substring(e+t),s="("+i+")"+a,f=r.parseFieldsInGeneralPurpose(o);return f==null?s:s+f},r.processVariableAI=function(e,t,n){var i=n.substring(0,e),a;n.length<e+t?a=n.length:a=e+t;var o=n.substring(e,a),s=n.substring(a),f="("+i+")"+o,u=r.parseFieldsInGeneralPurpose(s);return u==null?f:f+u},r.VARIABLE_LENGTH=[],r.TWO_DIGIT_DATA_LENGTH=[["00",18],["01",14],["02",14],["10",r.VARIABLE_LENGTH,20],["11",6],["12",6],["13",6],["15",6],["17",6],["20",2],["21",r.VARIABLE_LENGTH,20],["22",r.VARIABLE_LENGTH,29],["30",r.VARIABLE_LENGTH,8],["37",r.VARIABLE_LENGTH,8],["90",r.VARIABLE_LENGTH,30],["91",r.VARIABLE_LENGTH,30],["92",r.VARIABLE_LENGTH,30],["93",r.VARIABLE_LENGTH,30],["94",r.VARIABLE_LENGTH,30],["95",r.VARIABLE_LENGTH,30],["96",r.VARIABLE_LENGTH,30],["97",r.VARIABLE_LENGTH,3],["98",r.VARIABLE_LENGTH,30],["99",r.VARIABLE_LENGTH,30]],r.THREE_DIGIT_DATA_LENGTH=[["240",r.VARIABLE_LENGTH,30],["241",r.VARIABLE_LENGTH,30],["242",r.VARIABLE_LENGTH,6],["250",r.VARIABLE_LENGTH,30],["251",r.VARIABLE_LENGTH,30],["253",r.VARIABLE_LENGTH,17],["254",r.VARIABLE_LENGTH,20],["400",r.VARIABLE_LENGTH,30],["401",r.VARIABLE_LENGTH,30],["402",17],["403",r.VARIABLE_LENGTH,30],["410",13],["411",13],["412",13],["413",13],["414",13],["420",r.VARIABLE_LENGTH,20],["421",r.VARIABLE_LENGTH,15],["422",3],["423",r.VARIABLE_LENGTH,15],["424",3],["425",3],["426",3]],r.THREE_DIGIT_PLUS_DIGIT_DATA_LENGTH=[["310",6],["311",6],["312",6],["313",6],["314",6],["315",6],["316",6],["320",6],["321",6],["322",6],["323",6],["324",6],["325",6],["326",6],["327",6],["328",6],["329",6],["330",6],["331",6],["332",6],["333",6],["334",6],["335",6],["336",6],["340",6],["341",6],["342",6],["343",6],["344",6],["345",6],["346",6],["347",6],["348",6],["349",6],["350",6],["351",6],["352",6],["353",6],["354",6],["355",6],["356",6],["357",6],["360",6],["361",6],["362",6],["363",6],["364",6],["365",6],["366",6],["367",6],["368",6],["369",6],["390",r.VARIABLE_LENGTH,15],["391",r.VARIABLE_LENGTH,18],["392",r.VARIABLE_LENGTH,15],["393",r.VARIABLE_LENGTH,18],["703",r.VARIABLE_LENGTH,30]],r.FOUR_DIGIT_DATA_LENGTH=[["7001",13],["7002",r.VARIABLE_LENGTH,30],["7003",10],["8001",14],["8002",r.VARIABLE_LENGTH,20],["8003",r.VARIABLE_LENGTH,30],["8004",r.VARIABLE_LENGTH,30],["8005",6],["8006",18],["8007",r.VARIABLE_LENGTH,30],["8008",r.VARIABLE_LENGTH,12],["8018",18],["8020",r.VARIABLE_LENGTH,25],["8100",6],["8101",10],["8102",2],["8110",r.VARIABLE_LENGTH,70],["8200",r.VARIABLE_LENGTH,70]],r})(),er=(function(){function r(e){this.buffer=new X,this.information=e}return r.prototype.decodeAllCodes=function(e,t){var n=t,i=null;do{var a=this.decodeGeneralPurposeField(n,i),o=Ls.parseFieldsInGeneralPurpose(a.getNewString());if(o!=null&&e.append(o),a.isRemaining()?i=""+a.getRemainingValue():i=null,n===a.getNewPosition())break;n=a.getNewPosition()}while(!0);return e.toString()},r.prototype.isStillNumeric=function(e){if(e+7>this.information.getSize())return e+4<=this.information.getSize();for(var t=e;t<e+3;++t)if(this.information.get(t))return!0;return this.information.get(e+3)},r.prototype.decodeNumeric=function(e){if(e+7>this.information.getSize()){var t=this.extractNumericValueFromBitArray(e,4);return t===0?new At(this.information.getSize(),At.FNC1,At.FNC1):new At(this.information.getSize(),t-1,At.FNC1)}var n=this.extractNumericValueFromBitArray(e,7),i=(n-8)/11,a=(n-8)%11;return new At(e+7,i,a)},r.prototype.extractNumericValueFromBitArray=function(e,t){return r.extractNumericValueFromBitArray(this.information,e,t)},r.extractNumericValueFromBitArray=function(e,t,n){for(var i=0,a=0;a<n;++a)e.get(t+a)&&(i|=1<<n-a-1);return i},r.prototype.decodeGeneralPurposeField=function(e,t){this.buffer.setLengthToZero(),t!=null&&this.buffer.append(t),this.current.setPosition(e);var n=this.parseBlocks();return n!=null&&n.isRemaining()?new vt(this.current.getPosition(),this.buffer.toString(),n.getRemainingValue()):new vt(this.current.getPosition(),this.buffer.toString())},r.prototype.parseBlocks=function(){var e,t;do{var n=this.current.getPosition();this.current.isAlpha()?(t=this.parseAlphaBlock(),e=t.isFinished()):this.current.isIsoIec646()?(t=this.parseIsoIec646Block(),e=t.isFinished()):(t=this.parseNumericBlock(),e=t.isFinished());var i=n!==this.current.getPosition();if(!i&&!e)break}while(!e);return t.getDecodedInformation()},r.prototype.parseNumericBlock=function(){for(;this.isStillNumeric(this.current.getPosition());){var e=this.decodeNumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFirstDigitFNC1()){var t=void 0;return e.isSecondDigitFNC1()?t=new vt(this.current.getPosition(),this.buffer.toString()):t=new vt(this.current.getPosition(),this.buffer.toString(),e.getSecondDigit()),new dt(!0,t)}if(this.buffer.append(e.getFirstDigit()),e.isSecondDigitFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new dt(!0,t)}this.buffer.append(e.getSecondDigit())}return this.isNumericToAlphaNumericLatch(this.current.getPosition())&&(this.current.setAlpha(),this.current.incrementPosition(4)),new dt(!1)},r.prototype.parseIsoIec646Block=function(){for(;this.isStillIsoIec646(this.current.getPosition());){var e=this.decodeIsoIec646(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new dt(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setAlpha()),new dt(!1)},r.prototype.parseAlphaBlock=function(){for(;this.isStillAlpha(this.current.getPosition());){var e=this.decodeAlphanumeric(this.current.getPosition());if(this.current.setPosition(e.getNewPosition()),e.isFNC1()){var t=new vt(this.current.getPosition(),this.buffer.toString());return new dt(!0,t)}this.buffer.append(e.getValue())}return this.isAlphaOr646ToNumericLatch(this.current.getPosition())?(this.current.incrementPosition(3),this.current.setNumeric()):this.isAlphaTo646ToAlphaLatch(this.current.getPosition())&&(this.current.getPosition()+5<this.information.getSize()?this.current.incrementPosition(5):this.current.setPosition(this.information.getSize()),this.current.setIsoIec646()),new dt(!1)},r.prototype.isStillIsoIec646=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+7>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<116)return!0;if(e+8>this.information.getSize())return!1;var i=this.extractNumericValueFromBitArray(e,8);return i>=232&&i<253},r.prototype.decodeIsoIec646=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new Ke(e+5,Ke.FNC1);if(t>=5&&t<15)return new Ke(e+5,"0"+(t-5));var n=this.extractNumericValueFromBitArray(e,7);if(n>=64&&n<90)return new Ke(e+7,""+(n+1));if(n>=90&&n<116)return new Ke(e+7,""+(n+7));var i=this.extractNumericValueFromBitArray(e,8),a;switch(i){case 232:a="!";break;case 233:a='"';break;case 234:a="%";break;case 235:a="&";break;case 236:a="'";break;case 237:a="(";break;case 238:a=")";break;case 239:a="*";break;case 240:a="+";break;case 241:a=",";break;case 242:a="-";break;case 243:a=".";break;case 244:a="/";break;case 245:a=":";break;case 246:a=";";break;case 247:a="<";break;case 248:a="=";break;case 249:a=">";break;case 250:a="?";break;case 251:a="_";break;case 252:a=" ";break;default:throw new R}return new Ke(e+8,a)},r.prototype.isStillAlpha=function(e){if(e+5>this.information.getSize())return!1;var t=this.extractNumericValueFromBitArray(e,5);if(t>=5&&t<16)return!0;if(e+6>this.information.getSize())return!1;var n=this.extractNumericValueFromBitArray(e,6);return n>=16&&n<63},r.prototype.decodeAlphanumeric=function(e){var t=this.extractNumericValueFromBitArray(e,5);if(t===15)return new Ke(e+5,Ke.FNC1);if(t>=5&&t<15)return new Ke(e+5,"0"+(t-5));var n=this.extractNumericValueFromBitArray(e,6);if(n>=32&&n<58)return new Ke(e+6,""+(n+33));var i;switch(n){case 58:i="*";break;case 59:i=",";break;case 60:i="-";break;case 61:i=".";break;case 62:i="/";break;default:throw new Vt("Decoding invalid alphanumeric value: "+n)}return new Ke(e+6,i)},r.prototype.isAlphaTo646ToAlphaLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<5&&t+e<this.information.getSize();++t)if(t===2){if(!this.information.get(e+2))return!1}else if(this.information.get(e+t))return!1;return!0},r.prototype.isAlphaOr646ToNumericLatch=function(e){if(e+3>this.information.getSize())return!1;for(var t=e;t<e+3;++t)if(this.information.get(t))return!1;return!0},r.prototype.isNumericToAlphaNumericLatch=function(e){if(e+1>this.information.getSize())return!1;for(var t=0;t<4&&t+e<this.information.getSize();++t)if(this.information.get(e+t))return!1;return!0},r})(),Ni=(function(){function r(e){this.information=e,this.generalDecoder=new er(e)}return r.prototype.getInformation=function(){return this.information},r.prototype.getGeneralDecoder=function(){return this.generalDecoder},r})(),ks=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Qe=(function(r){ks(e,r);function e(t){return r.call(this,t)||this}return e.prototype.encodeCompressedGtin=function(t,n){t.append("(01)");var i=t.length();t.append("9"),this.encodeCompressedGtinWithoutAI(t,n,i)},e.prototype.encodeCompressedGtinWithoutAI=function(t,n,i){for(var a=0;a<4;++a){var o=this.getGeneralDecoder().extractNumericValueFromBitArray(n+10*a,10);o/100===0&&t.append("0"),o/10===0&&t.append("0"),t.append(o)}e.appendCheckDigit(t,i)},e.appendCheckDigit=function(t,n){for(var i=0,a=0;a<13;a++){var o=t.charAt(a+n).charCodeAt(0)-48;i+=(a&1)===0?3*o:o}i=10-i%10,i===10&&(i=0),t.append(i)},e.GTIN_SIZE=40,e})(Ni),Us=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Hs=(function(r){Us(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){var t=new X;t.append("(01)");var n=t.length(),i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE,4);return t.append(i),this.encodeCompressedGtinWithoutAI(t,e.HEADER_SIZE+4,n),this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE+44)},e.HEADER_SIZE=4,e})(Qe),Vs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Gs=(function(r){Vs(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){var t=new X;return this.getGeneralDecoder().decodeAllCodes(t,e.HEADER_SIZE)},e.HEADER_SIZE=5,e})(Ni),Ws=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),tr=(function(r){Ws(e,r);function e(t){return r.call(this,t)||this}return e.prototype.encodeCompressedWeight=function(t,n,i){var a=this.getGeneralDecoder().extractNumericValueFromBitArray(n,i);this.addWeightCode(t,a);for(var o=this.checkWeight(a),s=1e5,f=0;f<5;++f)o/s===0&&t.append("0"),s/=10;t.append(o)},e})(Qe),Xs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Pi=(function(r){Xs(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+tr.GTIN_SIZE+e.WEIGHT_SIZE)throw new I;var t=new X;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+tr.GTIN_SIZE,e.WEIGHT_SIZE),t.toString()},e.HEADER_SIZE=5,e.WEIGHT_SIZE=15,e})(tr),zs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),js=(function(r){zs(e,r);function e(t){return r.call(this,t)||this}return e.prototype.addWeightCode=function(t,n){t.append("(3103)")},e.prototype.checkWeight=function(t){return t},e})(Pi),Ys=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),$s=(function(r){Ys(e,r);function e(t){return r.call(this,t)||this}return e.prototype.addWeightCode=function(t,n){n<1e4?t.append("(3202)"):t.append("(3203)")},e.prototype.checkWeight=function(t){return t<1e4?t:t-1e4},e})(Pi),Zs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ks=(function(r){Zs(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Qe.GTIN_SIZE)throw new I;var t=new X;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Qe.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(392"),t.append(n),t.append(")");var i=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Qe.GTIN_SIZE+e.LAST_DIGIT_SIZE,null);return t.append(i.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e})(Qe),qs=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Qs=(function(r){qs(e,r);function e(t){return r.call(this,t)||this}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()<e.HEADER_SIZE+Qe.GTIN_SIZE)throw new I;var t=new X;this.encodeCompressedGtin(t,e.HEADER_SIZE);var n=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Qe.GTIN_SIZE,e.LAST_DIGIT_SIZE);t.append("(393"),t.append(n),t.append(")");var i=this.getGeneralDecoder().extractNumericValueFromBitArray(e.HEADER_SIZE+Qe.GTIN_SIZE+e.LAST_DIGIT_SIZE,e.FIRST_THREE_DIGITS_SIZE);i/100===0&&t.append("0"),i/10===0&&t.append("0"),t.append(i);var a=this.getGeneralDecoder().decodeGeneralPurposeField(e.HEADER_SIZE+Qe.GTIN_SIZE+e.LAST_DIGIT_SIZE+e.FIRST_THREE_DIGITS_SIZE,null);return t.append(a.getNewString()),t.toString()},e.HEADER_SIZE=8,e.LAST_DIGIT_SIZE=2,e.FIRST_THREE_DIGITS_SIZE=10,e})(Qe),Js=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),st=(function(r){Js(e,r);function e(t,n,i){var a=r.call(this,t)||this;return a.dateCode=i,a.firstAIdigits=n,a}return e.prototype.parseInformation=function(){if(this.getInformation().getSize()!==e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE+e.DATE_SIZE)throw new I;var t=new X;return this.encodeCompressedGtin(t,e.HEADER_SIZE),this.encodeCompressedWeight(t,e.HEADER_SIZE+e.GTIN_SIZE,e.WEIGHT_SIZE),this.encodeCompressedDate(t,e.HEADER_SIZE+e.GTIN_SIZE+e.WEIGHT_SIZE),t.toString()},e.prototype.encodeCompressedDate=function(t,n){var i=this.getGeneralDecoder().extractNumericValueFromBitArray(n,e.DATE_SIZE);if(i!==38400){t.append("("),t.append(this.dateCode),t.append(")");var a=i%32;i/=32;var o=i%12+1;i/=12;var s=i;s/10===0&&t.append("0"),t.append(s),o/10===0&&t.append("0"),t.append(o),a/10===0&&t.append("0"),t.append(a)}},e.prototype.addWeightCode=function(t,n){t.append("("),t.append(this.firstAIdigits),t.append(n/1e5),t.append(")")},e.prototype.checkWeight=function(t){return t%1e5},e.HEADER_SIZE=8,e.WEIGHT_SIZE=20,e.DATE_SIZE=16,e})(tr);function e1(r){try{if(r.get(1))return new Hs(r);if(!r.get(2))return new Gs(r);var e=er.extractNumericValueFromBitArray(r,1,4);switch(e){case 4:return new js(r);case 5:return new $s(r)}var t=er.extractNumericValueFromBitArray(r,1,5);switch(t){case 12:return new Ks(r);case 13:return new Qs(r)}var n=er.extractNumericValueFromBitArray(r,1,7);switch(n){case 56:return new st(r,"310","11");case 57:return new st(r,"320","11");case 58:return new st(r,"310","13");case 59:return new st(r,"320","13");case 60:return new st(r,"310","15");case 61:return new st(r,"320","15");case 62:return new st(r,"310","17");case 63:return new st(r,"320","17")}}catch(i){throw console.log(i),new Vt("unknown decoder: "+r)}}var Fn=(function(){function r(e,t,n,i){this.leftchar=e,this.rightchar=t,this.finderpattern=n,this.maybeLast=i}return r.prototype.mayBeLast=function(){return this.maybeLast},r.prototype.getLeftChar=function(){return this.leftchar},r.prototype.getRightChar=function(){return this.rightchar},r.prototype.getFinderPattern=function(){return this.finderpattern},r.prototype.mustBeLast=function(){return this.rightchar==null},r.prototype.toString=function(){return"[ "+this.leftchar+", "+this.rightchar+" : "+(this.finderpattern==null?"null":this.finderpattern.getValue())+" ]"},r.equals=function(e,t){return e instanceof r?r.equalsOrNull(e.leftchar,t.leftchar)&&r.equalsOrNull(e.rightchar,t.rightchar)&&r.equalsOrNull(e.finderpattern,t.finderpattern):!1},r.equalsOrNull=function(e,t){return e===null?t===null:r.equals(e,t)},r.prototype.hashCode=function(){var e=this.leftchar.getValue()^this.rightchar.getValue()^this.finderpattern.getValue();return e},r})(),t1=(function(){function r(e,t,n){this.pairs=e,this.rowNumber=t,this.wasReversed=n}return r.prototype.getPairs=function(){return this.pairs},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.isReversed=function(){return this.wasReversed},r.prototype.isEquivalent=function(e){return this.checkEqualitity(this,e)},r.prototype.toString=function(){return"{ "+this.pairs+" }"},r.prototype.equals=function(e,t){return e instanceof r?this.checkEqualitity(e,t)&&e.wasReversed===t.wasReversed:!1},r.prototype.checkEqualitity=function(e,t){if(!(!e||!t)){var n;return e.forEach(function(i,a){t.forEach(function(o){i.getLeftChar().getValue()===o.getLeftChar().getValue()&&i.getRightChar().getValue()===o.getRightChar().getValue()&&i.getFinderPatter().getValue()===o.getFinderPatter().getValue()&&(n=!0)})}),n}},r})(),r1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),ft=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},n1=(function(r){r1(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.pairs=new Array(e.MAX_PAIRS),t.rows=new Array,t.startEnd=[2],t}return e.prototype.decodeRow=function(t,n,i){this.pairs.length=0,this.startFromEven=!1;try{return e.constructResult(this.decodeRow2pairs(t,n))}catch{}return this.pairs.length=0,this.startFromEven=!0,e.constructResult(this.decodeRow2pairs(t,n))},e.prototype.reset=function(){this.pairs.length=0,this.rows.length=0},e.prototype.decodeRow2pairs=function(t,n){for(var i=!1;!i;)try{this.pairs.push(this.retrieveNextPair(n,this.pairs,t))}catch(s){if(s instanceof I){if(!this.pairs.length)throw new I;i=!0}}if(this.checkChecksum())return this.pairs;var a;if(this.rows.length?a=!0:a=!1,this.storeRow(t,!1),a){var o=this.checkRowsBoolean(!1);if(o!=null||(o=this.checkRowsBoolean(!0),o!=null))return o}throw new I},e.prototype.checkRowsBoolean=function(t){if(this.rows.length>25)return this.rows.length=0,null;this.pairs.length=0,t&&(this.rows=this.rows.reverse());var n=null;try{n=this.checkRows(new Array,0)}catch(i){console.log(i)}return t&&(this.rows=this.rows.reverse()),n},e.prototype.checkRows=function(t,n){for(var i,a,o=n;o<this.rows.length;o++){var s=this.rows[o];this.pairs.length=0;try{for(var f=(i=void 0,ft(t)),u=f.next();!u.done;u=f.next()){var c=u.value;this.pairs.push(c.getPairs())}}catch(h){i={error:h}}finally{try{u&&!u.done&&(a=f.return)&&a.call(f)}finally{if(i)throw i.error}}if(this.pairs.push(s.getPairs()),!!e.isValidSequence(this.pairs)){if(this.checkChecksum())return this.pairs;var l=new Array(t);l.push(s);try{return this.checkRows(l,o+1)}catch(h){console.log(h)}}}throw new I},e.isValidSequence=function(t){var n,i;try{for(var a=ft(e.FINDER_PATTERN_SEQUENCES),o=a.next();!o.done;o=a.next()){var s=o.value;if(!(t.length>s.length)){for(var f=!0,u=0;u<t.length;u++)if(t[u].getFinderPattern().getValue()!==s[u]){f=!1;break}if(f)return!0}}}catch(c){n={error:c}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}return!1},e.prototype.storeRow=function(t,n){for(var i=0,a=!1,o=!1;i<this.rows.length;){var s=this.rows[i];if(s.getRowNumber()>t){o=s.isEquivalent(this.pairs);break}a=s.isEquivalent(this.pairs),i++}o||a||e.isPartialRow(this.pairs,this.rows)||(this.rows.push(i,new t1(this.pairs,t,n)),this.removePartialRows(this.pairs,this.rows))},e.prototype.removePartialRows=function(t,n){var i,a,o,s,f,u;try{for(var c=ft(n),l=c.next();!l.done;l=c.next()){var h=l.value;if(h.getPairs().length!==t.length){var d=!0;try{for(var v=(o=void 0,ft(h.getPairs())),p=v.next();!p.done;p=v.next()){var x=p.value,y=!1;try{for(var w=(f=void 0,ft(t)),A=w.next();!A.done;A=w.next()){var C=A.value;if(Fn.equals(x,C)){y=!0;break}}}catch(O){f={error:O}}finally{try{A&&!A.done&&(u=w.return)&&u.call(w)}finally{if(f)throw f.error}}y||(d=!1)}}catch(O){o={error:O}}finally{try{p&&!p.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}}}}catch(O){i={error:O}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}},e.isPartialRow=function(t,n){var i,a,o,s,f,u;try{for(var c=ft(n),l=c.next();!l.done;l=c.next()){var h=l.value,d=!0;try{for(var v=(o=void 0,ft(t)),p=v.next();!p.done;p=v.next()){var x=p.value,y=!1;try{for(var w=(f=void 0,ft(h.getPairs())),A=w.next();!A.done;A=w.next()){var C=A.value;if(x.equals(C)){y=!0;break}}}catch(O){f={error:O}}finally{try{A&&!A.done&&(u=w.return)&&u.call(w)}finally{if(f)throw f.error}}if(!y){d=!1;break}}}catch(O){o={error:O}}finally{try{p&&!p.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}if(d)return!0}}catch(O){i={error:O}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}return!1},e.prototype.getRows=function(){return this.rows},e.constructResult=function(t){var n=Ps.buildBitArray(t),i=e1(n),a=i.parseInformation(),o=t[0].getFinderPattern().getResultPoints(),s=t[t.length-1].getFinderPattern().getResultPoints(),f=[o[0],o[1],s[0],s[1]];return new Pe(a,null,null,f,F.RSS_EXPANDED,null)},e.prototype.checkChecksum=function(){var t=this.pairs.get(0),n=t.getLeftChar(),i=t.getRightChar();if(i===null)return!1;for(var a=i.getChecksumPortion(),o=2,s=1;s<this.pairs.size();++s){var f=this.pairs.get(s);a+=f.getLeftChar().getChecksumPortion(),o++;var u=f.getRightChar();u!=null&&(a+=u.getChecksumPortion(),o++)}a%=211;var c=211*(o-4)+a;return c===n.getValue()},e.getNextSecondBar=function(t,n){var i;return t.get(n)?(i=t.getNextUnset(n),i=t.getNextSet(i)):(i=t.getNextSet(n),i=t.getNextUnset(i)),i},e.prototype.retrieveNextPair=function(t,n,i){var a=n.length%2===0;this.startFromEven&&(a=!a);var o,s=!0,f=-1;do this.findNextPair(t,n,f),o=this.parseFoundFinderPattern(t,i,a),o===null?f=e.getNextSecondBar(t,this.startEnd[0]):s=!1;while(s);var u=this.decodeDataCharacter(t,o,a,!0);if(!this.isEmptyPair(n)&&n[n.length-1].mustBeLast())throw new I;var c;try{c=this.decodeDataCharacter(t,o,a,!1)}catch(l){c=null,console.log(l)}return new Fn(u,c,o,!0)},e.prototype.isEmptyPair=function(t){return t.length===0},e.prototype.findNextPair=function(t,n,i){var a=this.getDecodeFinderCounters();a[0]=0,a[1]=0,a[2]=0,a[3]=0;var o=t.getSize(),s;if(i>=0)s=i;else if(this.isEmptyPair(n))s=0;else{var f=n[n.length-1];s=f.getFinderPattern().getStartEnd()[1]}var u=n.length%2!==0;this.startFromEven&&(u=!u);for(var c=!1;s<o&&(c=!t.get(s),!!c);)s++;for(var l=0,h=s,d=s;d<o;d++)if(t.get(d)!==c)a[l]++;else{if(l===3){if(u&&e.reverseCounters(a),e.isFinderPattern(a)){this.startEnd[0]=h,this.startEnd[1]=d;return}u&&e.reverseCounters(a),h+=a[0]+a[1],a[0]=a[2],a[1]=a[3],a[2]=0,a[3]=0,l--}else l++;a[l]=1,c=!c}throw new I},e.reverseCounters=function(t){for(var n=t.length,i=0;i<n/2;++i){var a=t[i];t[i]=t[n-i-1],t[n-i-1]=a}},e.prototype.parseFoundFinderPattern=function(t,n,i){var a,o,s;if(i){for(var f=this.startEnd[0]-1;f>=0&&!t.get(f);)f--;f++,a=this.startEnd[0]-f,o=f,s=this.startEnd[1]}else o=this.startEnd[0],s=t.getNextUnset(this.startEnd[1]+1),a=s-this.startEnd[1];var u=this.getDecodeFinderCounters();se.arraycopy(u,0,u,1,u.length-1),u[0]=a;var c;try{c=this.parseFinderValue(u,e.FINDER_PATTERNS)}catch{return null}return new Di(c,[o,s],o,s,n)},e.prototype.decodeDataCharacter=function(t,n,i,a){for(var o=this.getDataCharacterCounters(),s=0;s<o.length;s++)o[s]=0;if(a)e.recordPatternInReverse(t,n.getStartEnd()[0],o);else{e.recordPattern(t,n.getStartEnd()[1],o);for(var f=0,u=o.length-1;f<u;f++,u--){var c=o[f];o[f]=o[u],o[u]=c}}var l=17,h=q.sum(new Int32Array(o))/l,d=(n.getStartEnd()[1]-n.getStartEnd()[0])/15;if(Math.abs(h-d)/d>.3)throw new I;for(var v=this.getOddCounts(),p=this.getEvenCounts(),x=this.getOddRoundingErrors(),y=this.getEvenRoundingErrors(),f=0;f<o.length;f++){var w=1*o[f]/h,A=w+.5;if(A<1){if(w<.3)throw new I;A=1}else if(A>8){if(w>8.7)throw new I;A=8}var C=f/2;(f&1)===0?(v[C]=A,x[C]=w-A):(p[C]=A,y[C]=w-A)}this.adjustOddEvenCounts(l);for(var O=4*n.getValue()+(i?0:2)+(a?0:1)-1,b=0,T=0,f=v.length-1;f>=0;f--){if(e.isNotA1left(n,i,a)){var D=e.WEIGHTS[O][2*f];T+=v[f]*D}b+=v[f]}for(var B=0,f=p.length-1;f>=0;f--)if(e.isNotA1left(n,i,a)){var D=e.WEIGHTS[O][2*f+1];B+=p[f]*D}var M=T+B;if((b&1)!==0||b>13||b<4)throw new I;var ae=(13-b)/2,W=e.SYMBOL_WIDEST[ae],Q=9-W,he=Et.getRSSvalue(v,W,!0),Me=Et.getRSSvalue(p,Q,!1),Ie=e.EVEN_TOTAL_SUBSET[ae],ot=e.GSUM[ae],ke=he*Ie+Me+ot;return new nr(ke,M)},e.isNotA1left=function(t,n,i){return!(t.getValue()===0&&n&&i)},e.prototype.adjustOddEvenCounts=function(t){var n=q.sum(new Int32Array(this.getOddCounts())),i=q.sum(new Int32Array(this.getEvenCounts())),a=!1,o=!1;n>13?o=!0:n<4&&(a=!0);var s=!1,f=!1;i>13?f=!0:i<4&&(s=!0);var u=n+i-t,c=(n&1)===1,l=(i&1)===0;if(u===1)if(c){if(l)throw new I;o=!0}else{if(!l)throw new I;f=!0}else if(u===-1)if(c){if(l)throw new I;a=!0}else{if(!l)throw new I;s=!0}else if(u===0){if(c){if(!l)throw new I;n<i?(a=!0,f=!0):(o=!0,s=!0)}else if(l)throw new I}else throw new I;if(a){if(o)throw new I;e.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(o&&e.decrement(this.getOddCounts(),this.getOddRoundingErrors()),s){if(f)throw new I;e.increment(this.getEvenCounts(),this.getOddRoundingErrors())}f&&e.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.SYMBOL_WIDEST=[7,5,4,3,1],e.EVEN_TOTAL_SUBSET=[4,20,52,104,204],e.GSUM=[0,348,1388,2948,3988],e.FINDER_PATTERNS=[Int32Array.from([1,8,4,1]),Int32Array.from([3,6,4,1]),Int32Array.from([3,4,6,1]),Int32Array.from([3,2,8,1]),Int32Array.from([2,6,5,1]),Int32Array.from([2,2,9,1])],e.WEIGHTS=[[1,3,9,27,81,32,96,77],[20,60,180,118,143,7,21,63],[189,145,13,39,117,140,209,205],[193,157,49,147,19,57,171,91],[62,186,136,197,169,85,44,132],[185,133,188,142,4,12,36,108],[113,128,173,97,80,29,87,50],[150,28,84,41,123,158,52,156],[46,138,203,187,139,206,196,166],[76,17,51,153,37,111,122,155],[43,129,176,106,107,110,119,146],[16,48,144,10,30,90,59,177],[109,116,137,200,178,112,125,164],[70,210,208,202,184,130,179,115],[134,191,151,31,93,68,204,190],[148,22,66,198,172,94,71,2],[6,18,54,162,64,192,154,40],[120,149,25,75,14,42,126,167],[79,26,78,23,69,207,199,175],[103,98,83,38,114,131,182,124],[161,61,183,127,170,88,53,159],[55,165,73,8,24,72,5,15],[45,135,194,160,58,174,100,89]],e.FINDER_PAT_A=0,e.FINDER_PAT_B=1,e.FINDER_PAT_C=2,e.FINDER_PAT_D=3,e.FINDER_PAT_E=4,e.FINDER_PAT_F=5,e.FINDER_PATTERN_SEQUENCES=[[e.FINDER_PAT_A,e.FINDER_PAT_A],[e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B],[e.FINDER_PAT_A,e.FINDER_PAT_C,e.FINDER_PAT_B,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_C],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_E,e.FINDER_PAT_B,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F],[e.FINDER_PAT_A,e.FINDER_PAT_A,e.FINDER_PAT_B,e.FINDER_PAT_B,e.FINDER_PAT_C,e.FINDER_PAT_D,e.FINDER_PAT_D,e.FINDER_PAT_E,e.FINDER_PAT_E,e.FINDER_PAT_F,e.FINDER_PAT_F]],e.MAX_PAIRS=11,e})(pt),i1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),a1=(function(r){i1(e,r);function e(t,n,i){var a=r.call(this,t,n)||this;return a.count=0,a.finderPattern=i,a}return e.prototype.getFinderPattern=function(){return this.finderPattern},e.prototype.getCount=function(){return this.count},e.prototype.incrementCount=function(){this.count++},e})(nr),o1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),_r=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Ln=(function(r){o1(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.possibleLeftPairs=[],t.possibleRightPairs=[],t}return e.prototype.decodeRow=function(t,n,i){var a,o,s,f,u=this.decodePair(n,!1,t,i);e.addOrTally(this.possibleLeftPairs,u),n.reverse();var c=this.decodePair(n,!0,t,i);e.addOrTally(this.possibleRightPairs,c),n.reverse();try{for(var l=_r(this.possibleLeftPairs),h=l.next();!h.done;h=l.next()){var d=h.value;if(d.getCount()>1)try{for(var v=(s=void 0,_r(this.possibleRightPairs)),p=v.next();!p.done;p=v.next()){var x=p.value;if(x.getCount()>1&&e.checkChecksum(d,x))return e.constructResult(d,x)}}catch(y){s={error:y}}finally{try{p&&!p.done&&(f=v.return)&&f.call(v)}finally{if(s)throw s.error}}}}catch(y){a={error:y}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(a)throw a.error}}throw new I},e.addOrTally=function(t,n){var i,a;if(n!=null){var o=!1;try{for(var s=_r(t),f=s.next();!f.done;f=s.next()){var u=f.value;if(u.getValue()===n.getValue()){u.incrementCount(),o=!0;break}}}catch(c){i={error:c}}finally{try{f&&!f.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}o||t.push(n)}},e.prototype.reset=function(){this.possibleLeftPairs.length=0,this.possibleRightPairs.length=0},e.constructResult=function(t,n){for(var i=4537077*t.getValue()+n.getValue(),a=new String(i).toString(),o=new X,s=13-a.length;s>0;s--)o.append("0");o.append(a);for(var f=0,s=0;s<13;s++){var u=o.charAt(s).charCodeAt(0)-48;f+=(s&1)===0?3*u:u}f=10-f%10,f===10&&(f=0),o.append(f.toString());var c=t.getFinderPattern().getResultPoints(),l=n.getFinderPattern().getResultPoints();return new Pe(o.toString(),null,0,[c[0],c[1],l[0],l[1]],F.RSS_14,new Date().getTime())},e.checkChecksum=function(t,n){var i=(t.getChecksumPortion()+16*n.getChecksumPortion())%79,a=9*t.getFinderPattern().getValue()+n.getFinderPattern().getValue();return a>72&&a--,a>8&&a--,i===a},e.prototype.decodePair=function(t,n,i,a){try{var o=this.findFinderPattern(t,n),s=this.parseFoundFinderPattern(t,i,n,o),f=a==null?null:a.get(le.NEED_RESULT_POINT_CALLBACK);if(f!=null){var u=(o[0]+o[1])/2;n&&(u=t.getSize()-1-u),f.foundPossibleResultPoint(new N(u,i))}var c=this.decodeDataCharacter(t,s,!0),l=this.decodeDataCharacter(t,s,!1);return new a1(1597*c.getValue()+l.getValue(),c.getChecksumPortion()+4*l.getChecksumPortion(),s)}catch{return null}},e.prototype.decodeDataCharacter=function(t,n,i){for(var a=this.getDataCharacterCounters(),o=0;o<a.length;o++)a[o]=0;if(i)Ae.recordPatternInReverse(t,n.getStartEnd()[0],a);else{Ae.recordPattern(t,n.getStartEnd()[1]+1,a);for(var s=0,f=a.length-1;s<f;s++,f--){var u=a[s];a[s]=a[f],a[f]=u}}for(var c=i?16:15,l=q.sum(new Int32Array(a))/c,h=this.getOddCounts(),d=this.getEvenCounts(),v=this.getOddRoundingErrors(),p=this.getEvenRoundingErrors(),s=0;s<a.length;s++){var x=a[s]/l,y=Math.floor(x+.5);y<1?y=1:y>8&&(y=8);var w=Math.floor(s/2);(s&1)===0?(h[w]=y,v[w]=x-y):(d[w]=y,p[w]=x-y)}this.adjustOddEvenCounts(i,c);for(var A=0,C=0,s=h.length-1;s>=0;s--)C*=9,C+=h[s],A+=h[s];for(var O=0,b=0,s=d.length-1;s>=0;s--)O*=9,O+=d[s],b+=d[s];var T=C+3*O;if(i){if((A&1)!==0||A>12||A<4)throw new I;var D=(12-A)/2,B=e.OUTSIDE_ODD_WIDEST[D],M=9-B,ae=Et.getRSSvalue(h,B,!1),W=Et.getRSSvalue(d,M,!0),Q=e.OUTSIDE_EVEN_TOTAL_SUBSET[D],he=e.OUTSIDE_GSUM[D];return new nr(ae*Q+W+he,T)}else{if((b&1)!==0||b>10||b<4)throw new I;var D=(10-b)/2,B=e.INSIDE_ODD_WIDEST[D],M=9-B,ae=Et.getRSSvalue(h,B,!0),W=Et.getRSSvalue(d,M,!1),Me=e.INSIDE_ODD_TOTAL_SUBSET[D],he=e.INSIDE_GSUM[D];return new nr(W*Me+ae+he,T)}},e.prototype.findFinderPattern=function(t,n){var i=this.getDecodeFinderCounters();i[0]=0,i[1]=0,i[2]=0,i[3]=0;for(var a=t.getSize(),o=!1,s=0;s<a&&(o=!t.get(s),n!==o);)s++;for(var f=0,u=s,c=s;c<a;c++)if(t.get(c)!==o)i[f]++;else{if(f===3){if(pt.isFinderPattern(i))return[u,c];u+=i[0]+i[1],i[0]=i[2],i[1]=i[3],i[2]=0,i[3]=0,f--}else f++;i[f]=1,o=!o}throw new I},e.prototype.parseFoundFinderPattern=function(t,n,i,a){for(var o=t.get(a[0]),s=a[0]-1;s>=0&&o!==t.get(s);)s--;s++;var f=a[0]-s,u=this.getDecodeFinderCounters(),c=new Int32Array(u.length);se.arraycopy(u,0,c,1,u.length-1),c[0]=f;var l=this.parseFinderValue(c,e.FINDER_PATTERNS),h=s,d=a[1];return i&&(h=t.getSize()-1-h,d=t.getSize()-1-d),new Di(l,[s,a[1]],h,d,n)},e.prototype.adjustOddEvenCounts=function(t,n){var i=q.sum(new Int32Array(this.getOddCounts())),a=q.sum(new Int32Array(this.getEvenCounts())),o=!1,s=!1,f=!1,u=!1;t?(i>12?s=!0:i<4&&(o=!0),a>12?u=!0:a<4&&(f=!0)):(i>11?s=!0:i<5&&(o=!0),a>10?u=!0:a<4&&(f=!0));var c=i+a-n,l=(i&1)===(t?1:0),h=(a&1)===1;if(c===1)if(l){if(h)throw new I;s=!0}else{if(!h)throw new I;u=!0}else if(c===-1)if(l){if(h)throw new I;o=!0}else{if(!h)throw new I;f=!0}else if(c===0){if(l){if(!h)throw new I;i<a?(o=!0,u=!0):(s=!0,f=!0)}else if(h)throw new I}else throw new I;if(o){if(s)throw new I;pt.increment(this.getOddCounts(),this.getOddRoundingErrors())}if(s&&pt.decrement(this.getOddCounts(),this.getOddRoundingErrors()),f){if(u)throw new I;pt.increment(this.getEvenCounts(),this.getOddRoundingErrors())}u&&pt.decrement(this.getEvenCounts(),this.getEvenRoundingErrors())},e.OUTSIDE_EVEN_TOTAL_SUBSET=[1,10,34,70,126],e.INSIDE_ODD_TOTAL_SUBSET=[4,20,48,81],e.OUTSIDE_GSUM=[0,161,961,2015,2715],e.INSIDE_GSUM=[0,336,1036,1516],e.OUTSIDE_ODD_WIDEST=[8,6,4,3,1],e.INSIDE_ODD_WIDEST=[2,4,6,8],e.FINDER_PATTERNS=[Int32Array.from([3,8,2,1]),Int32Array.from([3,5,5,1]),Int32Array.from([3,3,7,1]),Int32Array.from([3,1,9,1]),Int32Array.from([2,7,4,1]),Int32Array.from([2,5,6,1]),Int32Array.from([2,3,8,1]),Int32Array.from([1,5,7,1]),Int32Array.from([1,3,9,1])],e})(pt),s1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Mt=(function(r){s1(e,r);function e(t){var n=r.call(this)||this;n.readers=[];var i=t?t.get(le.POSSIBLE_FORMATS):null,a=t&&t.get(le.ASSUME_CODE_39_CHECK_DIGIT)!==void 0,o=t&&t.get(le.ENABLE_CODE_39_EXTENDED_MODE)!==void 0;return i&&((i.includes(F.EAN_13)||i.includes(F.UPC_A)||i.includes(F.EAN_8)||i.includes(F.UPC_E))&&n.readers.push(new wr(t)),i.includes(F.CODE_39)&&n.readers.push(new On(a,o)),i.includes(F.CODE_93)&&n.readers.push(new bn),i.includes(F.CODE_128)&&n.readers.push(new In),i.includes(F.ITF)&&n.readers.push(new Tn),i.includes(F.CODABAR)&&n.readers.push(new Ts),i.includes(F.RSS_14)&&n.readers.push(new Ln),i.includes(F.RSS_EXPANDED)&&(console.warn("RSS Expanded reader IS NOT ready for production yet! use at your own risk."),n.readers.push(new n1))),n.readers.length===0&&(n.readers.push(new wr(t)),n.readers.push(new On),n.readers.push(new bn),n.readers.push(new wr(t)),n.readers.push(new In),n.readers.push(new Tn),n.readers.push(new Ln)),n}return e.prototype.decodeRow=function(t,n,i){for(var a=0;a<this.readers.length;a++)try{return this.readers[a].decodeRow(t,n,i)}catch{}throw new I},e.prototype.reset=function(){this.readers.forEach(function(t){return t.reset()})},e})(Ae),f1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){f1(e,r);function e(t,n){return t===void 0&&(t=500),r.call(this,new Mt(n),t,n)||this}return e})(bt);var kn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},te=(function(){function r(e,t,n){this.ecCodewords=e,this.ecBlocks=[t],n&&this.ecBlocks.push(n)}return r.prototype.getECCodewords=function(){return this.ecCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r})(),J=(function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r})(),u1=(function(){function r(e,t,n,i,a,o){var s,f;this.versionNumber=e,this.symbolSizeRows=t,this.symbolSizeColumns=n,this.dataRegionSizeRows=i,this.dataRegionSizeColumns=a,this.ecBlocks=o;var u=0,c=o.getECCodewords(),l=o.getECBlocks();try{for(var h=kn(l),d=h.next();!d.done;d=h.next()){var v=d.value;u+=v.getCount()*(v.getDataCodewords()+c)}}catch(p){s={error:p}}finally{try{d&&!d.done&&(f=h.return)&&f.call(h)}finally{if(s)throw s.error}}this.totalCodewords=u}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getSymbolSizeRows=function(){return this.symbolSizeRows},r.prototype.getSymbolSizeColumns=function(){return this.symbolSizeColumns},r.prototype.getDataRegionSizeRows=function(){return this.dataRegionSizeRows},r.prototype.getDataRegionSizeColumns=function(){return this.dataRegionSizeColumns},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getECBlocks=function(){return this.ecBlocks},r.getVersionForDimensions=function(e,t){var n,i;if((e&1)!==0||(t&1)!==0)throw new R;try{for(var a=kn(r.VERSIONS),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.symbolSizeRows===e&&s.symbolSizeColumns===t)return s}}catch(f){n={error:f}}finally{try{o&&!o.done&&(i=a.return)&&i.call(a)}finally{if(n)throw n.error}}throw new R},r.prototype.toString=function(){return""+this.versionNumber},r.buildVersions=function(){return[new r(1,10,10,8,8,new te(5,new J(1,3))),new r(2,12,12,10,10,new te(7,new J(1,5))),new r(3,14,14,12,12,new te(10,new J(1,8))),new r(4,16,16,14,14,new te(12,new J(1,12))),new r(5,18,18,16,16,new te(14,new J(1,18))),new r(6,20,20,18,18,new te(18,new J(1,22))),new r(7,22,22,20,20,new te(20,new J(1,30))),new r(8,24,24,22,22,new te(24,new J(1,36))),new r(9,26,26,24,24,new te(28,new J(1,44))),new r(10,32,32,14,14,new te(36,new J(1,62))),new r(11,36,36,16,16,new te(42,new J(1,86))),new r(12,40,40,18,18,new te(48,new J(1,114))),new r(13,44,44,20,20,new te(56,new J(1,144))),new r(14,48,48,22,22,new te(68,new J(1,174))),new r(15,52,52,24,24,new te(42,new J(2,102))),new r(16,64,64,14,14,new te(56,new J(2,140))),new r(17,72,72,16,16,new te(36,new J(4,92))),new r(18,80,80,18,18,new te(48,new J(4,114))),new r(19,88,88,20,20,new te(56,new J(4,144))),new r(20,96,96,22,22,new te(68,new J(4,174))),new r(21,104,104,24,24,new te(56,new J(6,136))),new r(22,120,120,18,18,new te(68,new J(6,175))),new r(23,132,132,20,20,new te(62,new J(8,163))),new r(24,144,144,22,22,new te(62,new J(8,156),new J(2,155))),new r(25,8,18,6,16,new te(7,new J(1,5))),new r(26,8,32,6,14,new te(11,new J(1,10))),new r(27,12,26,10,24,new te(14,new J(1,16))),new r(28,12,36,10,16,new te(18,new J(1,22))),new r(29,16,36,14,16,new te(24,new J(1,32))),new r(30,16,48,14,22,new te(28,new J(1,49)))]},r.VERSIONS=r.buildVersions(),r})(),c1=(function(){function r(e){var t=e.getHeight();if(t<8||t>144||(t&1)!==0)throw new R;this.version=r.readVersion(e),this.mappingBitMatrix=this.extractDataRegion(e),this.readMappingMatrix=new it(this.mappingBitMatrix.getWidth(),this.mappingBitMatrix.getHeight())}return r.prototype.getVersion=function(){return this.version},r.readVersion=function(e){var t=e.getHeight(),n=e.getWidth();return u1.getVersionForDimensions(t,n)},r.prototype.readCodewords=function(){var e=new Int8Array(this.version.getTotalCodewords()),t=0,n=4,i=0,a=this.mappingBitMatrix.getHeight(),o=this.mappingBitMatrix.getWidth(),s=!1,f=!1,u=!1,c=!1;do if(n===a&&i===0&&!s)e[t++]=this.readCorner1(a,o)&255,n-=2,i+=2,s=!0;else if(n===a-2&&i===0&&(o&3)!==0&&!f)e[t++]=this.readCorner2(a,o)&255,n-=2,i+=2,f=!0;else if(n===a+4&&i===2&&(o&7)===0&&!u)e[t++]=this.readCorner3(a,o)&255,n-=2,i+=2,u=!0;else if(n===a-2&&i===0&&(o&7)===4&&!c)e[t++]=this.readCorner4(a,o)&255,n-=2,i+=2,c=!0;else{do n<a&&i>=0&&!this.readMappingMatrix.get(i,n)&&(e[t++]=this.readUtah(n,i,a,o)&255),n-=2,i+=2;while(n>=0&&i<o);n+=1,i+=3;do n>=0&&i<o&&!this.readMappingMatrix.get(i,n)&&(e[t++]=this.readUtah(n,i,a,o)&255),n+=2,i-=2;while(n<a&&i>=0);n+=3,i+=1}while(n<a||i<o);if(t!==this.version.getTotalCodewords())throw new R;return e},r.prototype.readModule=function(e,t,n,i){return e<0&&(e+=n,t+=4-(n+4&7)),t<0&&(t+=i,e+=4-(i+4&7)),this.readMappingMatrix.set(t,e),this.mappingBitMatrix.get(t,e)},r.prototype.readUtah=function(e,t,n,i){var a=0;return this.readModule(e-2,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-2,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e-1,t,n,i)&&(a|=1),a<<=1,this.readModule(e,t-2,n,i)&&(a|=1),a<<=1,this.readModule(e,t-1,n,i)&&(a|=1),a<<=1,this.readModule(e,t,n,i)&&(a|=1),a},r.prototype.readCorner1=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,1,e,t)&&(n|=1),n<<=1,this.readModule(e-1,2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.readCorner2=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-4,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner3=function(e,t){var n=0;return this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(0,t-3,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-3,e,t)&&(n|=1),n<<=1,this.readModule(1,t-2,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n},r.prototype.readCorner4=function(e,t){var n=0;return this.readModule(e-3,0,e,t)&&(n|=1),n<<=1,this.readModule(e-2,0,e,t)&&(n|=1),n<<=1,this.readModule(e-1,0,e,t)&&(n|=1),n<<=1,this.readModule(0,t-2,e,t)&&(n|=1),n<<=1,this.readModule(0,t-1,e,t)&&(n|=1),n<<=1,this.readModule(1,t-1,e,t)&&(n|=1),n<<=1,this.readModule(2,t-1,e,t)&&(n|=1),n<<=1,this.readModule(3,t-1,e,t)&&(n|=1),n},r.prototype.extractDataRegion=function(e){var t=this.version.getSymbolSizeRows(),n=this.version.getSymbolSizeColumns();if(e.getHeight()!==t)throw new L("Dimension of bitMatrix must match the version size");for(var i=this.version.getDataRegionSizeRows(),a=this.version.getDataRegionSizeColumns(),o=t/i|0,s=n/a|0,f=o*i,u=s*a,c=new it(u,f),l=0;l<o;++l)for(var h=l*i,d=0;d<s;++d)for(var v=d*a,p=0;p<i;++p)for(var x=l*(i+2)+1+p,y=h+p,w=0;w<a;++w){var A=d*(a+2)+1+w;if(e.get(A,x)){var C=v+w;c.set(C,y)}}return c},r})(),Un=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},l1=(function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t){var n,i,a,o,s=t.getECBlocks(),f=0,u=s.getECBlocks();try{for(var c=Un(u),l=c.next();!l.done;l=c.next()){var h=l.value;f+=h.getCount()}}catch(he){n={error:he}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}var d=new Array(f),v=0;try{for(var p=Un(u),x=p.next();!x.done;x=p.next())for(var h=x.value,y=0;y<h.getCount();y++){var w=h.getDataCodewords(),A=s.getECCodewords()+w;d[v++]=new r(w,new Uint8Array(A))}}catch(he){a={error:he}}finally{try{x&&!x.done&&(o=p.return)&&o.call(p)}finally{if(a)throw a.error}}for(var C=d[0].codewords.length,O=C-s.getECCodewords(),b=O-1,T=0,y=0;y<b;y++)for(var D=0;D<v;D++)d[D].codewords[y]=e[T++];for(var B=t.getVersionNumber()===24,M=B?8:v,D=0;D<M;D++)d[D].codewords[O-1]=e[T++];for(var ae=d[0].codewords.length,y=O;y<ae;y++)for(var D=0;D<v;D++){var W=B?(D+8)%v:D,Q=B&&W>7?y-1:y;d[W].codewords[Q]=e[T++]}if(T!==e.length)throw new L;return d},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r})(),Mi=(function(){function r(e){this.bytes=e,this.byteOffset=0,this.bitOffset=0}return r.prototype.getBitOffset=function(){return this.bitOffset},r.prototype.getByteOffset=function(){return this.byteOffset},r.prototype.readBits=function(e){if(e<1||e>32||e>this.available())throw new L(""+e);var t=0,n=this.bitOffset,i=this.byteOffset,a=this.bytes;if(n>0){var o=8-n,s=e<o?e:o,f=o-s,u=255>>8-s<<f;t=(a[i]&u)>>f,e-=s,n+=s,n===8&&(n=0,i++)}if(e>0){for(;e>=8;)t=t<<8|a[i]&255,i++,e-=8;if(e>0){var f=8-e,u=255>>f<<f;t=t<<e|(a[i]&u)>>f,n+=e}}return this.bitOffset=n,this.byteOffset=i,t},r.prototype.available=function(){return 8*(this.bytes.length-this.byteOffset)-this.bitOffset},r})(),ge;(function(r){r[r.PAD_ENCODE=0]="PAD_ENCODE",r[r.ASCII_ENCODE=1]="ASCII_ENCODE",r[r.C40_ENCODE=2]="C40_ENCODE",r[r.TEXT_ENCODE=3]="TEXT_ENCODE",r[r.ANSIX12_ENCODE=4]="ANSIX12_ENCODE",r[r.EDIFACT_ENCODE=5]="EDIFACT_ENCODE",r[r.BASE256_ENCODE=6]="BASE256_ENCODE"})(ge||(ge={}));var h1=(function(){function r(){}return r.decode=function(e){var t=new Mi(e),n=new X,i=new X,a=new Array,o=ge.ASCII_ENCODE;do if(o===ge.ASCII_ENCODE)o=this.decodeAsciiSegment(t,n,i);else{switch(o){case ge.C40_ENCODE:this.decodeC40Segment(t,n);break;case ge.TEXT_ENCODE:this.decodeTextSegment(t,n);break;case ge.ANSIX12_ENCODE:this.decodeAnsiX12Segment(t,n);break;case ge.EDIFACT_ENCODE:this.decodeEdifactSegment(t,n);break;case ge.BASE256_ENCODE:this.decodeBase256Segment(t,n,a);break;default:throw new R}o=ge.ASCII_ENCODE}while(o!==ge.PAD_ENCODE&&t.available()>0);return i.length()>0&&n.append(i.toString()),new lr(e,n.toString(),a.length===0?null:a,null)},r.decodeAsciiSegment=function(e,t,n){var i=!1;do{var a=e.readBits(8);if(a===0)throw new R;if(a<=128)return i&&(a+=128),t.append(String.fromCharCode(a-1)),ge.ASCII_ENCODE;if(a===129)return ge.PAD_ENCODE;if(a<=229){var o=a-130;o<10&&t.append("0"),t.append(""+o)}else switch(a){case 230:return ge.C40_ENCODE;case 231:return ge.BASE256_ENCODE;case 232:t.append("");break;case 233:case 234:break;case 235:i=!0;break;case 236:t.append("[)>05"),n.insert(0,"");break;case 237:t.append("[)>06"),n.insert(0,"");break;case 238:return ge.ANSIX12_ENCODE;case 239:return ge.TEXT_ENCODE;case 240:return ge.EDIFACT_ENCODE;case 241:break;default:if(a!==254||e.available()!==0)throw new R;break}}while(e.available()>0);return ge.ASCII_ENCODE},r.decodeC40Segment=function(e,t){var n=!1,i=[],a=0;do{if(e.available()===8)return;var o=e.readBits(8);if(o===254)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var f=i[s];switch(a){case 0:if(f<3)a=f+1;else if(f<this.C40_BASIC_SET_CHARS.length){var u=this.C40_BASIC_SET_CHARS[f];n?(t.append(String.fromCharCode(u.charCodeAt(0)+128)),n=!1):t.append(u)}else throw new R;break;case 1:n?(t.append(String.fromCharCode(f+128)),n=!1):t.append(String.fromCharCode(f)),a=0;break;case 2:if(f<this.C40_SHIFT2_SET_CHARS.length){var u=this.C40_SHIFT2_SET_CHARS[f];n?(t.append(String.fromCharCode(u.charCodeAt(0)+128)),n=!1):t.append(u)}else switch(f){case 27:t.append("");break;case 30:n=!0;break;default:throw new R}a=0;break;case 3:n?(t.append(String.fromCharCode(f+224)),n=!1):t.append(String.fromCharCode(f+96)),a=0;break;default:throw new R}}}while(e.available()>0)},r.decodeTextSegment=function(e,t){var n=!1,i=[],a=0;do{if(e.available()===8)return;var o=e.readBits(8);if(o===254)return;this.parseTwoBytes(o,e.readBits(8),i);for(var s=0;s<3;s++){var f=i[s];switch(a){case 0:if(f<3)a=f+1;else if(f<this.TEXT_BASIC_SET_CHARS.length){var u=this.TEXT_BASIC_SET_CHARS[f];n?(t.append(String.fromCharCode(u.charCodeAt(0)+128)),n=!1):t.append(u)}else throw new R;break;case 1:n?(t.append(String.fromCharCode(f+128)),n=!1):t.append(String.fromCharCode(f)),a=0;break;case 2:if(f<this.TEXT_SHIFT2_SET_CHARS.length){var u=this.TEXT_SHIFT2_SET_CHARS[f];n?(t.append(String.fromCharCode(u.charCodeAt(0)+128)),n=!1):t.append(u)}else switch(f){case 27:t.append("");break;case 30:n=!0;break;default:throw new R}a=0;break;case 3:if(f<this.TEXT_SHIFT3_SET_CHARS.length){var u=this.TEXT_SHIFT3_SET_CHARS[f];n?(t.append(String.fromCharCode(u.charCodeAt(0)+128)),n=!1):t.append(u),a=0}else throw new R;break;default:throw new R}}}while(e.available()>0)},r.decodeAnsiX12Segment=function(e,t){var n=[];do{if(e.available()===8)return;var i=e.readBits(8);if(i===254)return;this.parseTwoBytes(i,e.readBits(8),n);for(var a=0;a<3;a++){var o=n[a];switch(o){case 0:t.append("\r");break;case 1:t.append("*");break;case 2:t.append(">");break;case 3:t.append(" ");break;default:if(o<14)t.append(String.fromCharCode(o+44));else if(o<40)t.append(String.fromCharCode(o+51));else throw new R;break}}}while(e.available()>0)},r.parseTwoBytes=function(e,t,n){var i=(e<<8)+t-1,a=Math.floor(i/1600);n[0]=a,i-=a*1600,a=Math.floor(i/40),n[1]=a,n[2]=i-a*40},r.decodeEdifactSegment=function(e,t){do{if(e.available()<=16)return;for(var n=0;n<4;n++){var i=e.readBits(6);if(i===31){var a=8-e.getBitOffset();a!==8&&e.readBits(a);return}(i&32)===0&&(i|=64),t.append(String.fromCharCode(i))}}while(e.available()>0)},r.decodeBase256Segment=function(e,t,n){var i=1+e.getByteOffset(),a=this.unrandomize255State(e.readBits(8),i++),o;if(a===0?o=e.available()/8|0:a<250?o=a:o=250*(a-249)+this.unrandomize255State(e.readBits(8),i++),o<0)throw new R;for(var s=new Uint8Array(o),f=0;f<o;f++){if(e.available()<8)throw new R;s[f]=this.unrandomize255State(e.readBits(8),i++)}n.push(s);try{t.append(Ye.decode(s,$.ISO88591))}catch(u){throw new Vt("Platform does not support required encoding: "+u.message)}},r.unrandomize255State=function(e,t){var n=149*t%255+1,i=e-n;return i>=0?i:i+256},r.C40_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],r.C40_SHIFT2_SET_CHARS=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","@","[","\\","]","^","_"],r.TEXT_BASIC_SET_CHARS=["*","*","*"," ","0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],r.TEXT_SHIFT2_SET_CHARS=r.C40_SHIFT2_SET_CHARS,r.TEXT_SHIFT3_SET_CHARS=["`","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","{","|","}","~",""],r})(),d1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},v1=(function(){function r(){this.rsDecoder=new hr(Ve.DATA_MATRIX_FIELD_256)}return r.prototype.decode=function(e){var t,n,i=new c1(e),a=i.getVersion(),o=i.readCodewords(),s=l1.getDataBlocks(o,a),f=0;try{for(var u=d1(s),c=u.next();!c.done;c=u.next()){var l=c.value;f+=l.getNumDataCodewords()}}catch(A){t={error:A}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}for(var h=new Uint8Array(f),d=s.length,v=0;v<d;v++){var p=s[v],x=p.getCodewords(),y=p.getNumDataCodewords();this.correctErrors(x,y);for(var w=0;w<y;w++)h[w*d+v]=x[w]}return h1.decode(h)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new me}for(var i=0;i<t;i++)e[i]=n[i]},r})(),p1=(function(){function r(e){this.image=e,this.rectangleDetector=new Br(this.image)}return r.prototype.detect=function(){var e=this.rectangleDetector.detect(),t=this.detectSolid1(e);if(t=this.detectSolid2(t),t[3]=this.correctTopRight(t),!t[3])throw new I;t=this.shiftToModuleCenter(t);var n=t[0],i=t[1],a=t[2],o=t[3],s=this.transitionsBetween(n,o)+1,f=this.transitionsBetween(a,o)+1;(s&1)===1&&(s+=1),(f&1)===1&&(f+=1),4*s<7*f&&4*f<7*s&&(s=f=Math.max(s,f));var u=r.sampleGrid(this.image,n,i,a,o,s,f);return new Kr(u,[n,i,a,o])},r.shiftPoint=function(e,t,n){var i=(t.getX()-e.getX())/(n+1),a=(t.getY()-e.getY())/(n+1);return new N(e.getX()+i,e.getY()+a)},r.moveAway=function(e,t,n){var i=e.getX(),a=e.getY();return i<t?i-=1:i+=1,a<n?a-=1:a+=1,new N(i,a)},r.prototype.detectSolid1=function(e){var t=e[0],n=e[1],i=e[3],a=e[2],o=this.transitionsBetween(t,n),s=this.transitionsBetween(n,i),f=this.transitionsBetween(i,a),u=this.transitionsBetween(a,t),c=o,l=[a,t,n,i];return c>s&&(c=s,l[0]=t,l[1]=n,l[2]=i,l[3]=a),c>f&&(c=f,l[0]=n,l[1]=i,l[2]=a,l[3]=t),c>u&&(l[0]=i,l[1]=a,l[2]=t,l[3]=n),l},r.prototype.detectSolid2=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=r.shiftPoint(n,i,(o+1)*4),f=r.shiftPoint(i,n,(o+1)*4),u=this.transitionsBetween(s,t),c=this.transitionsBetween(f,a);return u<c?(e[0]=t,e[1]=n,e[2]=i,e[3]=a):(e[0]=n,e[1]=i,e[2]=a,e[3]=t),e},r.prototype.correctTopRight=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a),s=this.transitionsBetween(n,a),f=r.shiftPoint(t,n,(s+1)*4),u=r.shiftPoint(i,n,(o+1)*4);o=this.transitionsBetween(f,a),s=this.transitionsBetween(u,a);var c=new N(a.getX()+(i.getX()-n.getX())/(o+1),a.getY()+(i.getY()-n.getY())/(o+1)),l=new N(a.getX()+(t.getX()-n.getX())/(s+1),a.getY()+(t.getY()-n.getY())/(s+1));if(!this.isValid(c))return this.isValid(l)?l:null;if(!this.isValid(l))return c;var h=this.transitionsBetween(f,c)+this.transitionsBetween(u,c),d=this.transitionsBetween(f,l)+this.transitionsBetween(u,l);return h>d?c:l},r.prototype.shiftToModuleCenter=function(e){var t=e[0],n=e[1],i=e[2],a=e[3],o=this.transitionsBetween(t,a)+1,s=this.transitionsBetween(i,a)+1,f=r.shiftPoint(t,n,s*4),u=r.shiftPoint(i,n,o*4);o=this.transitionsBetween(f,a)+1,s=this.transitionsBetween(u,a)+1,(o&1)===1&&(o+=1),(s&1)===1&&(s+=1);var c=(t.getX()+n.getX()+i.getX()+a.getX())/4,l=(t.getY()+n.getY()+i.getY()+a.getY())/4;t=r.moveAway(t,c,l),n=r.moveAway(n,c,l),i=r.moveAway(i,c,l),a=r.moveAway(a,c,l);var h,d;return f=r.shiftPoint(t,n,s*4),f=r.shiftPoint(f,a,o*4),h=r.shiftPoint(n,t,s*4),h=r.shiftPoint(h,i,o*4),u=r.shiftPoint(i,a,s*4),u=r.shiftPoint(u,n,o*4),d=r.shiftPoint(a,i,s*4),d=r.shiftPoint(d,t,o*4),[f,h,u,d]},r.prototype.isValid=function(e){return e.getX()>=0&&e.getX()<this.image.getWidth()&&e.getY()>0&&e.getY()<this.image.getHeight()},r.sampleGrid=function(e,t,n,i,a,o,s){var f=qr.getInstance();return f.sampleGrid(e,o,s,.5,.5,o-.5,.5,o-.5,s-.5,.5,s-.5,t.getX(),t.getY(),a.getX(),a.getY(),i.getX(),i.getY(),n.getX(),n.getY())},r.prototype.transitionsBetween=function(e,t){var n=Math.trunc(e.getX()),i=Math.trunc(e.getY()),a=Math.trunc(t.getX()),o=Math.trunc(t.getY()),s=Math.abs(o-i)>Math.abs(a-n);if(s){var f=n;n=i,i=f,f=a,a=o,o=f}for(var u=Math.abs(a-n),c=Math.abs(o-i),l=-u/2,h=i<o?1:-1,d=n<a?1:-1,v=0,p=this.image.get(s?i:n,s?n:i),x=n,y=i;x!==a;x+=d){var w=this.image.get(s?y:x,s?x:y);if(w!==p&&(v++,p=w),l+=c,l>0){if(y===o)break;y+=h,l-=u}}return v},r})(),kr=(function(){function r(){this.decoder=new v1}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n,i;if(t!=null&&t.has(le.PURE_BARCODE)){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decode(a),i=r.NO_POINTS}else{var o=new p1(e.getBlackMatrix()).detect();n=this.decoder.decode(o.getBits()),i=o.getPoints()}var s=n.getRawBytes(),f=new Pe(n.getText(),s,8*s.length,i,F.DATA_MATRIX,se.currentTimeMillis()),u=n.getByteSegments();u!=null&&f.putMetadata(Re.BYTE_SEGMENTS,u);var c=n.getECLevel();return c!=null&&f.putMetadata(Re.ERROR_CORRECTION_LEVEL,c),f},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t==null||n==null)throw new I;var i=this.moduleSize(t,e),a=t[1],o=n[1],s=t[0],f=n[0],u=(f-s+1)/i,c=(o-a+1)/i;if(u<=0||c<=0)throw new I;var l=i/2;a+=l,s+=l;for(var h=new it(u,c),d=0;d<c;d++)for(var v=a+d*i,p=0;p<u;p++)e.get(s+p*i,v)&&h.set(p,d);return h},r.moduleSize=function(e,t){for(var n=t.getWidth(),i=e[0],a=e[1];i<n&&t.get(i,a);)i++;if(i===n)throw new I;var o=i-e[0];if(o===0)throw new I;return o},r.NO_POINTS=[],r})(),g1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){g1(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new kr,t)||this}return e})(bt);var Ct;(function(r){r[r.L=0]="L",r[r.M=1]="M",r[r.Q=2]="Q",r[r.H=3]="H"})(Ct||(Ct={}));var x1=(function(){function r(e,t,n){this.value=e,this.stringValue=t,this.bits=n,r.FOR_BITS.set(n,this),r.FOR_VALUE.set(e,this)}return r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.fromString=function(e){switch(e){case"L":return r.L;case"M":return r.M;case"Q":return r.Q;case"H":return r.H;default:throw new qe(e+"not available")}},r.prototype.toString=function(){return this.stringValue},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.forBits=function(e){if(e<0||e>=r.FOR_BITS.size)throw new L;return r.FOR_BITS.get(e)},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.L=new r(Ct.L,"L",1),r.M=new r(Ct.M,"M",0),r.Q=new r(Ct.Q,"Q",3),r.H=new r(Ct.H,"H",2),r})(),y1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Bi=(function(){function r(e){this.errorCorrectionLevel=x1.forBits(e>>3&3),this.dataMask=e&7}return r.numBitsDiffering=function(e,t){return z.bitCount(e^t)},r.decodeFormatInformation=function(e,t){var n=r.doDecodeFormatInformation(e,t);return n!==null?n:r.doDecodeFormatInformation(e^r.FORMAT_INFO_MASK_QR,t^r.FORMAT_INFO_MASK_QR)},r.doDecodeFormatInformation=function(e,t){var n,i,a=Number.MAX_SAFE_INTEGER,o=0;try{for(var s=y1(r.FORMAT_INFO_DECODE_LOOKUP),f=s.next();!f.done;f=s.next()){var u=f.value,c=u[0];if(c===e||c===t)return new r(u[1]);var l=r.numBitsDiffering(e,c);l<a&&(o=u[1],a=l),e!==t&&(l=r.numBitsDiffering(t,c),l<a&&(o=u[1],a=l))}}catch(h){n={error:h}}finally{try{f&&!f.done&&(i=s.return)&&i.call(s)}finally{if(n)throw n.error}}return a<=3?new r(o):null},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getDataMask=function(){return this.dataMask},r.prototype.hashCode=function(){return this.errorCorrectionLevel.getBits()<<3|this.dataMask},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.errorCorrectionLevel===t.errorCorrectionLevel&&this.dataMask===t.dataMask},r.FORMAT_INFO_MASK_QR=21522,r.FORMAT_INFO_DECODE_LOOKUP=[Int32Array.from([21522,0]),Int32Array.from([20773,1]),Int32Array.from([24188,2]),Int32Array.from([23371,3]),Int32Array.from([17913,4]),Int32Array.from([16590,5]),Int32Array.from([20375,6]),Int32Array.from([19104,7]),Int32Array.from([30660,8]),Int32Array.from([29427,9]),Int32Array.from([32170,10]),Int32Array.from([30877,11]),Int32Array.from([26159,12]),Int32Array.from([25368,13]),Int32Array.from([27713,14]),Int32Array.from([26998,15]),Int32Array.from([5769,16]),Int32Array.from([5054,17]),Int32Array.from([7399,18]),Int32Array.from([6608,19]),Int32Array.from([1890,20]),Int32Array.from([597,21]),Int32Array.from([3340,22]),Int32Array.from([2107,23]),Int32Array.from([13663,24]),Int32Array.from([12392,25]),Int32Array.from([16177,26]),Int32Array.from([14854,27]),Int32Array.from([9396,28]),Int32Array.from([8579,29]),Int32Array.from([11994,30]),Int32Array.from([11245,31])],r})(),w1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=(function(){function r(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.ecCodewordsPerBlock=e,this.ecBlocks=t}return r.prototype.getECCodewordsPerBlock=function(){return this.ecCodewordsPerBlock},r.prototype.getNumBlocks=function(){var e,t,n=0,i=this.ecBlocks;try{for(var a=w1(i),o=a.next();!o.done;o=a.next()){var s=o.value;n+=s.getCount()}}catch(f){e={error:f}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n},r.prototype.getTotalECCodewords=function(){return this.ecCodewordsPerBlock*this.getNumBlocks()},r.prototype.getECBlocks=function(){return this.ecBlocks},r})(),g=(function(){function r(e,t){this.count=e,this.dataCodewords=t}return r.prototype.getCount=function(){return this.count},r.prototype.getDataCodewords=function(){return this.dataCodewords},r})(),_1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},wt=(function(){function r(e,t){for(var n,i,a=[],o=2;o<arguments.length;o++)a[o-2]=arguments[o];this.versionNumber=e,this.alignmentPatternCenters=t,this.ecBlocks=a;var s=0,f=a[0].getECCodewordsPerBlock(),u=a[0].getECBlocks();try{for(var c=_1(u),l=c.next();!l.done;l=c.next()){var h=l.value;s+=h.getCount()*(h.getDataCodewords()+f)}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}this.totalCodewords=s}return r.prototype.getVersionNumber=function(){return this.versionNumber},r.prototype.getAlignmentPatternCenters=function(){return this.alignmentPatternCenters},r.prototype.getTotalCodewords=function(){return this.totalCodewords},r.prototype.getDimensionForVersion=function(){return 17+4*this.versionNumber},r.prototype.getECBlocksForLevel=function(e){return this.ecBlocks[e.getValue()]},r.getProvisionalVersionForDimension=function(e){if(e%4!==1)throw new R;try{return this.getVersionForNumber((e-17)/4)}catch{throw new R}},r.getVersionForNumber=function(e){if(e<1||e>40)throw new L;return r.VERSIONS[e-1]},r.decodeVersionInformation=function(e){for(var t=Number.MAX_SAFE_INTEGER,n=0,i=0;i<r.VERSION_DECODE_INFO.length;i++){var a=r.VERSION_DECODE_INFO[i];if(a===e)return r.getVersionForNumber(i+7);var o=Bi.numBitsDiffering(e,a);o<t&&(n=i+7,t=o)}return t<=3?r.getVersionForNumber(n):null},r.prototype.buildFunctionPattern=function(){var e=this.getDimensionForVersion(),t=new it(e);t.setRegion(0,0,9,9),t.setRegion(e-8,0,8,9),t.setRegion(0,e-8,9,8);for(var n=this.alignmentPatternCenters.length,i=0;i<n;i++)for(var a=this.alignmentPatternCenters[i]-2,o=0;o<n;o++)i===0&&(o===0||o===n-1)||i===n-1&&o===0||t.setRegion(this.alignmentPatternCenters[o]-2,a,5,5);return t.setRegion(6,9,1,e-17),t.setRegion(9,6,e-17,1),this.versionNumber>6&&(t.setRegion(e-11,0,3,6),t.setRegion(0,e-11,6,3)),t},r.prototype.toString=function(){return""+this.versionNumber},r.VERSION_DECODE_INFO=Int32Array.from([31892,34236,39577,42195,48118,51042,55367,58893,63784,68472,70749,76311,79154,84390,87683,92361,96236,102084,102881,110507,110734,117786,119615,126325,127568,133589,136944,141498,145311,150283,152622,158308,161089,167017]),r.VERSIONS=[new r(1,new Int32Array(0),new E(7,new g(1,19)),new E(10,new g(1,16)),new E(13,new g(1,13)),new E(17,new g(1,9))),new r(2,Int32Array.from([6,18]),new E(10,new g(1,34)),new E(16,new g(1,28)),new E(22,new g(1,22)),new E(28,new g(1,16))),new r(3,Int32Array.from([6,22]),new E(15,new g(1,55)),new E(26,new g(1,44)),new E(18,new g(2,17)),new E(22,new g(2,13))),new r(4,Int32Array.from([6,26]),new E(20,new g(1,80)),new E(18,new g(2,32)),new E(26,new g(2,24)),new E(16,new g(4,9))),new r(5,Int32Array.from([6,30]),new E(26,new g(1,108)),new E(24,new g(2,43)),new E(18,new g(2,15),new g(2,16)),new E(22,new g(2,11),new g(2,12))),new r(6,Int32Array.from([6,34]),new E(18,new g(2,68)),new E(16,new g(4,27)),new E(24,new g(4,19)),new E(28,new g(4,15))),new r(7,Int32Array.from([6,22,38]),new E(20,new g(2,78)),new E(18,new g(4,31)),new E(18,new g(2,14),new g(4,15)),new E(26,new g(4,13),new g(1,14))),new r(8,Int32Array.from([6,24,42]),new E(24,new g(2,97)),new E(22,new g(2,38),new g(2,39)),new E(22,new g(4,18),new g(2,19)),new E(26,new g(4,14),new g(2,15))),new r(9,Int32Array.from([6,26,46]),new E(30,new g(2,116)),new E(22,new g(3,36),new g(2,37)),new E(20,new g(4,16),new g(4,17)),new E(24,new g(4,12),new g(4,13))),new r(10,Int32Array.from([6,28,50]),new E(18,new g(2,68),new g(2,69)),new E(26,new g(4,43),new g(1,44)),new E(24,new g(6,19),new g(2,20)),new E(28,new g(6,15),new g(2,16))),new r(11,Int32Array.from([6,30,54]),new E(20,new g(4,81)),new E(30,new g(1,50),new g(4,51)),new E(28,new g(4,22),new g(4,23)),new E(24,new g(3,12),new g(8,13))),new r(12,Int32Array.from([6,32,58]),new E(24,new g(2,92),new g(2,93)),new E(22,new g(6,36),new g(2,37)),new E(26,new g(4,20),new g(6,21)),new E(28,new g(7,14),new g(4,15))),new r(13,Int32Array.from([6,34,62]),new E(26,new g(4,107)),new E(22,new g(8,37),new g(1,38)),new E(24,new g(8,20),new g(4,21)),new E(22,new g(12,11),new g(4,12))),new r(14,Int32Array.from([6,26,46,66]),new E(30,new g(3,115),new g(1,116)),new E(24,new g(4,40),new g(5,41)),new E(20,new g(11,16),new g(5,17)),new E(24,new g(11,12),new g(5,13))),new r(15,Int32Array.from([6,26,48,70]),new E(22,new g(5,87),new g(1,88)),new E(24,new g(5,41),new g(5,42)),new E(30,new g(5,24),new g(7,25)),new E(24,new g(11,12),new g(7,13))),new r(16,Int32Array.from([6,26,50,74]),new E(24,new g(5,98),new g(1,99)),new E(28,new g(7,45),new g(3,46)),new E(24,new g(15,19),new g(2,20)),new E(30,new g(3,15),new g(13,16))),new r(17,Int32Array.from([6,30,54,78]),new E(28,new g(1,107),new g(5,108)),new E(28,new g(10,46),new g(1,47)),new E(28,new g(1,22),new g(15,23)),new E(28,new g(2,14),new g(17,15))),new r(18,Int32Array.from([6,30,56,82]),new E(30,new g(5,120),new g(1,121)),new E(26,new g(9,43),new g(4,44)),new E(28,new g(17,22),new g(1,23)),new E(28,new g(2,14),new g(19,15))),new r(19,Int32Array.from([6,30,58,86]),new E(28,new g(3,113),new g(4,114)),new E(26,new g(3,44),new g(11,45)),new E(26,new g(17,21),new g(4,22)),new E(26,new g(9,13),new g(16,14))),new r(20,Int32Array.from([6,34,62,90]),new E(28,new g(3,107),new g(5,108)),new E(26,new g(3,41),new g(13,42)),new E(30,new g(15,24),new g(5,25)),new E(28,new g(15,15),new g(10,16))),new r(21,Int32Array.from([6,28,50,72,94]),new E(28,new g(4,116),new g(4,117)),new E(26,new g(17,42)),new E(28,new g(17,22),new g(6,23)),new E(30,new g(19,16),new g(6,17))),new r(22,Int32Array.from([6,26,50,74,98]),new E(28,new g(2,111),new g(7,112)),new E(28,new g(17,46)),new E(30,new g(7,24),new g(16,25)),new E(24,new g(34,13))),new r(23,Int32Array.from([6,30,54,78,102]),new E(30,new g(4,121),new g(5,122)),new E(28,new g(4,47),new g(14,48)),new E(30,new g(11,24),new g(14,25)),new E(30,new g(16,15),new g(14,16))),new r(24,Int32Array.from([6,28,54,80,106]),new E(30,new g(6,117),new g(4,118)),new E(28,new g(6,45),new g(14,46)),new E(30,new g(11,24),new g(16,25)),new E(30,new g(30,16),new g(2,17))),new r(25,Int32Array.from([6,32,58,84,110]),new E(26,new g(8,106),new g(4,107)),new E(28,new g(8,47),new g(13,48)),new E(30,new g(7,24),new g(22,25)),new E(30,new g(22,15),new g(13,16))),new r(26,Int32Array.from([6,30,58,86,114]),new E(28,new g(10,114),new g(2,115)),new E(28,new g(19,46),new g(4,47)),new E(28,new g(28,22),new g(6,23)),new E(30,new g(33,16),new g(4,17))),new r(27,Int32Array.from([6,34,62,90,118]),new E(30,new g(8,122),new g(4,123)),new E(28,new g(22,45),new g(3,46)),new E(30,new g(8,23),new g(26,24)),new E(30,new g(12,15),new g(28,16))),new r(28,Int32Array.from([6,26,50,74,98,122]),new E(30,new g(3,117),new g(10,118)),new E(28,new g(3,45),new g(23,46)),new E(30,new g(4,24),new g(31,25)),new E(30,new g(11,15),new g(31,16))),new r(29,Int32Array.from([6,30,54,78,102,126]),new E(30,new g(7,116),new g(7,117)),new E(28,new g(21,45),new g(7,46)),new E(30,new g(1,23),new g(37,24)),new E(30,new g(19,15),new g(26,16))),new r(30,Int32Array.from([6,26,52,78,104,130]),new E(30,new g(5,115),new g(10,116)),new E(28,new g(19,47),new g(10,48)),new E(30,new g(15,24),new g(25,25)),new E(30,new g(23,15),new g(25,16))),new r(31,Int32Array.from([6,30,56,82,108,134]),new E(30,new g(13,115),new g(3,116)),new E(28,new g(2,46),new g(29,47)),new E(30,new g(42,24),new g(1,25)),new E(30,new g(23,15),new g(28,16))),new r(32,Int32Array.from([6,34,60,86,112,138]),new E(30,new g(17,115)),new E(28,new g(10,46),new g(23,47)),new E(30,new g(10,24),new g(35,25)),new E(30,new g(19,15),new g(35,16))),new r(33,Int32Array.from([6,30,58,86,114,142]),new E(30,new g(17,115),new g(1,116)),new E(28,new g(14,46),new g(21,47)),new E(30,new g(29,24),new g(19,25)),new E(30,new g(11,15),new g(46,16))),new r(34,Int32Array.from([6,34,62,90,118,146]),new E(30,new g(13,115),new g(6,116)),new E(28,new g(14,46),new g(23,47)),new E(30,new g(44,24),new g(7,25)),new E(30,new g(59,16),new g(1,17))),new r(35,Int32Array.from([6,30,54,78,102,126,150]),new E(30,new g(12,121),new g(7,122)),new E(28,new g(12,47),new g(26,48)),new E(30,new g(39,24),new g(14,25)),new E(30,new g(22,15),new g(41,16))),new r(36,Int32Array.from([6,24,50,76,102,128,154]),new E(30,new g(6,121),new g(14,122)),new E(28,new g(6,47),new g(34,48)),new E(30,new g(46,24),new g(10,25)),new E(30,new g(2,15),new g(64,16))),new r(37,Int32Array.from([6,28,54,80,106,132,158]),new E(30,new g(17,122),new g(4,123)),new E(28,new g(29,46),new g(14,47)),new E(30,new g(49,24),new g(10,25)),new E(30,new g(24,15),new g(46,16))),new r(38,Int32Array.from([6,32,58,84,110,136,162]),new E(30,new g(4,122),new g(18,123)),new E(28,new g(13,46),new g(32,47)),new E(30,new g(48,24),new g(14,25)),new E(30,new g(42,15),new g(32,16))),new r(39,Int32Array.from([6,26,54,82,110,138,166]),new E(30,new g(20,117),new g(4,118)),new E(28,new g(40,47),new g(7,48)),new E(30,new g(43,24),new g(22,25)),new E(30,new g(10,15),new g(67,16))),new r(40,Int32Array.from([6,30,58,86,114,142,170]),new E(30,new g(19,118),new g(6,119)),new E(28,new g(18,47),new g(31,48)),new E(30,new g(34,24),new g(34,25)),new E(30,new g(20,15),new g(61,16)))],r})(),Ee;(function(r){r[r.DATA_MASK_000=0]="DATA_MASK_000",r[r.DATA_MASK_001=1]="DATA_MASK_001",r[r.DATA_MASK_010=2]="DATA_MASK_010",r[r.DATA_MASK_011=3]="DATA_MASK_011",r[r.DATA_MASK_100=4]="DATA_MASK_100",r[r.DATA_MASK_101=5]="DATA_MASK_101",r[r.DATA_MASK_110=6]="DATA_MASK_110",r[r.DATA_MASK_111=7]="DATA_MASK_111"})(Ee||(Ee={}));var Hn=(function(){function r(e,t){this.value=e,this.isMasked=t}return r.prototype.unmaskBitMatrix=function(e,t){for(var n=0;n<t;n++)for(var i=0;i<t;i++)this.isMasked(n,i)&&e.flip(i,n)},r.values=new Map([[Ee.DATA_MASK_000,new r(Ee.DATA_MASK_000,function(e,t){return(e+t&1)===0})],[Ee.DATA_MASK_001,new r(Ee.DATA_MASK_001,function(e,t){return(e&1)===0})],[Ee.DATA_MASK_010,new r(Ee.DATA_MASK_010,function(e,t){return t%3===0})],[Ee.DATA_MASK_011,new r(Ee.DATA_MASK_011,function(e,t){return(e+t)%3===0})],[Ee.DATA_MASK_100,new r(Ee.DATA_MASK_100,function(e,t){return(Math.floor(e/2)+Math.floor(t/3)&1)===0})],[Ee.DATA_MASK_101,new r(Ee.DATA_MASK_101,function(e,t){return e*t%6===0})],[Ee.DATA_MASK_110,new r(Ee.DATA_MASK_110,function(e,t){return e*t%6<3})],[Ee.DATA_MASK_111,new r(Ee.DATA_MASK_111,function(e,t){return(e+t+e*t%3&1)===0})]]),r})(),A1=(function(){function r(e){var t=e.getHeight();if(t<21||(t&3)!==1)throw new R;this.bitMatrix=e}return r.prototype.readFormatInformation=function(){if(this.parsedFormatInfo!==null&&this.parsedFormatInfo!==void 0)return this.parsedFormatInfo;for(var e=0,t=0;t<6;t++)e=this.copyBit(t,8,e);e=this.copyBit(7,8,e),e=this.copyBit(8,8,e),e=this.copyBit(8,7,e);for(var n=5;n>=0;n--)e=this.copyBit(8,n,e);for(var i=this.bitMatrix.getHeight(),a=0,o=i-7,n=i-1;n>=o;n--)a=this.copyBit(8,n,a);for(var t=i-8;t<i;t++)a=this.copyBit(t,8,a);if(this.parsedFormatInfo=Bi.decodeFormatInformation(e,a),this.parsedFormatInfo!==null)return this.parsedFormatInfo;throw new R},r.prototype.readVersion=function(){if(this.parsedVersion!==null&&this.parsedVersion!==void 0)return this.parsedVersion;var e=this.bitMatrix.getHeight(),t=Math.floor((e-17)/4);if(t<=6)return wt.getVersionForNumber(t);for(var n=0,i=e-11,a=5;a>=0;a--)for(var o=e-9;o>=i;o--)n=this.copyBit(o,a,n);var s=wt.decodeVersionInformation(n);if(s!==null&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;n=0;for(var o=5;o>=0;o--)for(var a=e-9;a>=i;a--)n=this.copyBit(o,a,n);if(s=wt.decodeVersionInformation(n),s!==null&&s.getDimensionForVersion()===e)return this.parsedVersion=s,s;throw new R},r.prototype.copyBit=function(e,t,n){var i=this.isMirror?this.bitMatrix.get(t,e):this.bitMatrix.get(e,t);return i?n<<1|1:n<<1},r.prototype.readCodewords=function(){var e=this.readFormatInformation(),t=this.readVersion(),n=Hn.values.get(e.getDataMask()),i=this.bitMatrix.getHeight();n.unmaskBitMatrix(this.bitMatrix,i);for(var a=t.buildFunctionPattern(),o=!0,s=new Uint8Array(t.getTotalCodewords()),f=0,u=0,c=0,l=i-1;l>0;l-=2){l===6&&l--;for(var h=0;h<i;h++)for(var d=o?i-1-h:h,v=0;v<2;v++)a.get(l-v,d)||(c++,u<<=1,this.bitMatrix.get(l-v,d)&&(u|=1),c===8&&(s[f++]=u,c=0,u=0));o=!o}if(f!==t.getTotalCodewords())throw new R;return s},r.prototype.remask=function(){if(this.parsedFormatInfo!==null){var e=Hn.values.get(this.parsedFormatInfo.getDataMask()),t=this.bitMatrix.getHeight();e.unmaskBitMatrix(this.bitMatrix,t)}},r.prototype.setMirror=function(e){this.parsedVersion=null,this.parsedFormatInfo=null,this.isMirror=e},r.prototype.mirror=function(){for(var e=this.bitMatrix,t=0,n=e.getWidth();t<n;t++)for(var i=t+1,a=e.getHeight();i<a;i++)e.get(t,i)!==e.get(i,t)&&(e.flip(i,t),e.flip(t,i))},r})(),Vn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E1=(function(){function r(e,t){this.numDataCodewords=e,this.codewords=t}return r.getDataBlocks=function(e,t,n){var i,a,o,s;if(e.length!==t.getTotalCodewords())throw new L;var f=t.getECBlocksForLevel(n),u=0,c=f.getECBlocks();try{for(var l=Vn(c),h=l.next();!h.done;h=l.next()){var d=h.value;u+=d.getCount()}}catch(Q){i={error:Q}}finally{try{h&&!h.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}var v=new Array(u),p=0;try{for(var x=Vn(c),y=x.next();!y.done;y=x.next())for(var d=y.value,w=0;w<d.getCount();w++){var A=d.getDataCodewords(),C=f.getECCodewordsPerBlock()+A;v[p++]=new r(A,new Uint8Array(C))}}catch(Q){o={error:Q}}finally{try{y&&!y.done&&(s=x.return)&&s.call(x)}finally{if(o)throw o.error}}for(var O=v[0].codewords.length,b=v.length-1;b>=0;){var T=v[b].codewords.length;if(T===O)break;b--}b++;for(var D=O-f.getECCodewordsPerBlock(),B=0,w=0;w<D;w++)for(var M=0;M<p;M++)v[M].codewords[w]=e[B++];for(var M=b;M<p;M++)v[M].codewords[D]=e[B++];for(var ae=v[0].codewords.length,w=D;w<ae;w++)for(var M=0;M<p;M++){var W=M<b?w:w+1;v[M].codewords[W]=e[B++]}return v},r.prototype.getNumDataCodewords=function(){return this.numDataCodewords},r.prototype.getCodewords=function(){return this.codewords},r})(),ze;(function(r){r[r.TERMINATOR=0]="TERMINATOR",r[r.NUMERIC=1]="NUMERIC",r[r.ALPHANUMERIC=2]="ALPHANUMERIC",r[r.STRUCTURED_APPEND=3]="STRUCTURED_APPEND",r[r.BYTE=4]="BYTE",r[r.ECI=5]="ECI",r[r.KANJI=6]="KANJI",r[r.FNC1_FIRST_POSITION=7]="FNC1_FIRST_POSITION",r[r.FNC1_SECOND_POSITION=8]="FNC1_SECOND_POSITION",r[r.HANZI=9]="HANZI"})(ze||(ze={}));var ce=(function(){function r(e,t,n,i){this.value=e,this.stringValue=t,this.characterCountBitsForVersions=n,this.bits=i,r.FOR_BITS.set(i,this),r.FOR_VALUE.set(e,this)}return r.forBits=function(e){var t=r.FOR_BITS.get(e);if(t===void 0)throw new L;return t},r.prototype.getCharacterCountBits=function(e){var t=e.getVersionNumber(),n;return t<=9?n=0:t<=26?n=1:n=2,this.characterCountBitsForVersions[n]},r.prototype.getValue=function(){return this.value},r.prototype.getBits=function(){return this.bits},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;return this.value===t.value},r.prototype.toString=function(){return this.stringValue},r.FOR_BITS=new Map,r.FOR_VALUE=new Map,r.TERMINATOR=new r(ze.TERMINATOR,"TERMINATOR",Int32Array.from([0,0,0]),0),r.NUMERIC=new r(ze.NUMERIC,"NUMERIC",Int32Array.from([10,12,14]),1),r.ALPHANUMERIC=new r(ze.ALPHANUMERIC,"ALPHANUMERIC",Int32Array.from([9,11,13]),2),r.STRUCTURED_APPEND=new r(ze.STRUCTURED_APPEND,"STRUCTURED_APPEND",Int32Array.from([0,0,0]),3),r.BYTE=new r(ze.BYTE,"BYTE",Int32Array.from([8,16,16]),4),r.ECI=new r(ze.ECI,"ECI",Int32Array.from([0,0,0]),7),r.KANJI=new r(ze.KANJI,"KANJI",Int32Array.from([8,10,12]),8),r.FNC1_FIRST_POSITION=new r(ze.FNC1_FIRST_POSITION,"FNC1_FIRST_POSITION",Int32Array.from([0,0,0]),5),r.FNC1_SECOND_POSITION=new r(ze.FNC1_SECOND_POSITION,"FNC1_SECOND_POSITION",Int32Array.from([0,0,0]),9),r.HANZI=new r(ze.HANZI,"HANZI",Int32Array.from([8,10,12]),13),r})(),C1=(function(){function r(){}return r.decode=function(e,t,n,i){var a=new Mi(e),o=new X,s=new Array,f=-1,u=-1;try{var c=null,l=!1,h=void 0;do{if(a.available()<4)h=ce.TERMINATOR;else{var d=a.readBits(4);h=ce.forBits(d)}switch(h){case ce.TERMINATOR:break;case ce.FNC1_FIRST_POSITION:case ce.FNC1_SECOND_POSITION:l=!0;break;case ce.STRUCTURED_APPEND:if(a.available()<16)throw new R;f=a.readBits(8),u=a.readBits(8);break;case ce.ECI:var v=r.parseECIValue(a);if(c=we.getCharacterSetECIByValue(v),c===null)throw new R;break;case ce.HANZI:var p=a.readBits(4),x=a.readBits(h.getCharacterCountBits(t));p===r.GB2312_SUBSET&&r.decodeHanziSegment(a,o,x);break;default:var y=a.readBits(h.getCharacterCountBits(t));switch(h){case ce.NUMERIC:r.decodeNumericSegment(a,o,y);break;case ce.ALPHANUMERIC:r.decodeAlphanumericSegment(a,o,y,l);break;case ce.BYTE:r.decodeByteSegment(a,o,y,c,s,i);break;case ce.KANJI:r.decodeKanjiSegment(a,o,y);break;default:throw new R}break}}while(h!==ce.TERMINATOR)}catch{throw new R}return new lr(e,o.toString(),s.length===0?null:s,n===null?null:n.toString(),f,u)},r.decodeHanziSegment=function(e,t,n){if(n*13>e.available())throw new R;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/96<<8&4294967295|o%96;s<959?s+=41377:s+=42657,i[a]=s>>8&255,i[a+1]=s&255,a+=2,n--}try{t.append(Ye.decode(i,$.GB2312))}catch(f){throw new R(f)}},r.decodeKanjiSegment=function(e,t,n){if(n*13>e.available())throw new R;for(var i=new Uint8Array(2*n),a=0;n>0;){var o=e.readBits(13),s=o/192<<8&4294967295|o%192;s<7936?s+=33088:s+=49472,i[a]=s>>8,i[a+1]=s,a+=2,n--}try{t.append(Ye.decode(i,$.SHIFT_JIS))}catch(f){throw new R(f)}},r.decodeByteSegment=function(e,t,n,i,a,o){if(8*n>e.available())throw new R;for(var s=new Uint8Array(n),f=0;f<n;f++)s[f]=e.readBits(8);var u;i===null?u=$.guessEncoding(s,o):u=i.getName();try{t.append(Ye.decode(s,u))}catch(c){throw new R(c)}a.push(s)},r.toAlphaNumericChar=function(e){if(e>=r.ALPHANUMERIC_CHARS.length)throw new R;return r.ALPHANUMERIC_CHARS[e]},r.decodeAlphanumericSegment=function(e,t,n,i){for(var a=t.length();n>1;){if(e.available()<11)throw new R;var o=e.readBits(11);t.append(r.toAlphaNumericChar(Math.floor(o/45))),t.append(r.toAlphaNumericChar(o%45)),n-=2}if(n===1){if(e.available()<6)throw new R;t.append(r.toAlphaNumericChar(e.readBits(6)))}if(i)for(var s=a;s<t.length();s++)t.charAt(s)==="%"&&(s<t.length()-1&&t.charAt(s+1)==="%"?t.deleteCharAt(s+1):t.setCharAt(s,""))},r.decodeNumericSegment=function(e,t,n){for(;n>=3;){if(e.available()<10)throw new R;var i=e.readBits(10);if(i>=1e3)throw new R;t.append(r.toAlphaNumericChar(Math.floor(i/100))),t.append(r.toAlphaNumericChar(Math.floor(i/10)%10)),t.append(r.toAlphaNumericChar(i%10)),n-=3}if(n===2){if(e.available()<7)throw new R;var a=e.readBits(7);if(a>=100)throw new R;t.append(r.toAlphaNumericChar(Math.floor(a/10))),t.append(r.toAlphaNumericChar(a%10))}else if(n===1){if(e.available()<4)throw new R;var o=e.readBits(4);if(o>=10)throw new R;t.append(r.toAlphaNumericChar(o))}},r.parseECIValue=function(e){var t=e.readBits(8);if((t&128)===0)return t&127;if((t&192)===128){var n=e.readBits(8);return(t&63)<<8&4294967295|n}if((t&224)===192){var i=e.readBits(16);return(t&31)<<16&4294967295|i}throw new R},r.ALPHANUMERIC_CHARS="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",r.GB2312_SUBSET=1,r})(),Fi=(function(){function r(e){this.mirrored=e}return r.prototype.isMirrored=function(){return this.mirrored},r.prototype.applyMirroredCorrection=function(e){if(!(!this.mirrored||e===null||e.length<3)){var t=e[0];e[0]=e[2],e[2]=t}},r})(),Gn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},m1=(function(){function r(){this.rsDecoder=new hr(Ve.QR_CODE_FIELD_256)}return r.prototype.decodeBooleanArray=function(e,t){return this.decodeBitMatrix(it.parseFromBooleanArray(e),t)},r.prototype.decodeBitMatrix=function(e,t){var n=new A1(e),i=null;try{return this.decodeBitMatrixParser(n,t)}catch(o){i=o}try{n.remask(),n.setMirror(!0),n.readVersion(),n.readFormatInformation(),n.mirror();var a=this.decodeBitMatrixParser(n,t);return a.setOther(new Fi(!0)),a}catch(o){throw i!==null?i:o}},r.prototype.decodeBitMatrixParser=function(e,t){var n,i,a,o,s=e.readVersion(),f=e.readFormatInformation().getErrorCorrectionLevel(),u=e.readCodewords(),c=E1.getDataBlocks(u,s,f),l=0;try{for(var h=Gn(c),d=h.next();!d.done;d=h.next()){var v=d.value;l+=v.getNumDataCodewords()}}catch(b){n={error:b}}finally{try{d&&!d.done&&(i=h.return)&&i.call(h)}finally{if(n)throw n.error}}var p=new Uint8Array(l),x=0;try{for(var y=Gn(c),w=y.next();!w.done;w=y.next()){var v=w.value,A=v.getCodewords(),C=v.getNumDataCodewords();this.correctErrors(A,C);for(var O=0;O<C;O++)p[x++]=A[O]}}catch(b){a={error:b}}finally{try{w&&!w.done&&(o=y.return)&&o.call(y)}finally{if(a)throw a.error}}return C1.decode(p,s,f,t)},r.prototype.correctErrors=function(e,t){var n=new Int32Array(e);try{this.rsDecoder.decode(n,e.length-t)}catch{throw new me}for(var i=0;i<t;i++)e[i]=n[i]},r})(),S1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),I1=(function(r){S1(e,r);function e(t,n,i){var a=r.call(this,t,n)||this;return a.estimatedModuleSize=i,a}return e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){var a=(this.getX()+n)/2,o=(this.getY()+t)/2,s=(this.estimatedModuleSize+i)/2;return new e(a,o,s)},e})(N),O1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},b1=(function(){function r(e,t,n,i,a,o,s){this.image=e,this.startX=t,this.startY=n,this.width=i,this.height=a,this.moduleSize=o,this.resultPointCallback=s,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(3)}return r.prototype.find=function(){for(var e=this.startX,t=this.height,n=this.width,i=e+n,a=this.startY+t/2,o=new Int32Array(3),s=this.image,f=0;f<t;f++){var u=a+((f&1)===0?Math.floor((f+1)/2):-Math.floor((f+1)/2));o[0]=0,o[1]=0,o[2]=0;for(var c=e;c<i&&!s.get(c,u);)c++;for(var l=0;c<i;){if(s.get(c,u))if(l===1)o[1]++;else if(l===2){if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,u,c);if(h!==null)return h}o[0]=o[2],o[1]=1,o[2]=0,l=1}else o[++l]++;else l===1&&l++,o[l]++;c++}if(this.foundPatternCross(o)){var h=this.handlePossibleCenter(o,u,i);if(h!==null)return h}}if(this.possibleCenters.length!==0)return this.possibleCenters[0];throw new I},r.centerFromEnd=function(e,t){return t-e[2]-e[1]/2},r.prototype.foundPatternCross=function(e){for(var t=this.moduleSize,n=t/2,i=0;i<3;i++)if(Math.abs(t-e[i])>=n)return!1;return!0},r.prototype.crossCheckVertical=function(e,t,n,i){var a=this.image,o=a.getHeight(),s=this.crossCheckStateCount;s[0]=0,s[1]=0,s[2]=0;for(var f=e;f>=0&&a.get(t,f)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&!a.get(t,f)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=e+1;f<o&&a.get(t,f)&&s[1]<=n;)s[1]++,f++;if(f===o||s[1]>n)return NaN;for(;f<o&&!a.get(t,f)&&s[2]<=n;)s[2]++,f++;if(s[2]>n)return NaN;var u=s[0]+s[1]+s[2];return 5*Math.abs(u-i)>=2*i?NaN:this.foundPatternCross(s)?r.centerFromEnd(s,f):NaN},r.prototype.handlePossibleCenter=function(e,t,n){var i,a,o=e[0]+e[1]+e[2],s=r.centerFromEnd(e,n),f=this.crossCheckVertical(t,s,2*e[1],o);if(!isNaN(f)){var u=(e[0]+e[1]+e[2])/3;try{for(var c=O1(this.possibleCenters),l=c.next();!l.done;l=c.next()){var h=l.value;if(h.aboutEquals(u,f,s))return h.combineEstimate(f,s,u)}}catch(v){i={error:v}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}var d=new I1(s,f,u);this.possibleCenters.push(d),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(d)}return null},r})(),T1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),R1=(function(r){T1(e,r);function e(t,n,i,a){var o=r.call(this,t,n)||this;return o.estimatedModuleSize=i,o.count=a,a===void 0&&(o.count=1),o}return e.prototype.getEstimatedModuleSize=function(){return this.estimatedModuleSize},e.prototype.getCount=function(){return this.count},e.prototype.aboutEquals=function(t,n,i){if(Math.abs(n-this.getY())<=t&&Math.abs(i-this.getX())<=t){var a=Math.abs(t-this.estimatedModuleSize);return a<=1||a<=this.estimatedModuleSize}return!1},e.prototype.combineEstimate=function(t,n,i){var a=this.count+1,o=(this.count*this.getX()+n)/a,s=(this.count*this.getY()+t)/a,f=(this.count*this.estimatedModuleSize+i)/a;return new e(o,s,f,a)},e})(N),D1=(function(){function r(e){this.bottomLeft=e[0],this.topLeft=e[1],this.topRight=e[2]}return r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r})(),Rt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},N1=(function(){function r(e,t){this.image=e,this.resultPointCallback=t,this.possibleCenters=[],this.crossCheckStateCount=new Int32Array(5),this.resultPointCallback=t}return r.prototype.getImage=function(){return this.image},r.prototype.getPossibleCenters=function(){return this.possibleCenters},r.prototype.find=function(e){var t=e!=null&&e.get(le.TRY_HARDER)!==void 0,n=e!=null&&e.get(le.PURE_BARCODE)!==void 0,i=this.image,a=i.getHeight(),o=i.getWidth(),s=Math.floor(3*a/(4*r.MAX_MODULES));(s<r.MIN_SKIP||t)&&(s=r.MIN_SKIP);for(var f=!1,u=new Int32Array(5),c=s-1;c<a&&!f;c+=s){u[0]=0,u[1]=0,u[2]=0,u[3]=0,u[4]=0;for(var l=0,h=0;h<o;h++)if(i.get(h,c))(l&1)===1&&l++,u[l]++;else if((l&1)===0)if(l===4)if(r.foundPatternCross(u)){var d=this.handlePossibleCenter(u,c,h,n);if(d===!0)if(s=2,this.hasSkipped===!0)f=this.haveMultiplyConfirmedCenters();else{var v=this.findRowSkip();v>u[2]&&(c+=v-u[2]-s,h=o-1)}else{u[0]=u[2],u[1]=u[3],u[2]=u[4],u[3]=1,u[4]=0,l=3;continue}l=0,u[0]=0,u[1]=0,u[2]=0,u[3]=0,u[4]=0}else u[0]=u[2],u[1]=u[3],u[2]=u[4],u[3]=1,u[4]=0,l=3;else u[++l]++;else u[l]++;if(r.foundPatternCross(u)){var d=this.handlePossibleCenter(u,c,o,n);d===!0&&(s=u[0],this.hasSkipped&&(f=this.haveMultiplyConfirmedCenters()))}}var p=this.selectBestPatterns();return N.orderBestPatterns(p),new D1(p)},r.centerFromEnd=function(e,t){return t-e[4]-e[3]-e[2]/2},r.foundPatternCross=function(e){for(var t=0,n=0;n<5;n++){var i=e[n];if(i===0)return!1;t+=i}if(t<7)return!1;var a=t/7,o=a/2;return Math.abs(a-e[0])<o&&Math.abs(a-e[1])<o&&Math.abs(3*a-e[2])<3*o&&Math.abs(a-e[3])<o&&Math.abs(a-e[4])<o},r.prototype.getCrossCheckStateCount=function(){var e=this.crossCheckStateCount;return e[0]=0,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e},r.prototype.crossCheckDiagonal=function(e,t,n,i){for(var a=this.getCrossCheckStateCount(),o=0,s=this.image;e>=o&&t>=o&&s.get(t-o,e-o);)a[2]++,o++;if(e<o||t<o)return!1;for(;e>=o&&t>=o&&!s.get(t-o,e-o)&&a[1]<=n;)a[1]++,o++;if(e<o||t<o||a[1]>n)return!1;for(;e>=o&&t>=o&&s.get(t-o,e-o)&&a[0]<=n;)a[0]++,o++;if(a[0]>n)return!1;var f=s.getHeight(),u=s.getWidth();for(o=1;e+o<f&&t+o<u&&s.get(t+o,e+o);)a[2]++,o++;if(e+o>=f||t+o>=u)return!1;for(;e+o<f&&t+o<u&&!s.get(t+o,e+o)&&a[3]<n;)a[3]++,o++;if(e+o>=f||t+o>=u||a[3]>=n)return!1;for(;e+o<f&&t+o<u&&s.get(t+o,e+o)&&a[4]<n;)a[4]++,o++;if(a[4]>=n)return!1;var c=a[0]+a[1]+a[2]+a[3]+a[4];return Math.abs(c-i)<2*i&&r.foundPatternCross(a)},r.prototype.crossCheckVertical=function(e,t,n,i){for(var a=this.image,o=a.getHeight(),s=this.getCrossCheckStateCount(),f=e;f>=0&&a.get(t,f);)s[2]++,f--;if(f<0)return NaN;for(;f>=0&&!a.get(t,f)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&a.get(t,f)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=e+1;f<o&&a.get(t,f);)s[2]++,f++;if(f===o)return NaN;for(;f<o&&!a.get(t,f)&&s[3]<n;)s[3]++,f++;if(f===o||s[3]>=n)return NaN;for(;f<o&&a.get(t,f)&&s[4]<n;)s[4]++,f++;if(s[4]>=n)return NaN;var u=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(u-i)>=2*i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,f):NaN},r.prototype.crossCheckHorizontal=function(e,t,n,i){for(var a=this.image,o=a.getWidth(),s=this.getCrossCheckStateCount(),f=e;f>=0&&a.get(f,t);)s[2]++,f--;if(f<0)return NaN;for(;f>=0&&!a.get(f,t)&&s[1]<=n;)s[1]++,f--;if(f<0||s[1]>n)return NaN;for(;f>=0&&a.get(f,t)&&s[0]<=n;)s[0]++,f--;if(s[0]>n)return NaN;for(f=e+1;f<o&&a.get(f,t);)s[2]++,f++;if(f===o)return NaN;for(;f<o&&!a.get(f,t)&&s[3]<n;)s[3]++,f++;if(f===o||s[3]>=n)return NaN;for(;f<o&&a.get(f,t)&&s[4]<n;)s[4]++,f++;if(s[4]>=n)return NaN;var u=s[0]+s[1]+s[2]+s[3]+s[4];return 5*Math.abs(u-i)>=i?NaN:r.foundPatternCross(s)?r.centerFromEnd(s,f):NaN},r.prototype.handlePossibleCenter=function(e,t,n,i){var a=e[0]+e[1]+e[2]+e[3]+e[4],o=r.centerFromEnd(e,n),s=this.crossCheckVertical(t,Math.floor(o),e[2],a);if(!isNaN(s)&&(o=this.crossCheckHorizontal(Math.floor(o),Math.floor(s),e[2],a),!isNaN(o)&&(!i||this.crossCheckDiagonal(Math.floor(s),Math.floor(o),e[2],a)))){for(var f=a/7,u=!1,c=this.possibleCenters,l=0,h=c.length;l<h;l++){var d=c[l];if(d.aboutEquals(f,s,o)){c[l]=d.combineEstimate(s,o,f),u=!0;break}}if(!u){var v=new R1(o,s,f);c.push(v),this.resultPointCallback!==null&&this.resultPointCallback!==void 0&&this.resultPointCallback.foundPossibleResultPoint(v)}return!0}return!1},r.prototype.findRowSkip=function(){var e,t,n=this.possibleCenters.length;if(n<=1)return 0;var i=null;try{for(var a=Rt(this.possibleCenters),o=a.next();!o.done;o=a.next()){var s=o.value;if(s.getCount()>=r.CENTER_QUORUM)if(i==null)i=s;else return this.hasSkipped=!0,Math.floor((Math.abs(i.getX()-s.getX())-Math.abs(i.getY()-s.getY()))/2)}}catch(f){e={error:f}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return 0},r.prototype.haveMultiplyConfirmedCenters=function(){var e,t,n,i,a=0,o=0,s=this.possibleCenters.length;try{for(var f=Rt(this.possibleCenters),u=f.next();!u.done;u=f.next()){var c=u.value;c.getCount()>=r.CENTER_QUORUM&&(a++,o+=c.getEstimatedModuleSize())}}catch(p){e={error:p}}finally{try{u&&!u.done&&(t=f.return)&&t.call(f)}finally{if(e)throw e.error}}if(a<3)return!1;var l=o/s,h=0;try{for(var d=Rt(this.possibleCenters),v=d.next();!v.done;v=d.next()){var c=v.value;h+=Math.abs(c.getEstimatedModuleSize()-l)}}catch(p){n={error:p}}finally{try{v&&!v.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return h<=.05*o},r.prototype.selectBestPatterns=function(){var e,t,n,i,a=this.possibleCenters.length;if(a<3)throw new I;var o=this.possibleCenters,s;if(a>3){var f=0,u=0;try{for(var c=Rt(this.possibleCenters),l=c.next();!l.done;l=c.next()){var h=l.value,d=h.getEstimatedModuleSize();f+=d,u+=d*d}}catch(O){e={error:O}}finally{try{l&&!l.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}s=f/a;var v=Math.sqrt(u/a-s*s);o.sort(function(O,b){var T=Math.abs(b.getEstimatedModuleSize()-s),D=Math.abs(O.getEstimatedModuleSize()-s);return T<D?-1:T>D?1:0});for(var p=Math.max(.2*s,v),x=0;x<o.length&&o.length>3;x++){var y=o[x];Math.abs(y.getEstimatedModuleSize()-s)>p&&(o.splice(x,1),x--)}}if(o.length>3){var f=0;try{for(var w=Rt(o),A=w.next();!A.done;A=w.next()){var C=A.value;f+=C.getEstimatedModuleSize()}}catch(b){n={error:b}}finally{try{A&&!A.done&&(i=w.return)&&i.call(w)}finally{if(n)throw n.error}}s=f/o.length,o.sort(function(b,T){if(T.getCount()===b.getCount()){var D=Math.abs(T.getEstimatedModuleSize()-s),B=Math.abs(b.getEstimatedModuleSize()-s);return D<B?1:D>B?-1:0}else return T.getCount()-b.getCount()}),o.splice(3)}return[o[0],o[1],o[2]]},r.CENTER_QUORUM=2,r.MIN_SKIP=3,r.MAX_MODULES=57,r})(),P1=(function(){function r(e){this.image=e}return r.prototype.getImage=function(){return this.image},r.prototype.getResultPointCallback=function(){return this.resultPointCallback},r.prototype.detect=function(e){this.resultPointCallback=e==null?null:e.get(le.NEED_RESULT_POINT_CALLBACK);var t=new N1(this.image,this.resultPointCallback),n=t.find(e);return this.processFinderPatternInfo(n)},r.prototype.processFinderPatternInfo=function(e){var t=e.getTopLeft(),n=e.getTopRight(),i=e.getBottomLeft(),a=this.calculateModuleSize(t,n,i);if(a<1)throw new I("No pattern found in proccess finder.");var o=r.computeDimension(t,n,i,a),s=wt.getProvisionalVersionForDimension(o),f=s.getDimensionForVersion()-7,u=null;if(s.getAlignmentPatternCenters().length>0)for(var c=n.getX()-t.getX()+i.getX(),l=n.getY()-t.getY()+i.getY(),h=1-3/f,d=Math.floor(t.getX()+h*(c-t.getX())),v=Math.floor(t.getY()+h*(l-t.getY())),p=4;p<=16;p<<=1)try{u=this.findAlignmentInRegion(a,d,v,p);break}catch(A){if(!(A instanceof I))throw A}var x=r.createTransform(t,n,i,u,o),y=r.sampleGrid(this.image,x,o),w;return u===null?w=[i,t,n]:w=[i,t,n,u],new Kr(y,w)},r.createTransform=function(e,t,n,i,a){var o=a-3.5,s,f,u,c;return i!==null?(s=i.getX(),f=i.getY(),u=o-3,c=u):(s=t.getX()-e.getX()+n.getX(),f=t.getY()-e.getY()+n.getY(),u=o,c=o),Ri.quadrilateralToQuadrilateral(3.5,3.5,o,3.5,u,c,3.5,o,e.getX(),e.getY(),t.getX(),t.getY(),s,f,n.getX(),n.getY())},r.sampleGrid=function(e,t,n){var i=qr.getInstance();return i.sampleGridWithTransform(e,n,n,t)},r.computeDimension=function(e,t,n,i){var a=q.round(N.distance(e,t)/i),o=q.round(N.distance(e,n)/i),s=Math.floor((a+o)/2)+7;switch(s&3){case 0:s++;break;case 2:s--;break;case 3:throw new I("Dimensions could be not found.")}return s},r.prototype.calculateModuleSize=function(e,t,n){return(this.calculateModuleSizeOneWay(e,t)+this.calculateModuleSizeOneWay(e,n))/2},r.prototype.calculateModuleSizeOneWay=function(e,t){var n=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(e.getX()),Math.floor(e.getY()),Math.floor(t.getX()),Math.floor(t.getY())),i=this.sizeOfBlackWhiteBlackRunBothWays(Math.floor(t.getX()),Math.floor(t.getY()),Math.floor(e.getX()),Math.floor(e.getY()));return isNaN(n)?i/7:isNaN(i)?n/7:(n+i)/14},r.prototype.sizeOfBlackWhiteBlackRunBothWays=function(e,t,n,i){var a=this.sizeOfBlackWhiteBlackRun(e,t,n,i),o=1,s=e-(n-e);s<0?(o=e/(e-s),s=0):s>=this.image.getWidth()&&(o=(this.image.getWidth()-1-e)/(s-e),s=this.image.getWidth()-1);var f=Math.floor(t-(i-t)*o);return o=1,f<0?(o=t/(t-f),f=0):f>=this.image.getHeight()&&(o=(this.image.getHeight()-1-t)/(f-t),f=this.image.getHeight()-1),s=Math.floor(e+(s-e)*o),a+=this.sizeOfBlackWhiteBlackRun(e,t,s,f),a-1},r.prototype.sizeOfBlackWhiteBlackRun=function(e,t,n,i){var a=Math.abs(i-t)>Math.abs(n-e);if(a){var o=e;e=t,t=o,o=n,n=i,i=o}for(var s=Math.abs(n-e),f=Math.abs(i-t),u=-s/2,c=e<n?1:-1,l=t<i?1:-1,h=0,d=n+c,v=e,p=t;v!==d;v+=c){var x=a?p:v,y=a?v:p;if(h===1===this.image.get(x,y)){if(h===2)return q.distance(v,p,e,t);h++}if(u+=f,u>0){if(p===i)break;p+=l,u-=s}}return h===2?q.distance(n+c,i,e,t):NaN},r.prototype.findAlignmentInRegion=function(e,t,n,i){var a=Math.floor(i*e),o=Math.max(0,t-a),s=Math.min(this.image.getWidth()-1,t+a);if(s-o<e*3)throw new I("Alignment top exceeds estimated module size.");var f=Math.max(0,n-a),u=Math.min(this.image.getHeight()-1,n+a);if(u-f<e*3)throw new I("Alignment bottom exceeds estimated module size.");var c=new b1(this.image,o,f,s-o,u-f,e,this.resultPointCallback);return c.find()},r})(),Ur=(function(){function r(){this.decoder=new m1}return r.prototype.getDecoder=function(){return this.decoder},r.prototype.decode=function(e,t){var n,i;if(t!=null&&t.get(le.PURE_BARCODE)!==void 0){var a=r.extractPureBits(e.getBlackMatrix());n=this.decoder.decodeBitMatrix(a,t),i=r.NO_POINTS}else{var o=new P1(e.getBlackMatrix()).detect(t);n=this.decoder.decodeBitMatrix(o.getBits(),t),i=o.getPoints()}n.getOther()instanceof Fi&&n.getOther().applyMirroredCorrection(i);var s=new Pe(n.getText(),n.getRawBytes(),void 0,i,F.QR_CODE,void 0),f=n.getByteSegments();f!==null&&s.putMetadata(Re.BYTE_SEGMENTS,f);var u=n.getECLevel();return u!==null&&s.putMetadata(Re.ERROR_CORRECTION_LEVEL,u),n.hasStructuredAppend()&&(s.putMetadata(Re.STRUCTURED_APPEND_SEQUENCE,n.getStructuredAppendSequenceNumber()),s.putMetadata(Re.STRUCTURED_APPEND_PARITY,n.getStructuredAppendParity())),s},r.prototype.reset=function(){},r.extractPureBits=function(e){var t=e.getTopLeftOnBit(),n=e.getBottomRightOnBit();if(t===null||n===null)throw new I;var i=this.moduleSize(t,e),a=t[1],o=n[1],s=t[0],f=n[0];if(s>=f||a>=o)throw new I;if(o-a!==f-s&&(f=s+(o-a),f>=e.getWidth()))throw new I;var u=Math.round((f-s+1)/i),c=Math.round((o-a+1)/i);if(u<=0||c<=0)throw new I;if(c!==u)throw new I;var l=Math.floor(i/2);a+=l,s+=l;var h=s+Math.floor((u-1)*i)-f;if(h>0){if(h>l)throw new I;s-=h}var d=a+Math.floor((c-1)*i)-o;if(d>0){if(d>l)throw new I;a-=d}for(var v=new it(u,c),p=0;p<c;p++)for(var x=a+Math.floor(p*i),y=0;y<u;y++)e.get(s+Math.floor(y*i),x)&&v.set(y,p);return v},r.moduleSize=function(e,t){for(var n=t.getHeight(),i=t.getWidth(),a=e[0],o=e[1],s=!0,f=0;a<i&&o<n;){if(s!==t.get(a,o)){if(++f===5)break;s=!s}a++,o++}if(a===i||o===n)throw new I;return(a-e[0])/7},r.NO_POINTS=new Array,r})(),M1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},K=(function(){function r(){}return r.prototype.PDF417Common=function(){},r.getBitCountSum=function(e){return q.sum(e)},r.toIntArray=function(e){var t,n;if(e==null||!e.length)return r.EMPTY_INT_ARRAY;var i=new Int32Array(e.length),a=0;try{for(var o=M1(e),s=o.next();!s.done;s=o.next()){var f=s.value;i[a++]=f}}catch(u){t={error:u}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return i},r.getCodeword=function(e){var t=_e.binarySearch(r.SYMBOL_TABLE,e&262143);return t<0?-1:(r.CODEWORD_TABLE[t]-1)%r.NUMBER_OF_CODEWORDS},r.NUMBER_OF_CODEWORDS=929,r.MAX_CODEWORDS_IN_BARCODE=r.NUMBER_OF_CODEWORDS-1,r.MIN_ROWS_IN_BARCODE=3,r.MAX_ROWS_IN_BARCODE=90,r.MODULES_IN_CODEWORD=17,r.MODULES_IN_STOP_PATTERN=18,r.BARS_IN_MODULE=8,r.EMPTY_INT_ARRAY=new Int32Array([]),r.SYMBOL_TABLE=Int32Array.from([66142,66170,66206,66236,66290,66292,66350,66382,66396,66454,66470,66476,66594,66600,66614,66626,66628,66632,66640,66654,66662,66668,66682,66690,66718,66720,66748,66758,66776,66798,66802,66804,66820,66824,66832,66846,66848,66876,66880,66936,66950,66956,66968,66992,67006,67022,67036,67042,67044,67048,67062,67118,67150,67164,67214,67228,67256,67294,67322,67350,67366,67372,67398,67404,67416,67438,67474,67476,67490,67492,67496,67510,67618,67624,67650,67656,67664,67678,67686,67692,67706,67714,67716,67728,67742,67744,67772,67782,67788,67800,67822,67826,67828,67842,67848,67870,67872,67900,67904,67960,67974,67992,68016,68030,68046,68060,68066,68068,68072,68086,68104,68112,68126,68128,68156,68160,68216,68336,68358,68364,68376,68400,68414,68448,68476,68494,68508,68536,68546,68548,68552,68560,68574,68582,68588,68654,68686,68700,68706,68708,68712,68726,68750,68764,68792,68802,68804,68808,68816,68830,68838,68844,68858,68878,68892,68920,68976,68990,68994,68996,69e3,69008,69022,69024,69052,69062,69068,69080,69102,69106,69108,69142,69158,69164,69190,69208,69230,69254,69260,69272,69296,69310,69326,69340,69386,69394,69396,69410,69416,69430,69442,69444,69448,69456,69470,69478,69484,69554,69556,69666,69672,69698,69704,69712,69726,69754,69762,69764,69776,69790,69792,69820,69830,69836,69848,69870,69874,69876,69890,69918,69920,69948,69952,70008,70022,70040,70064,70078,70094,70108,70114,70116,70120,70134,70152,70174,70176,70264,70384,70412,70448,70462,70496,70524,70542,70556,70584,70594,70600,70608,70622,70630,70636,70664,70672,70686,70688,70716,70720,70776,70896,71136,71180,71192,71216,71230,71264,71292,71360,71416,71452,71480,71536,71550,71554,71556,71560,71568,71582,71584,71612,71622,71628,71640,71662,71726,71732,71758,71772,71778,71780,71784,71798,71822,71836,71864,71874,71880,71888,71902,71910,71916,71930,71950,71964,71992,72048,72062,72066,72068,72080,72094,72096,72124,72134,72140,72152,72174,72178,72180,72206,72220,72248,72304,72318,72416,72444,72456,72464,72478,72480,72508,72512,72568,72588,72600,72624,72638,72654,72668,72674,72676,72680,72694,72726,72742,72748,72774,72780,72792,72814,72838,72856,72880,72894,72910,72924,72930,72932,72936,72950,72966,72972,72984,73008,73022,73056,73084,73102,73116,73144,73156,73160,73168,73182,73190,73196,73210,73226,73234,73236,73250,73252,73256,73270,73282,73284,73296,73310,73318,73324,73346,73348,73352,73360,73374,73376,73404,73414,73420,73432,73454,73498,73518,73522,73524,73550,73564,73570,73572,73576,73590,73800,73822,73858,73860,73872,73886,73888,73916,73944,73970,73972,73992,74014,74016,74044,74048,74104,74118,74136,74160,74174,74210,74212,74216,74230,74244,74256,74270,74272,74360,74480,74502,74508,74544,74558,74592,74620,74638,74652,74680,74690,74696,74704,74726,74732,74782,74784,74812,74992,75232,75288,75326,75360,75388,75456,75512,75576,75632,75646,75650,75652,75664,75678,75680,75708,75718,75724,75736,75758,75808,75836,75840,75896,76016,76256,76736,76824,76848,76862,76896,76924,76992,77048,77296,77340,77368,77424,77438,77536,77564,77572,77576,77584,77600,77628,77632,77688,77702,77708,77720,77744,77758,77774,77788,77870,77902,77916,77922,77928,77966,77980,78008,78018,78024,78032,78046,78060,78074,78094,78136,78192,78206,78210,78212,78224,78238,78240,78268,78278,78284,78296,78322,78324,78350,78364,78448,78462,78560,78588,78600,78622,78624,78652,78656,78712,78726,78744,78768,78782,78798,78812,78818,78820,78824,78838,78862,78876,78904,78960,78974,79072,79100,79296,79352,79368,79376,79390,79392,79420,79424,79480,79600,79628,79640,79664,79678,79712,79740,79772,79800,79810,79812,79816,79824,79838,79846,79852,79894,79910,79916,79942,79948,79960,79982,79988,80006,80024,80048,80062,80078,80092,80098,80100,80104,80134,80140,80176,80190,80224,80252,80270,80284,80312,80328,80336,80350,80358,80364,80378,80390,80396,80408,80432,80446,80480,80508,80576,80632,80654,80668,80696,80752,80766,80776,80784,80798,80800,80828,80844,80856,80878,80882,80884,80914,80916,80930,80932,80936,80950,80962,80968,80976,80990,80998,81004,81026,81028,81040,81054,81056,81084,81094,81100,81112,81134,81154,81156,81160,81168,81182,81184,81212,81216,81272,81286,81292,81304,81328,81342,81358,81372,81380,81384,81398,81434,81454,81458,81460,81486,81500,81506,81508,81512,81526,81550,81564,81592,81602,81604,81608,81616,81630,81638,81644,81702,81708,81722,81734,81740,81752,81774,81778,81780,82050,82078,82080,82108,82180,82184,82192,82206,82208,82236,82240,82296,82316,82328,82352,82366,82402,82404,82408,82440,82448,82462,82464,82492,82496,82552,82672,82694,82700,82712,82736,82750,82784,82812,82830,82882,82884,82888,82896,82918,82924,82952,82960,82974,82976,83004,83008,83064,83184,83424,83468,83480,83504,83518,83552,83580,83648,83704,83740,83768,83824,83838,83842,83844,83848,83856,83872,83900,83910,83916,83928,83950,83984,84e3,84028,84032,84088,84208,84448,84928,85040,85054,85088,85116,85184,85240,85488,85560,85616,85630,85728,85756,85764,85768,85776,85790,85792,85820,85824,85880,85894,85900,85912,85936,85966,85980,86048,86080,86136,86256,86496,86976,88160,88188,88256,88312,88560,89056,89200,89214,89312,89340,89536,89592,89608,89616,89632,89664,89720,89840,89868,89880,89904,89952,89980,89998,90012,90040,90190,90204,90254,90268,90296,90306,90308,90312,90334,90382,90396,90424,90480,90494,90500,90504,90512,90526,90528,90556,90566,90572,90584,90610,90612,90638,90652,90680,90736,90750,90848,90876,90884,90888,90896,90910,90912,90940,90944,91e3,91014,91020,91032,91056,91070,91086,91100,91106,91108,91112,91126,91150,91164,91192,91248,91262,91360,91388,91584,91640,91664,91678,91680,91708,91712,91768,91888,91928,91952,91966,92e3,92028,92046,92060,92088,92098,92100,92104,92112,92126,92134,92140,92188,92216,92272,92384,92412,92608,92664,93168,93200,93214,93216,93244,93248,93304,93424,93664,93720,93744,93758,93792,93820,93888,93944,93980,94008,94064,94078,94084,94088,94096,94110,94112,94140,94150,94156,94168,94246,94252,94278,94284,94296,94318,94342,94348,94360,94384,94398,94414,94428,94440,94470,94476,94488,94512,94526,94560,94588,94606,94620,94648,94658,94660,94664,94672,94686,94694,94700,94714,94726,94732,94744,94768,94782,94816,94844,94912,94968,94990,95004,95032,95088,95102,95112,95120,95134,95136,95164,95180,95192,95214,95218,95220,95244,95256,95280,95294,95328,95356,95424,95480,95728,95758,95772,95800,95856,95870,95968,95996,96008,96016,96030,96032,96060,96064,96120,96152,96176,96190,96220,96226,96228,96232,96290,96292,96296,96310,96322,96324,96328,96336,96350,96358,96364,96386,96388,96392,96400,96414,96416,96444,96454,96460,96472,96494,96498,96500,96514,96516,96520,96528,96542,96544,96572,96576,96632,96646,96652,96664,96688,96702,96718,96732,96738,96740,96744,96758,96772,96776,96784,96798,96800,96828,96832,96888,97008,97030,97036,97048,97072,97086,97120,97148,97166,97180,97208,97220,97224,97232,97246,97254,97260,97326,97330,97332,97358,97372,97378,97380,97384,97398,97422,97436,97464,97474,97476,97480,97488,97502,97510,97516,97550,97564,97592,97648,97666,97668,97672,97680,97694,97696,97724,97734,97740,97752,97774,97830,97836,97850,97862,97868,97880,97902,97906,97908,97926,97932,97944,97968,97998,98012,98018,98020,98024,98038,98618,98674,98676,98838,98854,98874,98892,98904,98926,98930,98932,98968,99006,99042,99044,99048,99062,99166,99194,99246,99286,99350,99366,99372,99386,99398,99416,99438,99442,99444,99462,99504,99518,99534,99548,99554,99556,99560,99574,99590,99596,99608,99632,99646,99680,99708,99726,99740,99768,99778,99780,99784,99792,99806,99814,99820,99834,99858,99860,99874,99880,99894,99906,99920,99934,99962,99970,99972,99976,99984,99998,1e5,100028,100038,100044,100056,100078,100082,100084,100142,100174,100188,100246,100262,100268,100306,100308,100390,100396,100410,100422,100428,100440,100462,100466,100468,100486,100504,100528,100542,100558,100572,100578,100580,100584,100598,100620,100656,100670,100704,100732,100750,100792,100802,100808,100816,100830,100838,100844,100858,100888,100912,100926,100960,100988,101056,101112,101148,101176,101232,101246,101250,101252,101256,101264,101278,101280,101308,101318,101324,101336,101358,101362,101364,101410,101412,101416,101430,101442,101448,101456,101470,101478,101498,101506,101508,101520,101534,101536,101564,101580,101618,101620,101636,101640,101648,101662,101664,101692,101696,101752,101766,101784,101838,101858,101860,101864,101934,101938,101940,101966,101980,101986,101988,101992,102030,102044,102072,102082,102084,102088,102096,102138,102166,102182,102188,102214,102220,102232,102254,102282,102290,102292,102306,102308,102312,102326,102444,102458,102470,102476,102488,102514,102516,102534,102552,102576,102590,102606,102620,102626,102632,102646,102662,102668,102704,102718,102752,102780,102798,102812,102840,102850,102856,102864,102878,102886,102892,102906,102936,102974,103008,103036,103104,103160,103224,103280,103294,103298,103300,103312,103326,103328,103356,103366,103372,103384,103406,103410,103412,103472,103486,103520,103548,103616,103672,103920,103992,104048,104062,104160,104188,104194,104196,104200,104208,104224,104252,104256,104312,104326,104332,104344,104368,104382,104398,104412,104418,104420,104424,104482,104484,104514,104520,104528,104542,104550,104570,104578,104580,104592,104606,104608,104636,104652,104690,104692,104706,104712,104734,104736,104764,104768,104824,104838,104856,104910,104930,104932,104936,104968,104976,104990,104992,105020,105024,105080,105200,105240,105278,105312,105372,105410,105412,105416,105424,105446,105518,105524,105550,105564,105570,105572,105576,105614,105628,105656,105666,105672,105680,105702,105722,105742,105756,105784,105840,105854,105858,105860,105864,105872,105888,105932,105970,105972,106006,106022,106028,106054,106060,106072,106100,106118,106124,106136,106160,106174,106190,106210,106212,106216,106250,106258,106260,106274,106276,106280,106306,106308,106312,106320,106334,106348,106394,106414,106418,106420,106566,106572,106610,106612,106630,106636,106648,106672,106686,106722,106724,106728,106742,106758,106764,106776,106800,106814,106848,106876,106894,106908,106936,106946,106948,106952,106960,106974,106982,106988,107032,107056,107070,107104,107132,107200,107256,107292,107320,107376,107390,107394,107396,107400,107408,107422,107424,107452,107462,107468,107480,107502,107506,107508,107544,107568,107582,107616,107644,107712,107768,108016,108060,108088,108144,108158,108256,108284,108290,108292,108296,108304,108318,108320,108348,108352,108408,108422,108428,108440,108464,108478,108494,108508,108514,108516,108520,108592,108640,108668,108736,108792,109040,109536,109680,109694,109792,109820,110016,110072,110084,110088,110096,110112,110140,110144,110200,110320,110342,110348,110360,110384,110398,110432,110460,110478,110492,110520,110532,110536,110544,110558,110658,110686,110714,110722,110724,110728,110736,110750,110752,110780,110796,110834,110836,110850,110852,110856,110864,110878,110880,110908,110912,110968,110982,111e3,111054,111074,111076,111080,111108,111112,111120,111134,111136,111164,111168,111224,111344,111372,111422,111456,111516,111554,111556,111560,111568,111590,111632,111646,111648,111676,111680,111736,111856,112096,112152,112224,112252,112320,112440,112514,112516,112520,112528,112542,112544,112588,112686,112718,112732,112782,112796,112824,112834,112836,112840,112848,112870,112890,112910,112924,112952,113008,113022,113026,113028,113032,113040,113054,113056,113100,113138,113140,113166,113180,113208,113264,113278,113376,113404,113416,113424,113440,113468,113472,113560,113614,113634,113636,113640,113686,113702,113708,113734,113740,113752,113778,113780,113798,113804,113816,113840,113854,113870,113890,113892,113896,113926,113932,113944,113968,113982,114016,114044,114076,114114,114116,114120,114128,114150,114170,114194,114196,114210,114212,114216,114242,114244,114248,114256,114270,114278,114306,114308,114312,114320,114334,114336,114364,114380,114420,114458,114478,114482,114484,114510,114524,114530,114532,114536,114842,114866,114868,114970,114994,114996,115042,115044,115048,115062,115130,115226,115250,115252,115278,115292,115298,115300,115304,115318,115342,115394,115396,115400,115408,115422,115430,115436,115450,115478,115494,115514,115526,115532,115570,115572,115738,115758,115762,115764,115790,115804,115810,115812,115816,115830,115854,115868,115896,115906,115912,115920,115934,115942,115948,115962,115996,116024,116080,116094,116098,116100,116104,116112,116126,116128,116156,116166,116172,116184,116206,116210,116212,116246,116262,116268,116282,116294,116300,116312,116334,116338,116340,116358,116364,116376,116400,116414,116430,116444,116450,116452,116456,116498,116500,116514,116520,116534,116546,116548,116552,116560,116574,116582,116588,116602,116654,116694,116714,116762,116782,116786,116788,116814,116828,116834,116836,116840,116854,116878,116892,116920,116930,116936,116944,116958,116966,116972,116986,117006,117048,117104,117118,117122,117124,117136,117150,117152,117180,117190,117196,117208,117230,117234,117236,117304,117360,117374,117472,117500,117506,117508,117512,117520,117536,117564,117568,117624,117638,117644,117656,117680,117694,117710,117724,117730,117732,117736,117750,117782,117798,117804,117818,117830,117848,117874,117876,117894,117936,117950,117966,117986,117988,117992,118022,118028,118040,118064,118078,118112,118140,118172,118210,118212,118216,118224,118238,118246,118266,118306,118312,118338,118352,118366,118374,118394,118402,118404,118408,118416,118430,118432,118460,118476,118514,118516,118574,118578,118580,118606,118620,118626,118628,118632,118678,118694,118700,118730,118738,118740,118830,118834,118836,118862,118876,118882,118884,118888,118902,118926,118940,118968,118978,118980,118984,118992,119006,119014,119020,119034,119068,119096,119152,119166,119170,119172,119176,119184,119198,119200,119228,119238,119244,119256,119278,119282,119284,119324,119352,119408,119422,119520,119548,119554,119556,119560,119568,119582,119584,119612,119616,119672,119686,119692,119704,119728,119742,119758,119772,119778,119780,119784,119798,119920,119934,120032,120060,120256,120312,120324,120328,120336,120352,120384,120440,120560,120582,120588,120600,120624,120638,120672,120700,120718,120732,120760,120770,120772,120776,120784,120798,120806,120812,120870,120876,120890,120902,120908,120920,120946,120948,120966,120972,120984,121008,121022,121038,121058,121060,121064,121078,121100,121112,121136,121150,121184,121212,121244,121282,121284,121288,121296,121318,121338,121356,121368,121392,121406,121440,121468,121536,121592,121656,121730,121732,121736,121744,121758,121760,121804,121842,121844,121890,121922,121924,121928,121936,121950,121958,121978,121986,121988,121992,122e3,122014,122016,122044,122060,122098,122100,122116,122120,122128,122142,122144,122172,122176,122232,122246,122264,122318,122338,122340,122344,122414,122418,122420,122446,122460,122466,122468,122472,122510,122524,122552,122562,122564,122568,122576,122598,122618,122646,122662,122668,122694,122700,122712,122738,122740,122762,122770,122772,122786,122788,122792,123018,123026,123028,123042,123044,123048,123062,123098,123146,123154,123156,123170,123172,123176,123190,123202,123204,123208,123216,123238,123244,123258,123290,123314,123316,123402,123410,123412,123426,123428,123432,123446,123458,123464,123472,123486,123494,123500,123514,123522,123524,123528,123536,123552,123580,123590,123596,123608,123630,123634,123636,123674,123698,123700,123740,123746,123748,123752,123834,123914,123922,123924,123938,123944,123958,123970,123976,123984,123998,124006,124012,124026,124034,124036,124048,124062,124064,124092,124102,124108,124120,124142,124146,124148,124162,124164,124168,124176,124190,124192,124220,124224,124280,124294,124300,124312,124336,124350,124366,124380,124386,124388,124392,124406,124442,124462,124466,124468,124494,124508,124514,124520,124558,124572,124600,124610,124612,124616,124624,124646,124666,124694,124710,124716,124730,124742,124748,124760,124786,124788,124818,124820,124834,124836,124840,124854,124946,124948,124962,124964,124968,124982,124994,124996,125e3,125008,125022,125030,125036,125050,125058,125060,125064,125072,125086,125088,125116,125126,125132,125144,125166,125170,125172,125186,125188,125192,125200,125216,125244,125248,125304,125318,125324,125336,125360,125374,125390,125404,125410,125412,125416,125430,125444,125448,125456,125472,125504,125560,125680,125702,125708,125720,125744,125758,125792,125820,125838,125852,125880,125890,125892,125896,125904,125918,125926,125932,125978,125998,126002,126004,126030,126044,126050,126052,126056,126094,126108,126136,126146,126148,126152,126160,126182,126202,126222,126236,126264,126320,126334,126338,126340,126344,126352,126366,126368,126412,126450,126452,126486,126502,126508,126522,126534,126540,126552,126574,126578,126580,126598,126604,126616,126640,126654,126670,126684,126690,126692,126696,126738,126754,126756,126760,126774,126786,126788,126792,126800,126814,126822,126828,126842,126894,126898,126900,126934,127126,127142,127148,127162,127178,127186,127188,127254,127270,127276,127290,127302,127308,127320,127342,127346,127348,127370,127378,127380,127394,127396,127400,127450,127510,127526,127532,127546,127558,127576,127598,127602,127604,127622,127628,127640,127664,127678,127694,127708,127714,127716,127720,127734,127754,127762,127764,127778,127784,127810,127812,127816,127824,127838,127846,127866,127898,127918,127922,127924,128022,128038,128044,128058,128070,128076,128088,128110,128114,128116,128134,128140,128152,128176,128190,128206,128220,128226,128228,128232,128246,128262,128268,128280,128304,128318,128352,128380,128398,128412,128440,128450,128452,128456,128464,128478,128486,128492,128506,128522,128530,128532,128546,128548,128552,128566,128578,128580,128584,128592,128606,128614,128634,128642,128644,128648,128656,128670,128672,128700,128716,128754,128756,128794,128814,128818,128820,128846,128860,128866,128868,128872,128886,128918,128934,128940,128954,128978,128980,129178,129198,129202,129204,129238,129258,129306,129326,129330,129332,129358,129372,129378,129380,129384,129398,129430,129446,129452,129466,129482,129490,129492,129562,129582,129586,129588,129614,129628,129634,129636,129640,129654,129678,129692,129720,129730,129732,129736,129744,129758,129766,129772,129814,129830,129836,129850,129862,129868,129880,129902,129906,129908,129930,129938,129940,129954,129956,129960,129974,130010]),r.CODEWORD_TABLE=Int32Array.from([2627,1819,2622,2621,1813,1812,2729,2724,2723,2779,2774,2773,902,896,908,868,865,861,859,2511,873,871,1780,835,2493,825,2491,842,837,844,1764,1762,811,810,809,2483,807,2482,806,2480,815,814,813,812,2484,817,816,1745,1744,1742,1746,2655,2637,2635,2626,2625,2623,2628,1820,2752,2739,2737,2728,2727,2725,2730,2785,2783,2778,2777,2775,2780,787,781,747,739,736,2413,754,752,1719,692,689,681,2371,678,2369,700,697,694,703,1688,1686,642,638,2343,631,2341,627,2338,651,646,643,2345,654,652,1652,1650,1647,1654,601,599,2322,596,2321,594,2319,2317,611,610,608,606,2324,603,2323,615,614,612,1617,1616,1614,1612,616,1619,1618,2575,2538,2536,905,901,898,909,2509,2507,2504,870,867,864,860,2512,875,872,1781,2490,2489,2487,2485,1748,836,834,832,830,2494,827,2492,843,841,839,845,1765,1763,2701,2676,2674,2653,2648,2656,2634,2633,2631,2629,1821,2638,2636,2770,2763,2761,2750,2745,2753,2736,2735,2733,2731,1848,2740,2738,2786,2784,591,588,576,569,566,2296,1590,537,534,526,2276,522,2274,545,542,539,548,1572,1570,481,2245,466,2242,462,2239,492,485,482,2249,496,494,1534,1531,1528,1538,413,2196,406,2191,2188,425,419,2202,415,2199,432,430,427,1472,1467,1464,433,1476,1474,368,367,2160,365,2159,362,2157,2155,2152,378,377,375,2166,372,2165,369,2162,383,381,379,2168,1419,1418,1416,1414,385,1411,384,1423,1422,1420,1424,2461,802,2441,2439,790,786,783,794,2409,2406,2403,750,742,738,2414,756,753,1720,2367,2365,2362,2359,1663,693,691,684,2373,680,2370,702,699,696,704,1690,1687,2337,2336,2334,2332,1624,2329,1622,640,637,2344,634,2342,630,2340,650,648,645,2346,655,653,1653,1651,1649,1655,2612,2597,2595,2571,2568,2565,2576,2534,2529,2526,1787,2540,2537,907,904,900,910,2503,2502,2500,2498,1768,2495,1767,2510,2508,2506,869,866,863,2513,876,874,1782,2720,2713,2711,2697,2694,2691,2702,2672,2670,2664,1828,2678,2675,2647,2646,2644,2642,1823,2639,1822,2654,2652,2650,2657,2771,1855,2765,2762,1850,1849,2751,2749,2747,2754,353,2148,344,342,336,2142,332,2140,345,1375,1373,306,2130,299,2128,295,2125,319,314,311,2132,1354,1352,1349,1356,262,257,2101,253,2096,2093,274,273,267,2107,263,2104,280,278,275,1316,1311,1308,1320,1318,2052,202,2050,2044,2040,219,2063,212,2060,208,2055,224,221,2066,1260,1258,1252,231,1248,229,1266,1264,1261,1268,155,1998,153,1996,1994,1991,1988,165,164,2007,162,2006,159,2003,2e3,172,171,169,2012,166,2010,1186,1184,1182,1179,175,1176,173,1192,1191,1189,1187,176,1194,1193,2313,2307,2305,592,589,2294,2292,2289,578,572,568,2297,580,1591,2272,2267,2264,1547,538,536,529,2278,525,2275,547,544,541,1574,1571,2237,2235,2229,1493,2225,1489,478,2247,470,2244,465,2241,493,488,484,2250,498,495,1536,1533,1530,1539,2187,2186,2184,2182,1432,2179,1430,2176,1427,414,412,2197,409,2195,405,2193,2190,426,424,421,2203,418,2201,431,429,1473,1471,1469,1466,434,1477,1475,2478,2472,2470,2459,2457,2454,2462,803,2437,2432,2429,1726,2443,2440,792,789,785,2401,2399,2393,1702,2389,1699,2411,2408,2405,745,741,2415,758,755,1721,2358,2357,2355,2353,1661,2350,1660,2347,1657,2368,2366,2364,2361,1666,690,687,2374,683,2372,701,698,705,1691,1689,2619,2617,2610,2608,2605,2613,2593,2588,2585,1803,2599,2596,2563,2561,2555,1797,2551,1795,2573,2570,2567,2577,2525,2524,2522,2520,1786,2517,1785,2514,1783,2535,2533,2531,2528,1788,2541,2539,906,903,911,2721,1844,2715,2712,1838,1836,2699,2696,2693,2703,1827,1826,1824,2673,2671,2669,2666,1829,2679,2677,1858,1857,2772,1854,1853,1851,1856,2766,2764,143,1987,139,1986,135,133,131,1984,128,1983,125,1981,138,137,136,1985,1133,1132,1130,112,110,1974,107,1973,104,1971,1969,122,121,119,117,1977,114,1976,124,1115,1114,1112,1110,1117,1116,84,83,1953,81,1952,78,1950,1948,1945,94,93,91,1959,88,1958,85,1955,99,97,95,1961,1086,1085,1083,1081,1078,100,1090,1089,1087,1091,49,47,1917,44,1915,1913,1910,1907,59,1926,56,1925,53,1922,1919,66,64,1931,61,1929,1042,1040,1038,71,1035,70,1032,68,1048,1047,1045,1043,1050,1049,12,10,1869,1867,1864,1861,21,1880,19,1877,1874,1871,28,1888,25,1886,22,1883,982,980,977,974,32,30,991,989,987,984,34,995,994,992,2151,2150,2147,2146,2144,356,355,354,2149,2139,2138,2136,2134,1359,343,341,338,2143,335,2141,348,347,346,1376,1374,2124,2123,2121,2119,1326,2116,1324,310,308,305,2131,302,2129,298,2127,320,318,316,313,2133,322,321,1355,1353,1351,1357,2092,2091,2089,2087,1276,2084,1274,2081,1271,259,2102,256,2100,252,2098,2095,272,269,2108,266,2106,281,279,277,1317,1315,1313,1310,282,1321,1319,2039,2037,2035,2032,1203,2029,1200,1197,207,2053,205,2051,201,2049,2046,2043,220,218,2064,215,2062,211,2059,228,226,223,2069,1259,1257,1254,232,1251,230,1267,1265,1263,2316,2315,2312,2311,2309,2314,2304,2303,2301,2299,1593,2308,2306,590,2288,2287,2285,2283,1578,2280,1577,2295,2293,2291,579,577,574,571,2298,582,581,1592,2263,2262,2260,2258,1545,2255,1544,2252,1541,2273,2271,2269,2266,1550,535,532,2279,528,2277,546,543,549,1575,1573,2224,2222,2220,1486,2217,1485,2214,1482,1479,2238,2236,2234,2231,1496,2228,1492,480,477,2248,473,2246,469,2243,490,487,2251,497,1537,1535,1532,2477,2476,2474,2479,2469,2468,2466,2464,1730,2473,2471,2453,2452,2450,2448,1729,2445,1728,2460,2458,2456,2463,805,804,2428,2427,2425,2423,1725,2420,1724,2417,1722,2438,2436,2434,2431,1727,2444,2442,793,791,788,795,2388,2386,2384,1697,2381,1696,2378,1694,1692,2402,2400,2398,2395,1703,2392,1701,2412,2410,2407,751,748,744,2416,759,757,1807,2620,2618,1806,1805,2611,2609,2607,2614,1802,1801,1799,2594,2592,2590,2587,1804,2600,2598,1794,1793,1791,1789,2564,2562,2560,2557,1798,2554,1796,2574,2572,2569,2578,1847,1846,2722,1843,1842,1840,1845,2716,2714,1835,1834,1832,1830,1839,1837,2700,2698,2695,2704,1817,1811,1810,897,862,1777,829,826,838,1760,1758,808,2481,1741,1740,1738,1743,2624,1818,2726,2776,782,740,737,1715,686,679,695,1682,1680,639,628,2339,647,644,1645,1643,1640,1648,602,600,597,595,2320,593,2318,609,607,604,1611,1610,1608,1606,613,1615,1613,2328,926,924,892,886,899,857,850,2505,1778,824,823,821,819,2488,818,2486,833,831,828,840,1761,1759,2649,2632,2630,2746,2734,2732,2782,2781,570,567,1587,531,527,523,540,1566,1564,476,467,463,2240,486,483,1524,1521,1518,1529,411,403,2192,399,2189,423,416,1462,1457,1454,428,1468,1465,2210,366,363,2158,360,2156,357,2153,376,373,370,2163,1410,1409,1407,1405,382,1402,380,1417,1415,1412,1421,2175,2174,777,774,771,784,732,725,722,2404,743,1716,676,674,668,2363,665,2360,685,1684,1681,626,624,622,2335,620,2333,617,2330,641,635,649,1646,1644,1642,2566,928,925,2530,2527,894,891,888,2501,2499,2496,858,856,854,851,1779,2692,2668,2665,2645,2643,2640,2651,2768,2759,2757,2744,2743,2741,2748,352,1382,340,337,333,1371,1369,307,300,296,2126,315,312,1347,1342,1350,261,258,250,2097,246,2094,271,268,264,1306,1301,1298,276,1312,1309,2115,203,2048,195,2045,191,2041,213,209,2056,1246,1244,1238,225,1234,222,1256,1253,1249,1262,2080,2079,154,1997,150,1995,147,1992,1989,163,160,2004,156,2001,1175,1174,1172,1170,1167,170,1164,167,1185,1183,1180,1177,174,1190,1188,2025,2024,2022,587,586,564,559,556,2290,573,1588,520,518,512,2268,508,2265,530,1568,1565,461,457,2233,450,2230,446,2226,479,471,489,1526,1523,1520,397,395,2185,392,2183,389,2180,2177,410,2194,402,422,1463,1461,1459,1456,1470,2455,799,2433,2430,779,776,773,2397,2394,2390,734,728,724,746,1717,2356,2354,2351,2348,1658,677,675,673,670,667,688,1685,1683,2606,2589,2586,2559,2556,2552,927,2523,2521,2518,2515,1784,2532,895,893,890,2718,2709,2707,2689,2687,2684,2663,2662,2660,2658,1825,2667,2769,1852,2760,2758,142,141,1139,1138,134,132,129,126,1982,1129,1128,1126,1131,113,111,108,105,1972,101,1970,120,118,115,1109,1108,1106,1104,123,1113,1111,82,79,1951,75,1949,72,1946,92,89,86,1956,1077,1076,1074,1072,98,1069,96,1084,1082,1079,1088,1968,1967,48,45,1916,42,1914,39,1911,1908,60,57,54,1923,50,1920,1031,1030,1028,1026,67,1023,65,1020,62,1041,1039,1036,1033,69,1046,1044,1944,1943,1941,11,9,1868,7,1865,1862,1859,20,1878,16,1875,13,1872,970,968,966,963,29,960,26,23,983,981,978,975,33,971,31,990,988,985,1906,1904,1902,993,351,2145,1383,331,330,328,326,2137,323,2135,339,1372,1370,294,293,291,289,2122,286,2120,283,2117,309,303,317,1348,1346,1344,245,244,242,2090,239,2088,236,2085,2082,260,2099,249,270,1307,1305,1303,1300,1314,189,2038,186,2036,183,2033,2030,2026,206,198,2047,194,216,1247,1245,1243,1240,227,1237,1255,2310,2302,2300,2286,2284,2281,565,563,561,558,575,1589,2261,2259,2256,2253,1542,521,519,517,514,2270,511,533,1569,1567,2223,2221,2218,2215,1483,2211,1480,459,456,453,2232,449,474,491,1527,1525,1522,2475,2467,2465,2451,2449,2446,801,800,2426,2424,2421,2418,1723,2435,780,778,775,2387,2385,2382,2379,1695,2375,1693,2396,735,733,730,727,749,1718,2616,2615,2604,2603,2601,2584,2583,2581,2579,1800,2591,2550,2549,2547,2545,1792,2542,1790,2558,929,2719,1841,2710,2708,1833,1831,2690,2688,2686,1815,1809,1808,1774,1756,1754,1737,1736,1734,1739,1816,1711,1676,1674,633,629,1638,1636,1633,1641,598,1605,1604,1602,1600,605,1609,1607,2327,887,853,1775,822,820,1757,1755,1584,524,1560,1558,468,464,1514,1511,1508,1519,408,404,400,1452,1447,1444,417,1458,1455,2208,364,361,358,2154,1401,1400,1398,1396,374,1393,371,1408,1406,1403,1413,2173,2172,772,726,723,1712,672,669,666,682,1678,1675,625,623,621,618,2331,636,632,1639,1637,1635,920,918,884,880,889,849,848,847,846,2497,855,852,1776,2641,2742,2787,1380,334,1367,1365,301,297,1340,1338,1335,1343,255,251,247,1296,1291,1288,265,1302,1299,2113,204,196,192,2042,1232,1230,1224,214,1220,210,1242,1239,1235,1250,2077,2075,151,148,1993,144,1990,1163,1162,1160,1158,1155,161,1152,157,1173,1171,1168,1165,168,1181,1178,2021,2020,2018,2023,585,560,557,1585,516,509,1562,1559,458,447,2227,472,1516,1513,1510,398,396,393,390,2181,386,2178,407,1453,1451,1449,1446,420,1460,2209,769,764,720,712,2391,729,1713,664,663,661,659,2352,656,2349,671,1679,1677,2553,922,919,2519,2516,885,883,881,2685,2661,2659,2767,2756,2755,140,1137,1136,130,127,1125,1124,1122,1127,109,106,102,1103,1102,1100,1098,116,1107,1105,1980,80,76,73,1947,1068,1067,1065,1063,90,1060,87,1075,1073,1070,1080,1966,1965,46,43,40,1912,36,1909,1019,1018,1016,1014,58,1011,55,1008,51,1029,1027,1024,1021,63,1037,1034,1940,1939,1937,1942,8,1866,4,1863,1,1860,956,954,952,949,946,17,14,969,967,964,961,27,957,24,979,976,972,1901,1900,1898,1896,986,1905,1903,350,349,1381,329,327,324,1368,1366,292,290,287,284,2118,304,1341,1339,1337,1345,243,240,237,2086,233,2083,254,1297,1295,1293,1290,1304,2114,190,187,184,2034,180,2031,177,2027,199,1233,1231,1229,1226,217,1223,1241,2078,2076,584,555,554,552,550,2282,562,1586,507,506,504,502,2257,499,2254,515,1563,1561,445,443,441,2219,438,2216,435,2212,460,454,475,1517,1515,1512,2447,798,797,2422,2419,770,768,766,2383,2380,2376,721,719,717,714,731,1714,2602,2582,2580,2548,2546,2543,923,921,2717,2706,2705,2683,2682,2680,1771,1752,1750,1733,1732,1731,1735,1814,1707,1670,1668,1631,1629,1626,1634,1599,1598,1596,1594,1603,1601,2326,1772,1753,1751,1581,1554,1552,1504,1501,1498,1509,1442,1437,1434,401,1448,1445,2206,1392,1391,1389,1387,1384,359,1399,1397,1394,1404,2171,2170,1708,1672,1669,619,1632,1630,1628,1773,1378,1363,1361,1333,1328,1336,1286,1281,1278,248,1292,1289,2111,1218,1216,1210,197,1206,193,1228,1225,1221,1236,2073,2071,1151,1150,1148,1146,152,1143,149,1140,145,1161,1159,1156,1153,158,1169,1166,2017,2016,2014,2019,1582,510,1556,1553,452,448,1506,1500,394,391,387,1443,1441,1439,1436,1450,2207,765,716,713,1709,662,660,657,1673,1671,916,914,879,878,877,882,1135,1134,1121,1120,1118,1123,1097,1096,1094,1092,103,1101,1099,1979,1059,1058,1056,1054,77,1051,74,1066,1064,1061,1071,1964,1963,1007,1006,1004,1002,999,41,996,37,1017,1015,1012,1009,52,1025,1022,1936,1935,1933,1938,942,940,938,935,932,5,2,955,953,950,947,18,943,15,965,962,958,1895,1894,1892,1890,973,1899,1897,1379,325,1364,1362,288,285,1334,1332,1330,241,238,234,1287,1285,1283,1280,1294,2112,188,185,181,178,2028,1219,1217,1215,1212,200,1209,1227,2074,2072,583,553,551,1583,505,503,500,513,1557,1555,444,442,439,436,2213,455,451,1507,1505,1502,796,763,762,760,767,711,710,708,706,2377,718,715,1710,2544,917,915,2681,1627,1597,1595,2325,1769,1749,1747,1499,1438,1435,2204,1390,1388,1385,1395,2169,2167,1704,1665,1662,1625,1623,1620,1770,1329,1282,1279,2109,1214,1207,1222,2068,2065,1149,1147,1144,1141,146,1157,1154,2013,2011,2008,2015,1579,1549,1546,1495,1487,1433,1431,1428,1425,388,1440,2205,1705,658,1667,1664,1119,1095,1093,1978,1057,1055,1052,1062,1962,1960,1005,1003,1e3,997,38,1013,1010,1932,1930,1927,1934,941,939,936,933,6,930,3,951,948,944,1889,1887,1884,1881,959,1893,1891,35,1377,1360,1358,1327,1325,1322,1331,1277,1275,1272,1269,235,1284,2110,1205,1204,1201,1198,182,1195,179,1213,2070,2067,1580,501,1551,1548,440,437,1497,1494,1490,1503,761,709,707,1706,913,912,2198,1386,2164,2161,1621,1766,2103,1208,2058,2054,1145,1142,2005,2002,1999,2009,1488,1429,1426,2200,1698,1659,1656,1975,1053,1957,1954,1001,998,1924,1921,1918,1928,937,934,931,1879,1876,1873,1870,945,1885,1882,1323,1273,1270,2105,1202,1199,1196,1211,2061,2057,1576,1543,1540,1484,1481,1478,1491,1700]),r})(),B1=(function(){function r(e,t){this.bits=e,this.points=t}return r.prototype.getBits=function(){return this.bits},r.prototype.getPoints=function(){return this.points},r})(),F1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},L1=(function(){function r(){}return r.detectMultiple=function(e,t,n){var i=e.getBlackMatrix(),a=r.detect(n,i);return a.length||(i=i.clone(),i.rotate180(),a=r.detect(n,i)),new B1(i,a)},r.detect=function(e,t){for(var n,i,a=new Array,o=0,s=0,f=!1;o<t.getHeight();){var u=r.findVertices(t,o,s);if(u[0]==null&&u[3]==null){if(!f)break;f=!1,s=0;try{for(var c=(n=void 0,F1(a)),l=c.next();!l.done;l=c.next()){var h=l.value;h[1]!=null&&(o=Math.trunc(Math.max(o,h[1].getY()))),h[3]!=null&&(o=Math.max(o,Math.trunc(h[3].getY())))}}catch(d){n={error:d}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(n)throw n.error}}o+=r.ROW_STEP;continue}if(f=!0,a.push(u),!e)break;u[2]!=null?(s=Math.trunc(u[2].getX()),o=Math.trunc(u[2].getY())):(s=Math.trunc(u[4].getX()),o=Math.trunc(u[4].getY()))}return a},r.findVertices=function(e,t,n){var i=e.getHeight(),a=e.getWidth(),o=new Array(8);return r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.START_PATTERN),r.INDEXES_START_PATTERN),o[4]!=null&&(n=Math.trunc(o[4].getX()),t=Math.trunc(o[4].getY())),r.copyToResult(o,r.findRowsWithPattern(e,i,a,t,n,r.STOP_PATTERN),r.INDEXES_STOP_PATTERN),o},r.copyToResult=function(e,t,n){for(var i=0;i<n.length;i++)e[n[i]]=t[i]},r.findRowsWithPattern=function(e,t,n,i,a,o){for(var s=new Array(4),f=!1,u=new Int32Array(o.length);i<t;i+=r.ROW_STEP){var c=r.findGuardPattern(e,a,i,n,!1,o,u);if(c!=null){for(;i>0;){var l=r.findGuardPattern(e,a,--i,n,!1,o,u);if(l!=null)c=l;else{i++;break}}s[0]=new N(c[0],i),s[1]=new N(c[1],i),f=!0;break}}var h=i+1;if(f){for(var d=0,l=Int32Array.from([Math.trunc(s[0].getX()),Math.trunc(s[1].getX())]);h<t;h++){var c=r.findGuardPattern(e,l[0],h,n,!1,o,u);if(c!=null&&Math.abs(l[0]-c[0])<r.MAX_PATTERN_DRIFT&&Math.abs(l[1]-c[1])<r.MAX_PATTERN_DRIFT)l=c,d=0;else{if(d>r.SKIPPED_ROW_COUNT_MAX)break;d++}}h-=d+1,s[2]=new N(l[0],h),s[3]=new N(l[1],h)}return h-i<r.BARCODE_MIN_HEIGHT&&_e.fill(s,null),s},r.findGuardPattern=function(e,t,n,i,a,o,s){_e.fillWithin(s,0,s.length,0);for(var f=t,u=0;e.get(f,n)&&f>0&&u++<r.MAX_PIXEL_DRIFT;)f--;for(var c=f,l=0,h=o.length,d=a;c<i;c++){var v=e.get(c,n);if(v!==d)s[l]++;else{if(l===h-1){if(r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE)return new Int32Array([f,c]);f+=s[0]+s[1],se.arraycopy(s,2,s,0,l-1),s[l-1]=0,s[l]=0,l--}else l++;s[l]=1,d=!d}}return l===h-1&&r.patternMatchVariance(s,o,r.MAX_INDIVIDUAL_VARIANCE)<r.MAX_AVG_VARIANCE?new Int32Array([f,c-1]):null},r.patternMatchVariance=function(e,t,n){for(var i=e.length,a=0,o=0,s=0;s<i;s++)a+=e[s],o+=t[s];if(a<o)return 1/0;var f=a/o;n*=f;for(var u=0,c=0;c<i;c++){var l=e[c],h=t[c]*f,d=l>h?l-h:h-l;if(d>n)return 1/0;u+=d}return u/a},r.INDEXES_START_PATTERN=Int32Array.from([0,4,1,5]),r.INDEXES_STOP_PATTERN=Int32Array.from([6,2,7,3]),r.MAX_AVG_VARIANCE=.42,r.MAX_INDIVIDUAL_VARIANCE=.8,r.START_PATTERN=Int32Array.from([8,1,1,1,1,1,1,3]),r.STOP_PATTERN=Int32Array.from([7,1,1,3,1,1,1,2,1]),r.MAX_PIXEL_DRIFT=3,r.MAX_PATTERN_DRIFT=5,r.SKIPPED_ROW_COUNT_MAX=25,r.ROW_STEP=5,r.BARCODE_MIN_HEIGHT=10,r})(),k1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},xt=(function(){function r(e,t){if(t.length===0)throw new L;this.field=e;var n=t.length;if(n>1&&t[0]===0){for(var i=1;i<n&&t[i]===0;)i++;i===n?this.coefficients=new Int32Array([0]):(this.coefficients=new Int32Array(n-i),se.arraycopy(t,i,this.coefficients,0,this.coefficients.length))}else this.coefficients=t}return r.prototype.getCoefficients=function(){return this.coefficients},r.prototype.getDegree=function(){return this.coefficients.length-1},r.prototype.isZero=function(){return this.coefficients[0]===0},r.prototype.getCoefficient=function(e){return this.coefficients[this.coefficients.length-1-e]},r.prototype.evaluateAt=function(e){var t,n;if(e===0)return this.getCoefficient(0);if(e===1){var i=0;try{for(var a=k1(this.coefficients),o=a.next();!o.done;o=a.next()){var s=o.value;i=this.field.add(i,s)}}catch(l){t={error:l}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i}for(var f=this.coefficients[0],u=this.coefficients.length,c=1;c<u;c++)f=this.field.add(this.field.multiply(e,f),this.coefficients[c]);return f},r.prototype.add=function(e){if(!this.field.equals(e.field))throw new L("ModulusPolys do not have same ModulusGF field");if(this.isZero())return e;if(e.isZero())return this;var t=this.coefficients,n=e.coefficients;if(t.length>n.length){var i=t;t=n,n=i}var a=new Int32Array(n.length),o=n.length-t.length;se.arraycopy(n,0,a,0,o);for(var s=o;s<n.length;s++)a[s]=this.field.add(t[s-o],n[s]);return new r(this.field,a)},r.prototype.subtract=function(e){if(!this.field.equals(e.field))throw new L("ModulusPolys do not have same ModulusGF field");return e.isZero()?this:this.add(e.negative())},r.prototype.multiply=function(e){return e instanceof r?this.multiplyOther(e):this.multiplyScalar(e)},r.prototype.multiplyOther=function(e){if(!this.field.equals(e.field))throw new L("ModulusPolys do not have same ModulusGF field");if(this.isZero()||e.isZero())return new r(this.field,new Int32Array([0]));for(var t=this.coefficients,n=t.length,i=e.coefficients,a=i.length,o=new Int32Array(n+a-1),s=0;s<n;s++)for(var f=t[s],u=0;u<a;u++)o[s+u]=this.field.add(o[s+u],this.field.multiply(f,i[u]));return new r(this.field,o)},r.prototype.negative=function(){for(var e=this.coefficients.length,t=new Int32Array(e),n=0;n<e;n++)t[n]=this.field.subtract(0,this.coefficients[n]);return new r(this.field,t)},r.prototype.multiplyScalar=function(e){if(e===0)return new r(this.field,new Int32Array([0]));if(e===1)return this;for(var t=this.coefficients.length,n=new Int32Array(t),i=0;i<t;i++)n[i]=this.field.multiply(this.coefficients[i],e);return new r(this.field,n)},r.prototype.multiplyByMonomial=function(e,t){if(e<0)throw new L;if(t===0)return new r(this.field,new Int32Array([0]));for(var n=this.coefficients.length,i=new Int32Array(n+e),a=0;a<n;a++)i[a]=this.field.multiply(this.coefficients[a],t);return new r(this.field,i)},r.prototype.toString=function(){for(var e=new X,t=this.getDegree();t>=0;t--){var n=this.getCoefficient(t);n!==0&&(n<0?(e.append(" - "),n=-n):e.length()>0&&e.append(" + "),(t===0||n!==1)&&e.append(n),t!==0&&(t===1?e.append("x"):(e.append("x^"),e.append(t))))}return e.toString()},r})(),U1=(function(){function r(){}return r.prototype.add=function(e,t){return(e+t)%this.modulus},r.prototype.subtract=function(e,t){return(this.modulus+e-t)%this.modulus},r.prototype.exp=function(e){return this.expTable[e]},r.prototype.log=function(e){if(e===0)throw new L;return this.logTable[e]},r.prototype.inverse=function(e){if(e===0)throw new Ti;return this.expTable[this.modulus-this.logTable[e]-1]},r.prototype.multiply=function(e,t){return e===0||t===0?0:this.expTable[(this.logTable[e]+this.logTable[t])%(this.modulus-1)]},r.prototype.getSize=function(){return this.modulus},r.prototype.equals=function(e){return e===this},r})(),H1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),V1=(function(r){H1(e,r);function e(t,n){var i=r.call(this)||this;i.modulus=t,i.expTable=new Int32Array(t),i.logTable=new Int32Array(t);for(var a=1,o=0;o<t;o++)i.expTable[o]=a,a=a*n%t;for(var o=0;o<t-1;o++)i.logTable[i.expTable[o]]=o;return i.zero=new xt(i,new Int32Array([0])),i.one=new xt(i,new Int32Array([1])),i}return e.prototype.getZero=function(){return this.zero},e.prototype.getOne=function(){return this.one},e.prototype.buildMonomial=function(t,n){if(t<0)throw new L;if(n===0)return this.zero;var i=new Int32Array(t+1);return i[0]=n,new xt(this,i)},e.PDF417_GF=new e(K.NUMBER_OF_CODEWORDS,3),e})(U1),G1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},W1=(function(){function r(){this.field=V1.PDF417_GF}return r.prototype.decode=function(e,t,n){for(var i,a,o=new xt(this.field,e),s=new Int32Array(t),f=!1,u=t;u>0;u--){var c=o.evaluateAt(this.field.exp(u));s[t-u]=c,c!==0&&(f=!0)}if(!f)return 0;var l=this.field.getOne();if(n!=null)try{for(var h=G1(n),d=h.next();!d.done;d=h.next()){var v=d.value,p=this.field.exp(e.length-1-v),x=new xt(this.field,new Int32Array([this.field.subtract(0,p),1]));l=l.multiply(x)}}catch(D){i={error:D}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}for(var y=new xt(this.field,s),w=this.runEuclideanAlgorithm(this.field.buildMonomial(t,1),y,t),A=w[0],C=w[1],O=this.findErrorLocations(A),b=this.findErrorMagnitudes(C,A,O),u=0;u<O.length;u++){var T=e.length-1-this.field.log(O[u]);if(T<0)throw me.getChecksumInstance();e[T]=this.field.subtract(e[T],b[u])}return O.length},r.prototype.runEuclideanAlgorithm=function(e,t,n){if(e.getDegree()<t.getDegree()){var i=e;e=t,t=i}for(var a=e,o=t,s=this.field.getZero(),f=this.field.getOne();o.getDegree()>=Math.round(n/2);){var u=a,c=s;if(a=o,s=f,a.isZero())throw me.getChecksumInstance();o=u;for(var l=this.field.getZero(),h=a.getCoefficient(a.getDegree()),d=this.field.inverse(h);o.getDegree()>=a.getDegree()&&!o.isZero();){var v=o.getDegree()-a.getDegree(),p=this.field.multiply(o.getCoefficient(o.getDegree()),d);l=l.add(this.field.buildMonomial(v,p)),o=o.subtract(a.multiplyByMonomial(v,p))}f=l.multiply(s).subtract(c).negative()}var x=f.getCoefficient(0);if(x===0)throw me.getChecksumInstance();var y=this.field.inverse(x),w=f.multiply(y),A=o.multiply(y);return[w,A]},r.prototype.findErrorLocations=function(e){for(var t=e.getDegree(),n=new Int32Array(t),i=0,a=1;a<this.field.getSize()&&i<t;a++)e.evaluateAt(a)===0&&(n[i]=this.field.inverse(a),i++);if(i!==t)throw me.getChecksumInstance();return n},r.prototype.findErrorMagnitudes=function(e,t,n){for(var i=t.getDegree(),a=new Int32Array(i),o=1;o<=i;o++)a[i-o]=this.field.multiply(o,t.getCoefficient(o));for(var s=new xt(this.field,a),f=n.length,u=new Int32Array(f),o=0;o<f;o++){var c=this.field.inverse(n[o]),l=this.field.subtract(0,e.evaluateAt(c)),h=this.field.inverse(s.evaluateAt(c));u[o]=this.field.multiply(l,h)}return u},r})(),Hr=(function(){function r(e,t,n,i,a){e instanceof r?this.constructor_2(e):this.constructor_1(e,t,n,i,a)}return r.prototype.constructor_1=function(e,t,n,i,a){var o=t==null||n==null,s=i==null||a==null;if(o&&s)throw new I;o?(t=new N(0,i.getY()),n=new N(0,a.getY())):s&&(i=new N(e.getWidth()-1,t.getY()),a=new N(e.getWidth()-1,n.getY())),this.image=e,this.topLeft=t,this.bottomLeft=n,this.topRight=i,this.bottomRight=a,this.minX=Math.trunc(Math.min(t.getX(),n.getX())),this.maxX=Math.trunc(Math.max(i.getX(),a.getX())),this.minY=Math.trunc(Math.min(t.getY(),i.getY())),this.maxY=Math.trunc(Math.max(n.getY(),a.getY()))},r.prototype.constructor_2=function(e){this.image=e.image,this.topLeft=e.getTopLeft(),this.bottomLeft=e.getBottomLeft(),this.topRight=e.getTopRight(),this.bottomRight=e.getBottomRight(),this.minX=e.getMinX(),this.maxX=e.getMaxX(),this.minY=e.getMinY(),this.maxY=e.getMaxY()},r.merge=function(e,t){return e==null?t:t==null?e:new r(e.image,e.topLeft,e.bottomLeft,t.topRight,t.bottomRight)},r.prototype.addMissingRows=function(e,t,n){var i=this.topLeft,a=this.bottomLeft,o=this.topRight,s=this.bottomRight;if(e>0){var f=n?this.topLeft:this.topRight,u=Math.trunc(f.getY()-e);u<0&&(u=0);var c=new N(f.getX(),u);n?i=c:o=c}if(t>0){var l=n?this.bottomLeft:this.bottomRight,h=Math.trunc(l.getY()+t);h>=this.image.getHeight()&&(h=this.image.getHeight()-1);var d=new N(l.getX(),h);n?a=d:s=d}return new r(this.image,i,a,o,s)},r.prototype.getMinX=function(){return this.minX},r.prototype.getMaxX=function(){return this.maxX},r.prototype.getMinY=function(){return this.minY},r.prototype.getMaxY=function(){return this.maxY},r.prototype.getTopLeft=function(){return this.topLeft},r.prototype.getTopRight=function(){return this.topRight},r.prototype.getBottomLeft=function(){return this.bottomLeft},r.prototype.getBottomRight=function(){return this.bottomRight},r})(),X1=(function(){function r(e,t,n,i){this.columnCount=e,this.errorCorrectionLevel=i,this.rowCountUpperPart=t,this.rowCountLowerPart=n,this.rowCount=t+n}return r.prototype.getColumnCount=function(){return this.columnCount},r.prototype.getErrorCorrectionLevel=function(){return this.errorCorrectionLevel},r.prototype.getRowCount=function(){return this.rowCount},r.prototype.getRowCountUpperPart=function(){return this.rowCountUpperPart},r.prototype.getRowCountLowerPart=function(){return this.rowCountLowerPart},r})(),Jr=(function(){function r(){this.buffer=""}return r.form=function(e,t){var n=-1;function i(o,s,f,u,c,l){if(o==="%%")return"%";if(t[++n]!==void 0){o=u?parseInt(u.substr(1)):void 0;var h=c?parseInt(c.substr(1)):void 0,d;switch(l){case"s":d=t[n];break;case"c":d=t[n][0];break;case"f":d=parseFloat(t[n]).toFixed(o);break;case"p":d=parseFloat(t[n]).toPrecision(o);break;case"e":d=parseFloat(t[n]).toExponential(o);break;case"x":d=parseInt(t[n]).toString(h||16);break;case"d":d=parseFloat(parseInt(t[n],h||10).toPrecision(o)).toFixed(0);break}d=typeof d=="object"?JSON.stringify(d):(+d).toString(h);for(var v=parseInt(f),p=f&&f[0]+""=="0"?"0":" ";d.length<v;)d=s!==void 0?d+p:p+d;return d}}var a=/%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;return e.replace(a,i)},r.prototype.format=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.buffer+=r.form(e,t)},r.prototype.toString=function(){return this.buffer},r})(),z1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Li=(function(){function r(e){this.boundingBox=new Hr(e),this.codewords=new Array(e.getMaxY()-e.getMinY()+1)}return r.prototype.getCodewordNearby=function(e){var t=this.getCodeword(e);if(t!=null)return t;for(var n=1;n<r.MAX_NEARBY_DISTANCE;n++){var i=this.imageRowToCodewordIndex(e)-n;if(i>=0&&(t=this.codewords[i],t!=null)||(i=this.imageRowToCodewordIndex(e)+n,i<this.codewords.length&&(t=this.codewords[i],t!=null)))return t}return null},r.prototype.imageRowToCodewordIndex=function(e){return e-this.boundingBox.getMinY()},r.prototype.setCodeword=function(e,t){this.codewords[this.imageRowToCodewordIndex(e)]=t},r.prototype.getCodeword=function(e){return this.codewords[this.imageRowToCodewordIndex(e)]},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.getCodewords=function(){return this.codewords},r.prototype.toString=function(){var e,t,n=new Jr,i=0;try{for(var a=z1(this.codewords),o=a.next();!o.done;o=a.next()){var s=o.value;if(s==null){n.format("%3d:    |   %n",i++);continue}n.format("%3d: %3d|%3d%n",i++,s.getRowNumber(),s.getValue())}}catch(f){e={error:f}}finally{try{o&&!o.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n.toString()},r.MAX_NEARBY_DISTANCE=5,r})(),j1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Y1=function(r,e){var t=typeof Symbol=="function"&&r[Symbol.iterator];if(!t)return r;var n=t.call(r),i,a=[],o;try{for(;(e===void 0||e-- >0)&&!(i=n.next()).done;)a.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(o)throw o.error}}return a},Bt=(function(){function r(){this.values=new Map}return r.prototype.setValue=function(e){e=Math.trunc(e);var t=this.values.get(e);t==null&&(t=0),t++,this.values.set(e,t)},r.prototype.getValue=function(){var e,t,n=-1,i=new Array,a=function(l,h){var d={getKey:function(){return l},getValue:function(){return h}};d.getValue()>n?(n=d.getValue(),i=[],i.push(d.getKey())):d.getValue()===n&&i.push(d.getKey())};try{for(var o=j1(this.values.entries()),s=o.next();!s.done;s=o.next()){var f=Y1(s.value,2),u=f[0],c=f[1];a(u,c)}}catch(l){e={error:l}}finally{try{s&&!s.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}return K.toIntArray(i)},r.prototype.getConfidence=function(e){return this.values.get(e)},r})(),$1=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Ar=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Wn=(function(r){$1(e,r);function e(t,n){var i=r.call(this,t)||this;return i._isLeft=n,i}return e.prototype.setRowNumbers=function(){var t,n;try{for(var i=Ar(this.getCodewords()),a=i.next();!a.done;a=i.next()){var o=a.value;o!=null&&o.setRowNumberAsRowIndicatorColumn()}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},e.prototype.adjustCompleteIndicatorColumnRowNumbers=function(t){var n=this.getCodewords();this.setRowNumbers(),this.removeIncorrectCodewords(n,t);for(var i=this.getBoundingBox(),a=this._isLeft?i.getTopLeft():i.getTopRight(),o=this._isLeft?i.getBottomLeft():i.getBottomRight(),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),f=this.imageRowToCodewordIndex(Math.trunc(o.getY())),u=-1,c=1,l=0,h=s;h<f;h++)if(n[h]!=null){var d=n[h],v=d.getRowNumber()-u;if(v===0)l++;else if(v===1)c=Math.max(c,l),l=1,u=d.getRowNumber();else if(v<0||d.getRowNumber()>=t.getRowCount()||v>h)n[h]=null;else{var p=void 0;c>2?p=(c-2)*v:p=v;for(var x=p>=h,y=1;y<=p&&!x;y++)x=n[h-y]!=null;x?n[h]=null:(u=d.getRowNumber(),l=1)}}},e.prototype.getRowHeights=function(){var t,n,i=this.getBarcodeMetadata();if(i==null)return null;this.adjustIncompleteIndicatorColumnRowNumbers(i);var a=new Int32Array(i.getRowCount());try{for(var o=Ar(this.getCodewords()),s=o.next();!s.done;s=o.next()){var f=s.value;if(f!=null){var u=f.getRowNumber();if(u>=a.length)continue;a[u]++}}}catch(c){t={error:c}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return a},e.prototype.adjustIncompleteIndicatorColumnRowNumbers=function(t){for(var n=this.getBoundingBox(),i=this._isLeft?n.getTopLeft():n.getTopRight(),a=this._isLeft?n.getBottomLeft():n.getBottomRight(),o=this.imageRowToCodewordIndex(Math.trunc(i.getY())),s=this.imageRowToCodewordIndex(Math.trunc(a.getY())),f=this.getCodewords(),u=-1,c=o;c<s;c++)if(f[c]!=null){var l=f[c];l.setRowNumberAsRowIndicatorColumn();var h=l.getRowNumber()-u;h===0||(h===1?u=l.getRowNumber():l.getRowNumber()>=t.getRowCount()?f[c]=null:u=l.getRowNumber())}},e.prototype.getBarcodeMetadata=function(){var t,n,i=this.getCodewords(),a=new Bt,o=new Bt,s=new Bt,f=new Bt;try{for(var u=Ar(i),c=u.next();!c.done;c=u.next()){var l=c.value;if(l!=null){l.setRowNumberAsRowIndicatorColumn();var h=l.getValue()%30,d=l.getRowNumber();switch(this._isLeft||(d+=2),d%3){case 0:o.setValue(h*3+1);break;case 1:f.setValue(h/3),s.setValue(h%3);break;case 2:a.setValue(h+1);break}}}}catch(p){t={error:p}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}if(a.getValue().length===0||o.getValue().length===0||s.getValue().length===0||f.getValue().length===0||a.getValue()[0]<1||o.getValue()[0]+s.getValue()[0]<K.MIN_ROWS_IN_BARCODE||o.getValue()[0]+s.getValue()[0]>K.MAX_ROWS_IN_BARCODE)return null;var v=new X1(a.getValue()[0],o.getValue()[0],s.getValue()[0],f.getValue()[0]);return this.removeIncorrectCodewords(i,v),v},e.prototype.removeIncorrectCodewords=function(t,n){for(var i=0;i<t.length;i++){var a=t[i];if(t[i]!=null){var o=a.getValue()%30,s=a.getRowNumber();if(s>n.getRowCount()){t[i]=null;continue}switch(this._isLeft||(s+=2),s%3){case 0:o*3+1!==n.getRowCountUpperPart()&&(t[i]=null);break;case 1:(Math.trunc(o/3)!==n.getErrorCorrectionLevel()||o%3!==n.getRowCountLowerPart())&&(t[i]=null);break;case 2:o+1!==n.getColumnCount()&&(t[i]=null);break}}}},e.prototype.isLeft=function(){return this._isLeft},e.prototype.toString=function(){return"IsLeft: "+this._isLeft+`
`+r.prototype.toString.call(this)},e})(Li),Z1=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},K1=(function(){function r(e,t){this.ADJUST_ROW_NUMBER_SKIP=2,this.barcodeMetadata=e,this.barcodeColumnCount=e.getColumnCount(),this.boundingBox=t,this.detectionResultColumns=new Array(this.barcodeColumnCount+2)}return r.prototype.getDetectionResultColumns=function(){this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]),this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount+1]);var e=K.MAX_CODEWORDS_IN_BARCODE,t;do t=e,e=this.adjustRowNumbersAndGetCount();while(e>0&&e<t);return this.detectionResultColumns},r.prototype.adjustIndicatorColumnRowNumbers=function(e){e!=null&&e.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata)},r.prototype.adjustRowNumbersAndGetCount=function(){var e=this.adjustRowNumbersByRow();if(e===0)return 0;for(var t=1;t<this.barcodeColumnCount+1;t++)for(var n=this.detectionResultColumns[t].getCodewords(),i=0;i<n.length;i++)n[i]!=null&&(n[i].hasValidRowNumber()||this.adjustRowNumbers(t,i,n));return e},r.prototype.adjustRowNumbersByRow=function(){this.adjustRowNumbersFromBothRI();var e=this.adjustRowNumbersFromLRI();return e+this.adjustRowNumbersFromRRI()},r.prototype.adjustRowNumbersFromBothRI=function(){if(!(this.detectionResultColumns[0]==null||this.detectionResultColumns[this.barcodeColumnCount+1]==null)){for(var e=this.detectionResultColumns[0].getCodewords(),t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<e.length;n++)if(e[n]!=null&&t[n]!=null&&e[n].getRowNumber()===t[n].getRowNumber())for(var i=1;i<=this.barcodeColumnCount;i++){var a=this.detectionResultColumns[i].getCodewords()[n];a!=null&&(a.setRowNumber(e[n].getRowNumber()),a.hasValidRowNumber()||(this.detectionResultColumns[i].getCodewords()[n]=null))}}},r.prototype.adjustRowNumbersFromRRI=function(){if(this.detectionResultColumns[this.barcodeColumnCount+1]==null)return 0;for(var e=0,t=this.detectionResultColumns[this.barcodeColumnCount+1].getCodewords(),n=0;n<t.length;n++)if(t[n]!=null)for(var i=t[n].getRowNumber(),a=0,o=this.barcodeColumnCount+1;o>0&&a<this.ADJUST_ROW_NUMBER_SKIP;o--){var s=this.detectionResultColumns[o].getCodewords()[n];s!=null&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.prototype.adjustRowNumbersFromLRI=function(){if(this.detectionResultColumns[0]==null)return 0;for(var e=0,t=this.detectionResultColumns[0].getCodewords(),n=0;n<t.length;n++)if(t[n]!=null)for(var i=t[n].getRowNumber(),a=0,o=1;o<this.barcodeColumnCount+1&&a<this.ADJUST_ROW_NUMBER_SKIP;o++){var s=this.detectionResultColumns[o].getCodewords()[n];s!=null&&(a=r.adjustRowNumberIfValid(i,a,s),s.hasValidRowNumber()||e++)}return e},r.adjustRowNumberIfValid=function(e,t,n){return n==null||n.hasValidRowNumber()||(n.isValidRowNumber(e)?(n.setRowNumber(e),t=0):++t),t},r.prototype.adjustRowNumbers=function(e,t,n){var i,a;if(this.detectionResultColumns[e-1]!=null){var o=n[t],s=this.detectionResultColumns[e-1].getCodewords(),f=s;this.detectionResultColumns[e+1]!=null&&(f=this.detectionResultColumns[e+1].getCodewords());var u=new Array(14);u[2]=s[t],u[3]=f[t],t>0&&(u[0]=n[t-1],u[4]=s[t-1],u[5]=f[t-1]),t>1&&(u[8]=n[t-2],u[10]=s[t-2],u[11]=f[t-2]),t<n.length-1&&(u[1]=n[t+1],u[6]=s[t+1],u[7]=f[t+1]),t<n.length-2&&(u[9]=n[t+2],u[12]=s[t+2],u[13]=f[t+2]);try{for(var c=Z1(u),l=c.next();!l.done;l=c.next()){var h=l.value;if(r.adjustRowNumber(o,h))return}}catch(d){i={error:d}}finally{try{l&&!l.done&&(a=c.return)&&a.call(c)}finally{if(i)throw i.error}}}},r.adjustRowNumber=function(e,t){return t==null?!1:t.hasValidRowNumber()&&t.getBucket()===e.getBucket()?(e.setRowNumber(t.getRowNumber()),!0):!1},r.prototype.getBarcodeColumnCount=function(){return this.barcodeColumnCount},r.prototype.getBarcodeRowCount=function(){return this.barcodeMetadata.getRowCount()},r.prototype.getBarcodeECLevel=function(){return this.barcodeMetadata.getErrorCorrectionLevel()},r.prototype.setBoundingBox=function(e){this.boundingBox=e},r.prototype.getBoundingBox=function(){return this.boundingBox},r.prototype.setDetectionResultColumn=function(e,t){this.detectionResultColumns[e]=t},r.prototype.getDetectionResultColumn=function(e){return this.detectionResultColumns[e]},r.prototype.toString=function(){var e=this.detectionResultColumns[0];e==null&&(e=this.detectionResultColumns[this.barcodeColumnCount+1]);for(var t=new Jr,n=0;n<e.getCodewords().length;n++){t.format("CW %3d:",n);for(var i=0;i<this.barcodeColumnCount+2;i++){if(this.detectionResultColumns[i]==null){t.format("    |   ");continue}var a=this.detectionResultColumns[i].getCodewords()[n];if(a==null){t.format("    |   ");continue}t.format(" %3d|%3d",a.getRowNumber(),a.getValue())}t.format("%n")}return t.toString()},r})(),q1=(function(){function r(e,t,n,i){this.rowNumber=r.BARCODE_ROW_UNKNOWN,this.startX=Math.trunc(e),this.endX=Math.trunc(t),this.bucket=Math.trunc(n),this.value=Math.trunc(i)}return r.prototype.hasValidRowNumber=function(){return this.isValidRowNumber(this.rowNumber)},r.prototype.isValidRowNumber=function(e){return e!==r.BARCODE_ROW_UNKNOWN&&this.bucket===e%3*3},r.prototype.setRowNumberAsRowIndicatorColumn=function(){this.rowNumber=Math.trunc(Math.trunc(this.value/30)*3+Math.trunc(this.bucket/3))},r.prototype.getWidth=function(){return this.endX-this.startX},r.prototype.getStartX=function(){return this.startX},r.prototype.getEndX=function(){return this.endX},r.prototype.getBucket=function(){return this.bucket},r.prototype.getValue=function(){return this.value},r.prototype.getRowNumber=function(){return this.rowNumber},r.prototype.setRowNumber=function(e){this.rowNumber=e},r.prototype.toString=function(){return this.rowNumber+"|"+this.value},r.BARCODE_ROW_UNKNOWN=-1,r})(),Q1=(function(){function r(){}return r.initialize=function(){for(var e=0;e<K.SYMBOL_TABLE.length;e++)for(var t=K.SYMBOL_TABLE[e],n=t&1,i=0;i<K.BARS_IN_MODULE;i++){for(var a=0;(t&1)===n;)a+=1,t>>=1;n=t&1,r.RATIOS_TABLE[e]||(r.RATIOS_TABLE[e]=new Array(K.BARS_IN_MODULE)),r.RATIOS_TABLE[e][K.BARS_IN_MODULE-i-1]=Math.fround(a/K.MODULES_IN_CODEWORD)}this.bSymbolTableReady=!0},r.getDecodedValue=function(e){var t=r.getDecodedCodewordValue(r.sampleBitCounts(e));return t!==-1?t:r.getClosestDecodedValue(e)},r.sampleBitCounts=function(e){for(var t=q.sum(e),n=new Int32Array(K.BARS_IN_MODULE),i=0,a=0,o=0;o<K.MODULES_IN_CODEWORD;o++){var s=t/(2*K.MODULES_IN_CODEWORD)+o*t/K.MODULES_IN_CODEWORD;a+e[i]<=s&&(a+=e[i],i++),n[i]++}return n},r.getDecodedCodewordValue=function(e){var t=r.getBitValue(e);return K.getCodeword(t)===-1?-1:t},r.getBitValue=function(e){for(var t=0,n=0;n<e.length;n++)for(var i=0;i<e[n];i++)t=t<<1|(n%2===0?1:0);return Math.trunc(t)},r.getClosestDecodedValue=function(e){var t=q.sum(e),n=new Array(K.BARS_IN_MODULE);if(t>1)for(var i=0;i<n.length;i++)n[i]=Math.fround(e[i]/t);var a=Mr.MAX_VALUE,o=-1;this.bSymbolTableReady||r.initialize();for(var s=0;s<r.RATIOS_TABLE.length;s++){for(var f=0,u=r.RATIOS_TABLE[s],c=0;c<K.BARS_IN_MODULE;c++){var l=Math.fround(u[c]-n[c]);if(f+=Math.fround(l*l),f>=a)break}f<a&&(a=f,o=K.SYMBOL_TABLE[s])}return o},r.bSymbolTableReady=!1,r.RATIOS_TABLE=new Array(K.SYMBOL_TABLE.length).map(function(e){return new Array(K.BARS_IN_MODULE)}),r})(),J1=(function(){function r(){this.segmentCount=-1,this.fileSize=-1,this.timestamp=-1,this.checksum=-1}return r.prototype.getSegmentIndex=function(){return this.segmentIndex},r.prototype.setSegmentIndex=function(e){this.segmentIndex=e},r.prototype.getFileId=function(){return this.fileId},r.prototype.setFileId=function(e){this.fileId=e},r.prototype.getOptionalData=function(){return this.optionalData},r.prototype.setOptionalData=function(e){this.optionalData=e},r.prototype.isLastSegment=function(){return this.lastSegment},r.prototype.setLastSegment=function(e){this.lastSegment=e},r.prototype.getSegmentCount=function(){return this.segmentCount},r.prototype.setSegmentCount=function(e){this.segmentCount=e},r.prototype.getSender=function(){return this.sender||null},r.prototype.setSender=function(e){this.sender=e},r.prototype.getAddressee=function(){return this.addressee||null},r.prototype.setAddressee=function(e){this.addressee=e},r.prototype.getFileName=function(){return this.fileName},r.prototype.setFileName=function(e){this.fileName=e},r.prototype.getFileSize=function(){return this.fileSize},r.prototype.setFileSize=function(e){this.fileSize=e},r.prototype.getChecksum=function(){return this.checksum},r.prototype.setChecksum=function(e){this.checksum=e},r.prototype.getTimestamp=function(){return this.timestamp},r.prototype.setTimestamp=function(e){this.timestamp=e},r})(),Xn=(function(){function r(){}return r.parseLong=function(e,t){return t===void 0&&(t=void 0),parseInt(e,t)},r})(),ef=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),tf=(function(r){ef(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="NullPointerException",e})(Xe),rf=(function(){function r(){}return r.prototype.writeBytes=function(e){this.writeBytesOffset(e,0,e.length)},r.prototype.writeBytesOffset=function(e,t,n){if(e==null)throw new tf;if(t<0||t>e.length||n<0||t+n>e.length||t+n<0)throw new $r;if(n===0)return;for(var i=0;i<n;i++)this.write(e[t+i])},r.prototype.flush=function(){},r.prototype.close=function(){},r})(),nf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),af=(function(r){nf(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e})(Xe),of=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),sf=(function(r){of(e,r);function e(t){t===void 0&&(t=32);var n=r.call(this)||this;if(n.count=0,t<0)throw new L("Negative initial size: "+t);return n.buf=new Uint8Array(t),n}return e.prototype.ensureCapacity=function(t){t-this.buf.length>0&&this.grow(t)},e.prototype.grow=function(t){var n=this.buf.length,i=n<<1;if(i-t<0&&(i=t),i<0){if(t<0)throw new af;i=z.MAX_VALUE}this.buf=_e.copyOfUint8Array(this.buf,i)},e.prototype.write=function(t){this.ensureCapacity(this.count+1),this.buf[this.count]=t,this.count+=1},e.prototype.writeBytesOffset=function(t,n,i){if(n<0||n>t.length||i<0||n+i-t.length>0)throw new $r;this.ensureCapacity(this.count+i),se.arraycopy(t,n,this.buf,this.count,i),this.count+=i},e.prototype.writeTo=function(t){t.writeBytesOffset(this.buf,0,this.count)},e.prototype.reset=function(){this.count=0},e.prototype.toByteArray=function(){return _e.copyOfUint8Array(this.buf,this.count)},e.prototype.size=function(){return this.count},e.prototype.toString=function(t){return t?typeof t=="string"?this.toString_string(t):this.toString_number(t):this.toString_void()},e.prototype.toString_void=function(){return new String(this.buf).toString()},e.prototype.toString_string=function(t){return new String(this.buf).toString()},e.prototype.toString_number=function(t){return new String(this.buf).toString()},e.prototype.close=function(){},e})(rf),ie;(function(r){r[r.ALPHA=0]="ALPHA",r[r.LOWER=1]="LOWER",r[r.MIXED=2]="MIXED",r[r.PUNCT=3]="PUNCT",r[r.ALPHA_SHIFT=4]="ALPHA_SHIFT",r[r.PUNCT_SHIFT=5]="PUNCT_SHIFT"})(ie||(ie={}));function ki(){if(typeof window<"u")return window.BigInt||null;if(typeof global<"u")return global.BigInt||null;if(typeof self<"u")return self.BigInt||null;throw new Error("Can't search globals for BigInt!")}var Yt;function ut(r){if(typeof Yt>"u"&&(Yt=ki()),Yt===null)throw new Error("BigInt is not supported!");return Yt(r)}function ff(){var r=[];r[0]=ut(1);var e=ut(900);r[1]=e;for(var t=2;t<16;t++)r[t]=r[t-1]*e;return r}var uf=(function(){function r(){}return r.decode=function(e,t){var n=new X(""),i=we.ISO8859_1;n.enableDecoding(i);for(var a=1,o=e[a++],s=new J1;a<e[0];){switch(o){case r.TEXT_COMPACTION_MODE_LATCH:a=r.textCompaction(e,a,n);break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:a=r.byteCompaction(o,e,i,a,n);break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:n.append(e[a++]);break;case r.NUMERIC_COMPACTION_MODE_LATCH:a=r.numericCompaction(e,a,n);break;case r.ECI_CHARSET:we.getCharacterSetECIByValue(e[a++]);break;case r.ECI_GENERAL_PURPOSE:a+=2;break;case r.ECI_USER_DEFINED:a++;break;case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:a=r.decodeMacroBlock(e,a,s);break;case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:throw new R;default:a--,a=r.textCompaction(e,a,n);break}if(a<e.length)o=e[a++];else throw R.getFormatInstance()}if(n.length()===0)throw R.getFormatInstance();var f=new lr(null,n.toString(),null,t);return f.setOther(s),f},r.decodeMacroBlock=function(e,t,n){if(t+r.NUMBER_OF_SEQUENCE_CODEWORDS>e[0])throw R.getFormatInstance();for(var i=new Int32Array(r.NUMBER_OF_SEQUENCE_CODEWORDS),a=0;a<r.NUMBER_OF_SEQUENCE_CODEWORDS;a++,t++)i[a]=e[t];n.setSegmentIndex(z.parseInt(r.decodeBase900toBase10(i,r.NUMBER_OF_SEQUENCE_CODEWORDS)));var o=new X;t=r.textCompaction(e,t,o),n.setFileId(o.toString());var s=-1;for(e[t]===r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD&&(s=t+1);t<e[0];)switch(e[t]){case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:switch(t++,e[t]){case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:var f=new X;t=r.textCompaction(e,t+1,f),n.setFileName(f.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SENDER:var u=new X;t=r.textCompaction(e,t+1,u),n.setSender(u.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:var c=new X;t=r.textCompaction(e,t+1,c),n.setAddressee(c.toString());break;case r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:var l=new X;t=r.numericCompaction(e,t+1,l),n.setSegmentCount(z.parseInt(l.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:var h=new X;t=r.numericCompaction(e,t+1,h),n.setTimestamp(Xn.parseLong(h.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:var d=new X;t=r.numericCompaction(e,t+1,d),n.setChecksum(z.parseInt(d.toString()));break;case r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:var v=new X;t=r.numericCompaction(e,t+1,v),n.setFileSize(Xn.parseLong(v.toString()));break;default:throw R.getFormatInstance()}break;case r.MACRO_PDF417_TERMINATOR:t++,n.setLastSegment(!0);break;default:throw R.getFormatInstance()}if(s!==-1){var p=t-s;n.isLastSegment()&&p--,n.setOptionalData(_e.copyOfRange(e,s,s+p))}return t},r.textCompaction=function(e,t,n){for(var i=new Int32Array((e[0]-t)*2),a=new Int32Array((e[0]-t)*2),o=0,s=!1;t<e[0]&&!s;){var f=e[t++];if(f<r.TEXT_COMPACTION_MODE_LATCH)i[o]=f/30,i[o+1]=f%30,o+=2;else switch(f){case r.TEXT_COMPACTION_MODE_LATCH:i[o++]=r.TEXT_COMPACTION_MODE_LATCH;break;case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,s=!0;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i[o]=r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE,f=e[t++],a[o]=f,o++;break}}return r.decodeTextCompaction(i,a,o,n),t},r.decodeTextCompaction=function(e,t,n,i){for(var a=ie.ALPHA,o=ie.ALPHA,s=0;s<n;){var f=e[s],u="";switch(a){case ie.ALPHA:if(f<26)u=String.fromCharCode(65+f);else switch(f){case 26:u=" ";break;case r.LL:a=ie.LOWER;break;case r.ML:a=ie.MIXED;break;case r.PS:o=a,a=ie.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break;case ie.LOWER:if(f<26)u=String.fromCharCode(97+f);else switch(f){case 26:u=" ";break;case r.AS:o=a,a=ie.ALPHA_SHIFT;break;case r.ML:a=ie.MIXED;break;case r.PS:o=a,a=ie.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break;case ie.MIXED:if(f<r.PL)u=r.MIXED_CHARS[f];else switch(f){case r.PL:a=ie.PUNCT;break;case 26:u=" ";break;case r.LL:a=ie.LOWER;break;case r.AL:a=ie.ALPHA;break;case r.PS:o=a,a=ie.PUNCT_SHIFT;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break;case ie.PUNCT:if(f<r.PAL)u=r.PUNCT_CHARS[f];else switch(f){case r.PAL:a=ie.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break;case ie.ALPHA_SHIFT:if(a=o,f<26)u=String.fromCharCode(65+f);else switch(f){case 26:u=" ";break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break;case ie.PUNCT_SHIFT:if(a=o,f<r.PAL)u=r.PUNCT_CHARS[f];else switch(f){case r.PAL:a=ie.ALPHA;break;case r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:i.append(t[s]);break;case r.TEXT_COMPACTION_MODE_LATCH:a=ie.ALPHA;break}break}u!==""&&i.append(u),s++}},r.byteCompaction=function(e,t,n,i,a){var o=new sf,s=0,f=0,u=!1;switch(e){case r.BYTE_COMPACTION_MODE_LATCH:for(var c=new Int32Array(6),l=t[i++];i<t[0]&&!u;)switch(c[s++]=l,f=900*f+l,l=t[i++],l){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,u=!0;break;default:if(s%5===0&&s>0){for(var h=0;h<6;++h)o.write(Number(ut(f)>>ut(8*(5-h))));f=0,s=0}break}i===t[0]&&l<r.TEXT_COMPACTION_MODE_LATCH&&(c[s++]=l);for(var d=0;d<s;d++)o.write(c[d]);break;case r.BYTE_COMPACTION_MODE_LATCH_6:for(;i<t[0]&&!u;){var v=t[i++];if(v<r.TEXT_COMPACTION_MODE_LATCH)s++,f=900*f+v;else switch(v){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.NUMERIC_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:i--,u=!0;break}if(s%5===0&&s>0){for(var h=0;h<6;++h)o.write(Number(ut(f)>>ut(8*(5-h))));f=0,s=0}}break}return a.append(Ye.decode(o.toByteArray(),n)),i},r.numericCompaction=function(e,t,n){for(var i=0,a=!1,o=new Int32Array(r.MAX_NUMERIC_CODEWORDS);t<e[0]&&!a;){var s=e[t++];if(t===e[0]&&(a=!0),s<r.TEXT_COMPACTION_MODE_LATCH)o[i]=s,i++;else switch(s){case r.TEXT_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH:case r.BYTE_COMPACTION_MODE_LATCH_6:case r.BEGIN_MACRO_PDF417_CONTROL_BLOCK:case r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:case r.MACRO_PDF417_TERMINATOR:t--,a=!0;break}(i%r.MAX_NUMERIC_CODEWORDS===0||s===r.NUMERIC_COMPACTION_MODE_LATCH||a)&&i>0&&(n.append(r.decodeBase900toBase10(o,i)),i=0)}return t},r.decodeBase900toBase10=function(e,t){for(var n=ut(0),i=0;i<t;i++)n+=r.EXP900[t-i-1]*ut(e[i]);var a=n.toString();if(a.charAt(0)!=="1")throw new R;return a.substring(1)},r.TEXT_COMPACTION_MODE_LATCH=900,r.BYTE_COMPACTION_MODE_LATCH=901,r.NUMERIC_COMPACTION_MODE_LATCH=902,r.BYTE_COMPACTION_MODE_LATCH_6=924,r.ECI_USER_DEFINED=925,r.ECI_GENERAL_PURPOSE=926,r.ECI_CHARSET=927,r.BEGIN_MACRO_PDF417_CONTROL_BLOCK=928,r.BEGIN_MACRO_PDF417_OPTIONAL_FIELD=923,r.MACRO_PDF417_TERMINATOR=922,r.MODE_SHIFT_TO_BYTE_COMPACTION_MODE=913,r.MAX_NUMERIC_CODEWORDS=15,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME=0,r.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT=1,r.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP=2,r.MACRO_PDF417_OPTIONAL_FIELD_SENDER=3,r.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE=4,r.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE=5,r.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM=6,r.PL=25,r.LL=27,r.AS=27,r.ML=28,r.AL=28,r.PS=29,r.PAL=29,r.PUNCT_CHARS=`;<>@[\\]_\`~!\r	,:
-.$/"|*()?{}'`,r.MIXED_CHARS="0123456789&\r	,:#-.$/+%*=^",r.EXP900=ki()?ff():[],r.NUMBER_OF_SEQUENCE_CODEWORDS=2,r})(),Dt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},cf=(function(){function r(){}return r.decode=function(e,t,n,i,a,o,s){for(var f=new Hr(e,t,n,i,a),u=null,c=null,l,h=!0;;h=!1){if(t!=null&&(u=r.getRowIndicatorColumn(e,f,t,!0,o,s)),i!=null&&(c=r.getRowIndicatorColumn(e,f,i,!1,o,s)),l=r.merge(u,c),l==null)throw I.getNotFoundInstance();var d=l.getBoundingBox();if(h&&d!=null&&(d.getMinY()<f.getMinY()||d.getMaxY()>f.getMaxY()))f=d;else break}l.setBoundingBox(f);var v=l.getBarcodeColumnCount()+1;l.setDetectionResultColumn(0,u),l.setDetectionResultColumn(v,c);for(var p=u!=null,x=1;x<=v;x++){var y=p?x:v-x;if(l.getDetectionResultColumn(y)===void 0){var w=void 0;y===0||y===v?w=new Wn(f,y===0):w=new Li(f),l.setDetectionResultColumn(y,w);for(var A=-1,C=A,O=f.getMinY();O<=f.getMaxY();O++){if(A=r.getStartColumn(l,y,O,p),A<0||A>f.getMaxX()){if(C===-1)continue;A=C}var b=r.detectCodeword(e,f.getMinX(),f.getMaxX(),p,A,O,o,s);b!=null&&(w.setCodeword(O,b),C=A,o=Math.min(o,b.getWidth()),s=Math.max(s,b.getWidth()))}}}return r.createDecoderResult(l)},r.merge=function(e,t){if(e==null&&t==null)return null;var n=r.getBarcodeMetadata(e,t);if(n==null)return null;var i=Hr.merge(r.adjustBoundingBox(e),r.adjustBoundingBox(t));return new K1(n,i)},r.adjustBoundingBox=function(e){var t,n;if(e==null)return null;var i=e.getRowHeights();if(i==null)return null;var a=r.getMax(i),o=0;try{for(var s=Dt(i),f=s.next();!f.done;f=s.next()){var u=f.value;if(o+=a-u,u>0)break}}catch(d){t={error:d}}finally{try{f&&!f.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}for(var c=e.getCodewords(),l=0;o>0&&c[l]==null;l++)o--;for(var h=0,l=i.length-1;l>=0&&(h+=a-i[l],!(i[l]>0));l--);for(var l=c.length-1;h>0&&c[l]==null;l--)h--;return e.getBoundingBox().addMissingRows(o,h,e.isLeft())},r.getMax=function(e){var t,n,i=-1;try{for(var a=Dt(e),o=a.next();!o.done;o=a.next()){var s=o.value;i=Math.max(i,s)}}catch(f){t={error:f}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(t)throw t.error}}return i},r.getBarcodeMetadata=function(e,t){var n;if(e==null||(n=e.getBarcodeMetadata())==null)return t==null?null:t.getBarcodeMetadata();var i;return t==null||(i=t.getBarcodeMetadata())==null?n:n.getColumnCount()!==i.getColumnCount()&&n.getErrorCorrectionLevel()!==i.getErrorCorrectionLevel()&&n.getRowCount()!==i.getRowCount()?null:n},r.getRowIndicatorColumn=function(e,t,n,i,a,o){for(var s=new Wn(t,i),f=0;f<2;f++)for(var u=f===0?1:-1,c=Math.trunc(Math.trunc(n.getX())),l=Math.trunc(Math.trunc(n.getY()));l<=t.getMaxY()&&l>=t.getMinY();l+=u){var h=r.detectCodeword(e,0,e.getWidth(),i,c,l,a,o);h!=null&&(s.setCodeword(l,h),i?c=h.getStartX():c=h.getEndX())}return s},r.adjustCodewordCount=function(e,t){var n=t[0][1],i=n.getValue(),a=e.getBarcodeColumnCount()*e.getBarcodeRowCount()-r.getNumberOfECCodeWords(e.getBarcodeECLevel());if(i.length===0){if(a<1||a>K.MAX_CODEWORDS_IN_BARCODE)throw I.getNotFoundInstance();n.setValue(a)}else i[0]!==a&&n.setValue(a)},r.createDecoderResult=function(e){var t=r.createBarcodeMatrix(e);r.adjustCodewordCount(e,t);for(var n=new Array,i=new Int32Array(e.getBarcodeRowCount()*e.getBarcodeColumnCount()),a=[],o=new Array,s=0;s<e.getBarcodeRowCount();s++)for(var f=0;f<e.getBarcodeColumnCount();f++){var u=t[s][f+1].getValue(),c=s*e.getBarcodeColumnCount()+f;u.length===0?n.push(c):u.length===1?i[c]=u[0]:(o.push(c),a.push(u))}for(var l=new Array(a.length),h=0;h<l.length;h++)l[h]=a[h];return r.createDecoderResultFromAmbiguousValues(e.getBarcodeECLevel(),i,K.toIntArray(n),K.toIntArray(o),l)},r.createDecoderResultFromAmbiguousValues=function(e,t,n,i,a){for(var o=new Int32Array(i.length),s=100;s-- >0;){for(var f=0;f<o.length;f++)t[i[f]]=a[f][o[f]];try{return r.decodeCodewords(t,e,n)}catch(c){var u=c instanceof me;if(!u)throw c}if(o.length===0)throw me.getChecksumInstance();for(var f=0;f<o.length;f++)if(o[f]<a[f].length-1){o[f]++;break}else if(o[f]=0,f===o.length-1)throw me.getChecksumInstance()}throw me.getChecksumInstance()},r.createBarcodeMatrix=function(e){for(var t,n,i,a,o=Array.from({length:e.getBarcodeRowCount()},function(){return new Array(e.getBarcodeColumnCount()+2)}),s=0;s<o.length;s++)for(var f=0;f<o[s].length;f++)o[s][f]=new Bt;var u=0;try{for(var c=Dt(e.getDetectionResultColumns()),l=c.next();!l.done;l=c.next()){var h=l.value;if(h!=null)try{for(var d=(i=void 0,Dt(h.getCodewords())),v=d.next();!v.done;v=d.next()){var p=v.value;if(p!=null){var x=p.getRowNumber();if(x>=0){if(x>=o.length)continue;o[x][u].setValue(p.getValue())}}}}catch(y){i={error:y}}finally{try{v&&!v.done&&(a=d.return)&&a.call(d)}finally{if(i)throw i.error}}u++}}catch(y){t={error:y}}finally{try{l&&!l.done&&(n=c.return)&&n.call(c)}finally{if(t)throw t.error}}return o},r.isValidBarcodeColumn=function(e,t){return t>=0&&t<=e.getBarcodeColumnCount()+1},r.getStartColumn=function(e,t,n,i){var a,o,s=i?1:-1,f=null;if(r.isValidBarcodeColumn(e,t-s)&&(f=e.getDetectionResultColumn(t-s).getCodeword(n)),f!=null)return i?f.getEndX():f.getStartX();if(f=e.getDetectionResultColumn(t).getCodewordNearby(n),f!=null)return i?f.getStartX():f.getEndX();if(r.isValidBarcodeColumn(e,t-s)&&(f=e.getDetectionResultColumn(t-s).getCodewordNearby(n)),f!=null)return i?f.getEndX():f.getStartX();for(var u=0;r.isValidBarcodeColumn(e,t-s);){t-=s;try{for(var c=(a=void 0,Dt(e.getDetectionResultColumn(t).getCodewords())),l=c.next();!l.done;l=c.next()){var h=l.value;if(h!=null)return(i?h.getEndX():h.getStartX())+s*u*(h.getEndX()-h.getStartX())}}catch(d){a={error:d}}finally{try{l&&!l.done&&(o=c.return)&&o.call(c)}finally{if(a)throw a.error}}u++}return i?e.getBoundingBox().getMinX():e.getBoundingBox().getMaxX()},r.detectCodeword=function(e,t,n,i,a,o,s,f){a=r.adjustCodewordStartColumn(e,t,n,i,a,o);var u=r.getModuleBitCount(e,t,n,i,a,o);if(u==null)return null;var c,l=q.sum(u);if(i)c=a+l;else{for(var h=0;h<u.length/2;h++){var d=u[h];u[h]=u[u.length-1-h],u[u.length-1-h]=d}c=a,a=c-l}if(!r.checkCodewordSkew(l,s,f))return null;var v=Q1.getDecodedValue(u),p=K.getCodeword(v);return p===-1?null:new q1(a,c,r.getCodewordBucketNumber(v),p)},r.getModuleBitCount=function(e,t,n,i,a,o){for(var s=a,f=new Int32Array(8),u=0,c=i?1:-1,l=i;(i?s<n:s>=t)&&u<f.length;)e.get(s,o)===l?(f[u]++,s+=c):(u++,l=!l);return u===f.length||s===(i?n:t)&&u===f.length-1?f:null},r.getNumberOfECCodeWords=function(e){return 2<<e},r.adjustCodewordStartColumn=function(e,t,n,i,a,o){for(var s=a,f=i?-1:1,u=0;u<2;u++){for(;(i?s>=t:s<n)&&i===e.get(s,o);){if(Math.abs(a-s)>r.CODEWORD_SKEW_SIZE)return a;s+=f}f=-f,i=!i}return s},r.checkCodewordSkew=function(e,t,n){return t-r.CODEWORD_SKEW_SIZE<=e&&e<=n+r.CODEWORD_SKEW_SIZE},r.decodeCodewords=function(e,t,n){if(e.length===0)throw R.getFormatInstance();var i=1<<t+1,a=r.correctErrors(e,n,i);r.verifyCodewordCount(e,i);var o=uf.decode(e,""+t);return o.setErrorsCorrected(a),o.setErasures(n.length),o},r.correctErrors=function(e,t,n){if(t!=null&&t.length>n/2+r.MAX_ERRORS||n<0||n>r.MAX_EC_CODEWORDS)throw me.getChecksumInstance();return r.errorCorrection.decode(e,n,t)},r.verifyCodewordCount=function(e,t){if(e.length<4)throw R.getFormatInstance();var n=e[0];if(n>e.length)throw R.getFormatInstance();if(n===0)if(t<e.length)e[0]=e.length-t;else throw R.getFormatInstance()},r.getBitCountForCodeword=function(e){for(var t=new Int32Array(8),n=0,i=t.length-1;!((e&1)!==n&&(n=e&1,i--,i<0));)t[i]++,e>>=1;return t},r.getCodewordBucketNumber=function(e){return e instanceof Int32Array?this.getCodewordBucketNumber_Int32Array(e):this.getCodewordBucketNumber_number(e)},r.getCodewordBucketNumber_number=function(e){return r.getCodewordBucketNumber(r.getBitCountForCodeword(e))},r.getCodewordBucketNumber_Int32Array=function(e){return(e[0]-e[2]+e[4]-e[6]+9)%9},r.toString=function(e){for(var t=new Jr,n=0;n<e.length;n++){t.format("Row %2d: ",n);for(var i=0;i<e[n].length;i++){var a=e[n][i];a.getValue().length===0?t.format("        ",null):t.format("%4d(%2d)",a.getValue()[0],a.getConfidence(a.getValue()[0]))}t.format("%n")}return t.toString()},r.CODEWORD_SKEW_SIZE=2,r.MAX_ERRORS=3,r.MAX_EC_CODEWORDS=512,r.errorCorrection=new W1,r})(),lf=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},Vr=(function(){function r(){}return r.prototype.decode=function(e,t){t===void 0&&(t=null);var n=r.decode(e,t,!1);if(n==null||n.length===0||n[0]==null)throw I.getNotFoundInstance();return n[0]},r.prototype.decodeMultiple=function(e,t){t===void 0&&(t=null);try{return r.decode(e,t,!0)}catch(n){throw n instanceof R||n instanceof me?I.getNotFoundInstance():n}},r.decode=function(e,t,n){var i,a,o=new Array,s=L1.detectMultiple(e,t,n);try{for(var f=lf(s.getPoints()),u=f.next();!u.done;u=f.next()){var c=u.value,l=cf.decode(s.getBits(),c[4],c[5],c[6],c[7],r.getMinCodewordWidth(c),r.getMaxCodewordWidth(c)),h=new Pe(l.getText(),l.getRawBytes(),void 0,c,F.PDF_417);h.putMetadata(Re.ERROR_CORRECTION_LEVEL,l.getECLevel());var d=l.getOther();d!=null&&h.putMetadata(Re.PDF417_EXTRA_METADATA,d),o.push(h)}}catch(v){i={error:v}}finally{try{u&&!u.done&&(a=f.return)&&a.call(f)}finally{if(i)throw i.error}}return o.map(function(v){return v})},r.getMaxWidth=function(e,t){return e==null||t==null?0:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMinWidth=function(e,t){return e==null||t==null?z.MAX_VALUE:Math.trunc(Math.abs(e.getX()-t.getX()))},r.getMaxCodewordWidth=function(e){return Math.floor(Math.max(Math.max(r.getMaxWidth(e[0],e[4]),r.getMaxWidth(e[6],e[2])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN),Math.max(r.getMaxWidth(e[1],e[5]),r.getMaxWidth(e[7],e[3])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN)))},r.getMinCodewordWidth=function(e){return Math.floor(Math.min(Math.min(r.getMinWidth(e[0],e[4]),r.getMinWidth(e[6],e[2])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN),Math.min(r.getMinWidth(e[1],e[5]),r.getMinWidth(e[7],e[3])*K.MODULES_IN_CODEWORD/K.MODULES_IN_STOP_PATTERN)))},r.prototype.reset=function(){},r})(),hf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),zn=(function(r){hf(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="ReaderException",e})(Xe),jn=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},df=(function(){function r(){}return r.prototype.decode=function(e,t){return this.setHints(t),this.decodeInternal(e)},r.prototype.decodeWithState=function(e){return(this.readers===null||this.readers===void 0)&&this.setHints(null),this.decodeInternal(e)},r.prototype.setHints=function(e){this.hints=e;var t=e!=null&&e.get(le.TRY_HARDER)!==void 0,n=e==null?null:e.get(le.POSSIBLE_FORMATS),i=new Array;if(n!=null){var a=n.some(function(o){return o===F.UPC_A||o===F.UPC_E||o===F.EAN_13||o===F.EAN_8||o===F.CODABAR||o===F.CODE_39||o===F.CODE_93||o===F.CODE_128||o===F.ITF||o===F.RSS_14||o===F.RSS_EXPANDED});a&&!t&&i.push(new Mt(e)),n.includes(F.QR_CODE)&&i.push(new Ur),n.includes(F.DATA_MATRIX)&&i.push(new kr),n.includes(F.AZTEC)&&i.push(new Fr),n.includes(F.PDF_417)&&i.push(new Vr),a&&t&&i.push(new Mt(e))}i.length===0&&(t||i.push(new Mt(e)),i.push(new Ur),i.push(new kr),i.push(new Fr),i.push(new Vr),t&&i.push(new Mt(e))),this.readers=i},r.prototype.reset=function(){var e,t;if(this.readers!==null)try{for(var n=jn(this.readers),i=n.next();!i.done;i=n.next()){var a=i.value;a.reset()}}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},r.prototype.decodeInternal=function(e){var t,n;if(this.readers===null)throw new zn("No readers where selected, nothing can be read.");try{for(var i=jn(this.readers),a=i.next();!a.done;a=i.next()){var o=a.value;try{return o.decode(e,this.hints)}catch(s){if(s instanceof zn)continue}}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}throw new I("No MultiFormat Readers were able to detect the code.")},r})(),vf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),pf=(function(r){vf(e,r);function e(t,n){t===void 0&&(t=null),n===void 0&&(n=500);var i=this,a=new df;return a.setHints(t),i=r.call(this,a,n)||this,i}return e.prototype.decodeBitmap=function(t){return this.reader.decodeWithState(t)},e})(bt),gf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){gf(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new Vr,t)||this}return e})(bt);var xf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){xf(e,r);function e(t){return t===void 0&&(t=500),r.call(this,new Ur,t)||this}return e})(bt);var Gr;(function(r){r[r.ERROR_CORRECTION=0]="ERROR_CORRECTION",r[r.CHARACTER_SET=1]="CHARACTER_SET",r[r.DATA_MATRIX_SHAPE=2]="DATA_MATRIX_SHAPE",r[r.DATA_MATRIX_COMPACT=3]="DATA_MATRIX_COMPACT",r[r.MIN_SIZE=4]="MIN_SIZE",r[r.MAX_SIZE=5]="MAX_SIZE",r[r.MARGIN=6]="MARGIN",r[r.PDF417_COMPACT=7]="PDF417_COMPACT",r[r.PDF417_COMPACTION=8]="PDF417_COMPACTION",r[r.PDF417_DIMENSIONS=9]="PDF417_DIMENSIONS",r[r.AZTEC_LAYERS=10]="AZTEC_LAYERS",r[r.QR_VERSION=11]="QR_VERSION",r[r.GS1_FORMAT=12]="GS1_FORMAT",r[r.FORCE_C40=13]="FORCE_C40"})(Gr||(Gr={}));const $t=Gr;var Ui=(function(){function r(e){this.field=e,this.cachedGenerators=[],this.cachedGenerators.push(new ct(e,Int32Array.from([1])))}return r.prototype.buildGenerator=function(e){var t=this.cachedGenerators;if(e>=t.length)for(var n=t[t.length-1],i=this.field,a=t.length;a<=e;a++){var o=n.multiply(new ct(i,Int32Array.from([1,i.exp(a-1+i.getGeneratorBase())])));t.push(o),n=o}return t[e]},r.prototype.encode=function(e,t){if(t===0)throw new L("No error correction bytes");var n=e.length-t;if(n<=0)throw new L("No data bytes provided");var i=this.buildGenerator(t),a=new Int32Array(n);se.arraycopy(e,0,a,0,n);var o=new ct(this.field,a);o=o.multiplyByMonomial(t,1);for(var s=o.divide(i)[1],f=s.getCoefficients(),u=t-f.length,c=0;c<u;c++)e[n+c]=0;se.arraycopy(f,0,e,n+u,f.length)},r})(),Ft=(function(){function r(){}return r.applyMaskPenaltyRule1=function(e){return r.applyMaskPenaltyRule1Internal(e,!0)+r.applyMaskPenaltyRule1Internal(e,!1)},r.applyMaskPenaltyRule2=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a-1;o++)for(var s=n[o],f=0;f<i-1;f++){var u=s[f];u===s[f+1]&&u===n[o+1][f]&&u===n[o+1][f+1]&&t++}return r.N2*t},r.applyMaskPenaltyRule3=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=0;s<i;s++){var f=n[o];s+6<i&&f[s]===1&&f[s+1]===0&&f[s+2]===1&&f[s+3]===1&&f[s+4]===1&&f[s+5]===0&&f[s+6]===1&&(r.isWhiteHorizontal(f,s-4,s)||r.isWhiteHorizontal(f,s+7,s+11))&&t++,o+6<a&&n[o][s]===1&&n[o+1][s]===0&&n[o+2][s]===1&&n[o+3][s]===1&&n[o+4][s]===1&&n[o+5][s]===0&&n[o+6][s]===1&&(r.isWhiteVertical(n,s,o-4,o)||r.isWhiteVertical(n,s,o+7,o+11))&&t++}return t*r.N3},r.isWhiteHorizontal=function(e,t,n){t=Math.max(t,0),n=Math.min(n,e.length);for(var i=t;i<n;i++)if(e[i]===1)return!1;return!0},r.isWhiteVertical=function(e,t,n,i){n=Math.max(n,0),i=Math.min(i,e.length);for(var a=n;a<i;a++)if(e[a][t]===1)return!1;return!0},r.applyMaskPenaltyRule4=function(e){for(var t=0,n=e.getArray(),i=e.getWidth(),a=e.getHeight(),o=0;o<a;o++)for(var s=n[o],f=0;f<i;f++)s[f]===1&&t++;var u=e.getHeight()*e.getWidth(),c=Math.floor(Math.abs(t*2-u)*10/u);return c*r.N4},r.getDataMaskBit=function(e,t,n){var i,a;switch(e){case 0:i=n+t&1;break;case 1:i=n&1;break;case 2:i=t%3;break;case 3:i=(n+t)%3;break;case 4:i=Math.floor(n/2)+Math.floor(t/3)&1;break;case 5:a=n*t,i=(a&1)+a%3;break;case 6:a=n*t,i=(a&1)+a%3&1;break;case 7:a=n*t,i=a%3+(n+t&1)&1;break;default:throw new L("Invalid mask pattern: "+e)}return i===0},r.applyMaskPenaltyRule1Internal=function(e,t){for(var n=0,i=t?e.getHeight():e.getWidth(),a=t?e.getWidth():e.getHeight(),o=e.getArray(),s=0;s<i;s++){for(var f=0,u=-1,c=0;c<a;c++){var l=t?o[s][c]:o[c][s];l===u?f++:(f>=5&&(n+=r.N1+(f-5)),f=1,u=l)}f>=5&&(n+=r.N1+(f-5))}return n},r.N1=3,r.N2=3,r.N3=40,r.N4=10,r})(),yf=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},wf=(function(){function r(e,t){this.width=e,this.height=t;for(var n=new Array(t),i=0;i!==t;i++)n[i]=new Uint8Array(e);this.bytes=n}return r.prototype.getHeight=function(){return this.height},r.prototype.getWidth=function(){return this.width},r.prototype.get=function(e,t){return this.bytes[t][e]},r.prototype.getArray=function(){return this.bytes},r.prototype.setNumber=function(e,t,n){this.bytes[t][e]=n},r.prototype.setBoolean=function(e,t,n){this.bytes[t][e]=n?1:0},r.prototype.clear=function(e){var t,n;try{for(var i=yf(this.bytes),a=i.next();!a.done;a=i.next()){var o=a.value;_e.fill(o,e)}}catch(s){t={error:s}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}},r.prototype.equals=function(e){if(!(e instanceof r))return!1;var t=e;if(this.width!==t.width||this.height!==t.height)return!1;for(var n=0,i=this.height;n<i;++n)for(var a=this.bytes[n],o=t.bytes[n],s=0,f=this.width;s<f;++s)if(a[s]!==o[s])return!1;return!0},r.prototype.toString=function(){for(var e=new X,t=0,n=this.height;t<n;++t){for(var i=this.bytes[t],a=0,o=this.width;a<o;++a)switch(i[a]){case 0:e.append(" 0");break;case 1:e.append(" 1");break;default:e.append("  ");break}e.append(`
`)}return e.toString()},r})(),Wr=(function(){function r(){this.maskPattern=-1}return r.prototype.getMode=function(){return this.mode},r.prototype.getECLevel=function(){return this.ecLevel},r.prototype.getVersion=function(){return this.version},r.prototype.getMaskPattern=function(){return this.maskPattern},r.prototype.getMatrix=function(){return this.matrix},r.prototype.toString=function(){var e=new X;return e.append(`<<
`),e.append(" mode: "),e.append(this.mode?this.mode.toString():"null"),e.append(`
 ecLevel: `),e.append(this.ecLevel?this.ecLevel.toString():"null"),e.append(`
 version: `),e.append(this.version?this.version.toString():"null"),e.append(`
 maskPattern: `),e.append(this.maskPattern.toString()),this.matrix?(e.append(`
 matrix:
`),e.append(this.matrix.toString())):e.append(`
 matrix: null
`),e.append(`>>
`),e.toString()},r.prototype.setMode=function(e){this.mode=e},r.prototype.setECLevel=function(e){this.ecLevel=e},r.prototype.setVersion=function(e){this.version=e},r.prototype.setMaskPattern=function(e){this.maskPattern=e},r.prototype.setMatrix=function(e){this.matrix=e},r.isValidMaskPattern=function(e){return e>=0&&e<r.NUM_MASK_PATTERNS},r.NUM_MASK_PATTERNS=8,r})(),_f=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),ue=(function(r){_f(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.kind="WriterException",e})(Xe),Yn=(function(){function r(){}return r.clearMatrix=function(e){e.clear(255)},r.buildMatrix=function(e,t,n,i,a){r.clearMatrix(a),r.embedBasicPatterns(n,a),r.embedTypeInfo(t,i,a),r.maybeEmbedVersionInfo(n,a),r.embedDataBits(e,i,a)},r.embedBasicPatterns=function(e,t){r.embedPositionDetectionPatternsAndSeparators(t),r.embedDarkDotAtLeftBottomCorner(t),r.maybeEmbedPositionAdjustmentPatterns(e,t),r.embedTimingPatterns(t)},r.embedTypeInfo=function(e,t,n){var i=new Se;r.makeTypeInfoBits(e,t,i);for(var a=0,o=i.getSize();a<o;++a){var s=i.get(i.getSize()-1-a),f=r.TYPE_INFO_COORDINATES[a],u=f[0],c=f[1];if(n.setBoolean(u,c,s),a<8){var l=n.getWidth()-a-1,h=8;n.setBoolean(l,h,s)}else{var l=8,h=n.getHeight()-7+(a-8);n.setBoolean(l,h,s)}}},r.maybeEmbedVersionInfo=function(e,t){if(!(e.getVersionNumber()<7)){var n=new Se;r.makeVersionInfoBits(e,n);for(var i=17,a=0;a<6;++a)for(var o=0;o<3;++o){var s=n.get(i);i--,t.setBoolean(a,t.getHeight()-11+o,s),t.setBoolean(t.getHeight()-11+o,a,s)}}},r.embedDataBits=function(e,t,n){for(var i=0,a=-1,o=n.getWidth()-1,s=n.getHeight()-1;o>0;){for(o===6&&(o-=1);s>=0&&s<n.getHeight();){for(var f=0;f<2;++f){var u=o-f;if(r.isEmpty(n.get(u,s))){var c=void 0;i<e.getSize()?(c=e.get(i),++i):c=!1,t!==255&&Ft.getDataMaskBit(t,u,s)&&(c=!c),n.setBoolean(u,s,c)}}s+=a}a=-a,s+=a,o-=2}if(i!==e.getSize())throw new ue("Not all bits consumed: "+i+"/"+e.getSize())},r.findMSBSet=function(e){return 32-z.numberOfLeadingZeros(e)},r.calculateBCHCode=function(e,t){if(t===0)throw new L("0 polynomial");var n=r.findMSBSet(t);for(e<<=n-1;r.findMSBSet(e)>=n;)e^=t<<r.findMSBSet(e)-n;return e},r.makeTypeInfoBits=function(e,t,n){if(!Wr.isValidMaskPattern(t))throw new ue("Invalid mask pattern");var i=e.getBits()<<3|t;n.appendBits(i,5);var a=r.calculateBCHCode(i,r.TYPE_INFO_POLY);n.appendBits(a,10);var o=new Se;if(o.appendBits(r.TYPE_INFO_MASK_PATTERN,15),n.xor(o),n.getSize()!==15)throw new ue("should not happen but we got: "+n.getSize())},r.makeVersionInfoBits=function(e,t){t.appendBits(e.getVersionNumber(),6);var n=r.calculateBCHCode(e.getVersionNumber(),r.VERSION_INFO_POLY);if(t.appendBits(n,12),t.getSize()!==18)throw new ue("should not happen but we got: "+t.getSize())},r.isEmpty=function(e){return e===255},r.embedTimingPatterns=function(e){for(var t=8;t<e.getWidth()-8;++t){var n=(t+1)%2;r.isEmpty(e.get(t,6))&&e.setNumber(t,6,n),r.isEmpty(e.get(6,t))&&e.setNumber(6,t,n)}},r.embedDarkDotAtLeftBottomCorner=function(e){if(e.get(8,e.getHeight()-8)===0)throw new ue;e.setNumber(8,e.getHeight()-8,1)},r.embedHorizontalSeparationPattern=function(e,t,n){for(var i=0;i<8;++i){if(!r.isEmpty(n.get(e+i,t)))throw new ue;n.setNumber(e+i,t,0)}},r.embedVerticalSeparationPattern=function(e,t,n){for(var i=0;i<7;++i){if(!r.isEmpty(n.get(e,t+i)))throw new ue;n.setNumber(e,t+i,0)}},r.embedPositionAdjustmentPattern=function(e,t,n){for(var i=0;i<5;++i)for(var a=r.POSITION_ADJUSTMENT_PATTERN[i],o=0;o<5;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPattern=function(e,t,n){for(var i=0;i<7;++i)for(var a=r.POSITION_DETECTION_PATTERN[i],o=0;o<7;++o)n.setNumber(e+o,t+i,a[o])},r.embedPositionDetectionPatternsAndSeparators=function(e){var t=r.POSITION_DETECTION_PATTERN[0].length;r.embedPositionDetectionPattern(0,0,e),r.embedPositionDetectionPattern(e.getWidth()-t,0,e),r.embedPositionDetectionPattern(0,e.getWidth()-t,e);var n=8;r.embedHorizontalSeparationPattern(0,n-1,e),r.embedHorizontalSeparationPattern(e.getWidth()-n,n-1,e),r.embedHorizontalSeparationPattern(0,e.getWidth()-n,e);var i=7;r.embedVerticalSeparationPattern(i,0,e),r.embedVerticalSeparationPattern(e.getHeight()-i-1,0,e),r.embedVerticalSeparationPattern(i,e.getHeight()-i,e)},r.maybeEmbedPositionAdjustmentPatterns=function(e,t){if(!(e.getVersionNumber()<2))for(var n=e.getVersionNumber()-1,i=r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[n],a=0,o=i.length;a!==o;a++){var s=i[a];if(s>=0)for(var f=0;f!==o;f++){var u=i[f];u>=0&&r.isEmpty(t.get(u,s))&&r.embedPositionAdjustmentPattern(u-2,s-2,t)}}},r.POSITION_DETECTION_PATTERN=Array.from([Int32Array.from([1,1,1,1,1,1,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,1,1,1,0,1]),Int32Array.from([1,0,0,0,0,0,1]),Int32Array.from([1,1,1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN=Array.from([Int32Array.from([1,1,1,1,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,0,1,0,1]),Int32Array.from([1,0,0,0,1]),Int32Array.from([1,1,1,1,1])]),r.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE=Array.from([Int32Array.from([-1,-1,-1,-1,-1,-1,-1]),Int32Array.from([6,18,-1,-1,-1,-1,-1]),Int32Array.from([6,22,-1,-1,-1,-1,-1]),Int32Array.from([6,26,-1,-1,-1,-1,-1]),Int32Array.from([6,30,-1,-1,-1,-1,-1]),Int32Array.from([6,34,-1,-1,-1,-1,-1]),Int32Array.from([6,22,38,-1,-1,-1,-1]),Int32Array.from([6,24,42,-1,-1,-1,-1]),Int32Array.from([6,26,46,-1,-1,-1,-1]),Int32Array.from([6,28,50,-1,-1,-1,-1]),Int32Array.from([6,30,54,-1,-1,-1,-1]),Int32Array.from([6,32,58,-1,-1,-1,-1]),Int32Array.from([6,34,62,-1,-1,-1,-1]),Int32Array.from([6,26,46,66,-1,-1,-1]),Int32Array.from([6,26,48,70,-1,-1,-1]),Int32Array.from([6,26,50,74,-1,-1,-1]),Int32Array.from([6,30,54,78,-1,-1,-1]),Int32Array.from([6,30,56,82,-1,-1,-1]),Int32Array.from([6,30,58,86,-1,-1,-1]),Int32Array.from([6,34,62,90,-1,-1,-1]),Int32Array.from([6,28,50,72,94,-1,-1]),Int32Array.from([6,26,50,74,98,-1,-1]),Int32Array.from([6,30,54,78,102,-1,-1]),Int32Array.from([6,28,54,80,106,-1,-1]),Int32Array.from([6,32,58,84,110,-1,-1]),Int32Array.from([6,30,58,86,114,-1,-1]),Int32Array.from([6,34,62,90,118,-1,-1]),Int32Array.from([6,26,50,74,98,122,-1]),Int32Array.from([6,30,54,78,102,126,-1]),Int32Array.from([6,26,52,78,104,130,-1]),Int32Array.from([6,30,56,82,108,134,-1]),Int32Array.from([6,34,60,86,112,138,-1]),Int32Array.from([6,30,58,86,114,142,-1]),Int32Array.from([6,34,62,90,118,146,-1]),Int32Array.from([6,30,54,78,102,126,150]),Int32Array.from([6,24,50,76,102,128,154]),Int32Array.from([6,28,54,80,106,132,158]),Int32Array.from([6,32,58,84,110,136,162]),Int32Array.from([6,26,54,82,110,138,166]),Int32Array.from([6,30,58,86,114,142,170])]),r.TYPE_INFO_COORDINATES=Array.from([Int32Array.from([8,0]),Int32Array.from([8,1]),Int32Array.from([8,2]),Int32Array.from([8,3]),Int32Array.from([8,4]),Int32Array.from([8,5]),Int32Array.from([8,7]),Int32Array.from([8,8]),Int32Array.from([7,8]),Int32Array.from([5,8]),Int32Array.from([4,8]),Int32Array.from([3,8]),Int32Array.from([2,8]),Int32Array.from([1,8]),Int32Array.from([0,8])]),r.VERSION_INFO_POLY=7973,r.TYPE_INFO_POLY=1335,r.TYPE_INFO_MASK_PATTERN=21522,r})(),Af=(function(){function r(e,t){this.dataBytes=e,this.errorCorrectionBytes=t}return r.prototype.getDataBytes=function(){return this.dataBytes},r.prototype.getErrorCorrectionBytes=function(){return this.errorCorrectionBytes},r})(),$n=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};(function(){function r(){}return r.calculateMaskPenalty=function(e){return Ft.applyMaskPenaltyRule1(e)+Ft.applyMaskPenaltyRule2(e)+Ft.applyMaskPenaltyRule3(e)+Ft.applyMaskPenaltyRule4(e)},r.encode=function(e,t,n){n===void 0&&(n=null);var i=r.DEFAULT_BYTE_MODE_ENCODING,a=n!==null&&n.get($t.CHARACTER_SET)!==void 0;a&&(i=n.get($t.CHARACTER_SET).toString());var o=this.chooseMode(e,i),s=new Se;if(o===ce.BYTE&&(a||r.DEFAULT_BYTE_MODE_ENCODING!==i)){var f=we.getCharacterSetECIByName(i);f!==void 0&&this.appendECI(f,s)}this.appendModeInfo(o,s);var u=new Se;this.appendBytes(e,o,u,i);var c;if(n!==null&&n.get($t.QR_VERSION)!==void 0){var l=Number.parseInt(n.get($t.QR_VERSION).toString(),10);c=wt.getVersionForNumber(l);var h=this.calculateBitsNeeded(o,s,u,c);if(!this.willFit(h,c,t))throw new ue("Data too big for requested version")}else c=this.recommendVersion(t,o,s,u);var d=new Se;d.appendBitArray(s);var v=o===ce.BYTE?u.getSizeInBytes():e.length;this.appendLengthInfo(v,c,o,d),d.appendBitArray(u);var p=c.getECBlocksForLevel(t),x=c.getTotalCodewords()-p.getTotalECCodewords();this.terminateBits(x,d);var y=this.interleaveWithECBytes(d,c.getTotalCodewords(),x,p.getNumBlocks()),w=new Wr;w.setECLevel(t),w.setMode(o),w.setVersion(c);var A=c.getDimensionForVersion(),C=new wf(A,A),O=this.chooseMaskPattern(y,t,c,C);return w.setMaskPattern(O),Yn.buildMatrix(y,t,c,O,C),w.setMatrix(C),w},r.recommendVersion=function(e,t,n,i){var a=this.calculateBitsNeeded(t,n,i,wt.getVersionForNumber(1)),o=this.chooseVersion(a,e),s=this.calculateBitsNeeded(t,n,i,o);return this.chooseVersion(s,e)},r.calculateBitsNeeded=function(e,t,n,i){return t.getSize()+e.getCharacterCountBits(i)+n.getSize()},r.getAlphanumericCode=function(e){return e<r.ALPHANUMERIC_TABLE.length?r.ALPHANUMERIC_TABLE[e]:-1},r.chooseMode=function(e,t){if(t===void 0&&(t=null),we.SJIS.getName()===t&&this.isOnlyDoubleByteKanji(e))return ce.KANJI;for(var n=!1,i=!1,a=0,o=e.length;a<o;++a){var s=e.charAt(a);if(r.isDigit(s))n=!0;else if(this.getAlphanumericCode(s.charCodeAt(0))!==-1)i=!0;else return ce.BYTE}return i?ce.ALPHANUMERIC:n?ce.NUMERIC:ce.BYTE},r.isOnlyDoubleByteKanji=function(e){var t;try{t=Ye.encode(e,we.SJIS)}catch{return!1}var n=t.length;if(n%2!==0)return!1;for(var i=0;i<n;i+=2){var a=t[i]&255;if((a<129||a>159)&&(a<224||a>235))return!1}return!0},r.chooseMaskPattern=function(e,t,n,i){for(var a=Number.MAX_SAFE_INTEGER,o=-1,s=0;s<Wr.NUM_MASK_PATTERNS;s++){Yn.buildMatrix(e,t,n,s,i);var f=this.calculateMaskPenalty(i);f<a&&(a=f,o=s)}return o},r.chooseVersion=function(e,t){for(var n=1;n<=40;n++){var i=wt.getVersionForNumber(n);if(r.willFit(e,i,t))return i}throw new ue("Data too big")},r.willFit=function(e,t,n){var i=t.getTotalCodewords(),a=t.getECBlocksForLevel(n),o=a.getTotalECCodewords(),s=i-o,f=(e+7)/8;return s>=f},r.terminateBits=function(e,t){var n=e*8;if(t.getSize()>n)throw new ue("data bits cannot fit in the QR Code"+t.getSize()+" > "+n);for(var i=0;i<4&&t.getSize()<n;++i)t.appendBit(!1);var a=t.getSize()&7;if(a>0)for(var i=a;i<8;i++)t.appendBit(!1);for(var o=e-t.getSizeInBytes(),i=0;i<o;++i)t.appendBits((i&1)===0?236:17,8);if(t.getSize()!==n)throw new ue("Bits size does not equal capacity")},r.getNumDataBytesAndNumECBytesForBlockID=function(e,t,n,i,a,o){if(i>=n)throw new ue("Block ID too large");var s=e%n,f=n-s,u=Math.floor(e/n),c=u+1,l=Math.floor(t/n),h=l+1,d=u-l,v=c-h;if(d!==v)throw new ue("EC bytes mismatch");if(n!==f+s)throw new ue("RS blocks mismatch");if(e!==(l+d)*f+(h+v)*s)throw new ue("Total bytes mismatch");i<f?(a[0]=l,o[0]=d):(a[0]=h,o[0]=v)},r.interleaveWithECBytes=function(e,t,n,i){var a,o,s,f;if(e.getSizeInBytes()!==n)throw new ue("Number of bits and data bytes does not match");for(var u=0,c=0,l=0,h=new Array,d=0;d<i;++d){var v=new Int32Array(1),p=new Int32Array(1);r.getNumDataBytesAndNumECBytesForBlockID(t,n,i,d,v,p);var x=v[0],y=new Uint8Array(x);e.toBytes(8*u,y,0,x);var w=r.generateECBytes(y,p[0]);h.push(new Af(y,w)),c=Math.max(c,x),l=Math.max(l,w.length),u+=v[0]}if(n!==u)throw new ue("Data bytes does not match offset");for(var A=new Se,d=0;d<c;++d)try{for(var C=(a=void 0,$n(h)),O=C.next();!O.done;O=C.next()){var b=O.value,y=b.getDataBytes();d<y.length&&A.appendBits(y[d],8)}}catch(M){a={error:M}}finally{try{O&&!O.done&&(o=C.return)&&o.call(C)}finally{if(a)throw a.error}}for(var d=0;d<l;++d)try{for(var T=(s=void 0,$n(h)),D=T.next();!D.done;D=T.next()){var b=D.value,w=b.getErrorCorrectionBytes();d<w.length&&A.appendBits(w[d],8)}}catch(M){s={error:M}}finally{try{D&&!D.done&&(f=T.return)&&f.call(T)}finally{if(s)throw s.error}}if(t!==A.getSizeInBytes())throw new ue("Interleaving error: "+t+" and "+A.getSizeInBytes()+" differ.");return A},r.generateECBytes=function(e,t){for(var n=e.length,i=new Int32Array(n+t),a=0;a<n;a++)i[a]=e[a]&255;new Ui(Ve.QR_CODE_FIELD_256).encode(i,t);for(var o=new Uint8Array(t),a=0;a<t;a++)o[a]=i[n+a];return o},r.appendModeInfo=function(e,t){t.appendBits(e.getBits(),4)},r.appendLengthInfo=function(e,t,n,i){var a=n.getCharacterCountBits(t);if(e>=1<<a)throw new ue(e+" is bigger than "+((1<<a)-1));i.appendBits(e,a)},r.appendBytes=function(e,t,n,i){switch(t){case ce.NUMERIC:r.appendNumericBytes(e,n);break;case ce.ALPHANUMERIC:r.appendAlphanumericBytes(e,n);break;case ce.BYTE:r.append8BitBytes(e,n,i);break;case ce.KANJI:r.appendKanjiBytes(e,n);break;default:throw new ue("Invalid mode: "+t)}},r.getDigit=function(e){return e.charCodeAt(0)-48},r.isDigit=function(e){var t=r.getDigit(e);return t>=0&&t<=9},r.appendNumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getDigit(e.charAt(i));if(i+2<n){var o=r.getDigit(e.charAt(i+1)),s=r.getDigit(e.charAt(i+2));t.appendBits(a*100+o*10+s,10),i+=3}else if(i+1<n){var o=r.getDigit(e.charAt(i+1));t.appendBits(a*10+o,7),i+=2}else t.appendBits(a,4),i++}},r.appendAlphanumericBytes=function(e,t){for(var n=e.length,i=0;i<n;){var a=r.getAlphanumericCode(e.charCodeAt(i));if(a===-1)throw new ue;if(i+1<n){var o=r.getAlphanumericCode(e.charCodeAt(i+1));if(o===-1)throw new ue;t.appendBits(a*45+o,11),i+=2}else t.appendBits(a,6),i++}},r.append8BitBytes=function(e,t,n){var i;try{i=Ye.encode(e,n)}catch(f){throw new ue(f)}for(var a=0,o=i.length;a!==o;a++){var s=i[a];t.appendBits(s,8)}},r.appendKanjiBytes=function(e,t){var n;try{n=Ye.encode(e,we.SJIS)}catch(l){throw new ue(l)}for(var i=n.length,a=0;a<i;a+=2){var o=n[a]&255,s=n[a+1]&255,f=o<<8&4294967295|s,u=-1;if(f>=33088&&f<=40956?u=f-33088:f>=57408&&f<=60351&&(u=f-49472),u===-1)throw new ue("Invalid byte sequence");var c=(u>>8)*192+(u&255);t.appendBits(c,13)}},r.appendECI=function(e,t){t.appendBits(ce.ECI.getBits(),4),t.appendBits(e.getValue(),8)},r.ALPHANUMERIC_TABLE=Int32Array.from([-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,36,-1,-1,-1,37,38,-1,-1,-1,-1,39,40,-1,41,42,43,0,1,2,3,4,5,6,7,8,9,44,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,-1]),r.DEFAULT_BYTE_MODE_ENCODING=we.UTF8.getName(),r})();var Ef=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){Ef(e,r);function e(t,n,i,a,o,s,f,u){var c=r.call(this,s,f)||this;if(c.yuvData=t,c.dataWidth=n,c.dataHeight=i,c.left=a,c.top=o,a+s>n||o+f>i)throw new L("Crop rectangle does not fit within image data.");return u&&c.reverseHorizontal(s,f),c}return e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new L("Requested row is outside the image: "+t);var i=this.getWidth();(n==null||n.length<i)&&(n=new Uint8ClampedArray(i));var a=(t+this.top)*this.dataWidth+this.left;return se.arraycopy(this.yuvData,a,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.yuvData;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return se.arraycopy(this.yuvData,o,a,0,i),a;for(var s=0;s<n;s++){var f=s*t;se.arraycopy(this.yuvData,o,a,f,t),o+=this.dataWidth}return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.yuvData,this.dataWidth,this.dataHeight,this.left+t,this.top+n,i,a,!1)},e.prototype.renderThumbnail=function(){for(var t=this.getWidth()/e.THUMBNAIL_SCALE_FACTOR,n=this.getHeight()/e.THUMBNAIL_SCALE_FACTOR,i=new Int32Array(t*n),a=this.yuvData,o=this.top*this.dataWidth+this.left,s=0;s<n;s++){for(var f=s*t,u=0;u<t;u++){var c=a[o+u*e.THUMBNAIL_SCALE_FACTOR]&255;i[f+u]=4278190080|c*65793}o+=this.dataWidth*e.THUMBNAIL_SCALE_FACTOR}return i},e.prototype.getThumbnailWidth=function(){return this.getWidth()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.getThumbnailHeight=function(){return this.getHeight()/e.THUMBNAIL_SCALE_FACTOR},e.prototype.reverseHorizontal=function(t,n){for(var i=this.yuvData,a=0,o=this.top*this.dataWidth+this.left;a<n;a++,o+=this.dataWidth)for(var s=o+t/2,f=o,u=o+t-1;f<s;f++,u--){var c=i[f];i[f]=i[u],i[u]=c}},e.prototype.invert=function(){return new Zr(this)},e.THUMBNAIL_SCALE_FACTOR=2,e})(cr);var Cf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})();(function(r){Cf(e,r);function e(t,n,i,a,o,s,f){var u=r.call(this,n,i)||this;if(u.dataWidth=a,u.dataHeight=o,u.left=s,u.top=f,t.BYTES_PER_ELEMENT===4){for(var c=n*i,l=new Uint8ClampedArray(c),h=0;h<c;h++){var d=t[h],v=d>>16&255,p=d>>7&510,x=d&255;l[h]=(v+p+x)/4&255}u.luminances=l}else u.luminances=t;if(a===void 0&&(u.dataWidth=n),o===void 0&&(u.dataHeight=i),s===void 0&&(u.left=0),f===void 0&&(u.top=0),u.left+n>u.dataWidth||u.top+i>u.dataHeight)throw new L("Crop rectangle does not fit within image data.");return u}return e.prototype.getRow=function(t,n){if(t<0||t>=this.getHeight())throw new L("Requested row is outside the image: "+t);var i=this.getWidth();(n==null||n.length<i)&&(n=new Uint8ClampedArray(i));var a=(t+this.top)*this.dataWidth+this.left;return se.arraycopy(this.luminances,a,n,0,i),n},e.prototype.getMatrix=function(){var t=this.getWidth(),n=this.getHeight();if(t===this.dataWidth&&n===this.dataHeight)return this.luminances;var i=t*n,a=new Uint8ClampedArray(i),o=this.top*this.dataWidth+this.left;if(t===this.dataWidth)return se.arraycopy(this.luminances,o,a,0,i),a;for(var s=0;s<n;s++){var f=s*t;se.arraycopy(this.luminances,o,a,f,t),o+=this.dataWidth}return a},e.prototype.isCropSupported=function(){return!0},e.prototype.crop=function(t,n,i,a){return new e(this.luminances,i,a,this.dataWidth,this.dataHeight,this.left+t,this.top+n)},e.prototype.invert=function(){return new Zr(this)},e})(cr);var mf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Sf=(function(r){mf(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.forName=function(t){return this.getCharacterSetECIByName(t)},e})(we),If=(function(){function r(){}return r.ISO_8859_1=we.ISO8859_1,r})(),Xr,Of=301,bf=function(r,e){for(var t=1,n=0;n<255;n++)e[n]=t,r[t]=n,t*=2,t>=256&&(t^=Of);return{LOG:r,ALOG:e}};Xr=bf([],[]),Xr.LOG;Xr.ALOG;var Zn;(function(r){r[r.FORCE_NONE=0]="FORCE_NONE",r[r.FORCE_SQUARE=1]="FORCE_SQUARE",r[r.FORCE_RECTANGLE=2]="FORCE_RECTANGLE"})(Zn||(Zn={}));var Kn=129,Hi=230,Tf=231,Rf=235,Df=236,Nf=237,Pf=238,Mf=239,Bf=240,Er=254,Ff=254,qn="[)>05",Qn="[)>06",Jn="",ne=0,Te=1,Le=2,xe=3,Ce=4,De=5,Lf=(function(){function r(){}return r.prototype.getEncodingMode=function(){return ne},r.prototype.encode=function(e){var t=Je.determineConsecutiveDigitCount(e.getMessage(),e.pos);if(t>=2)e.writeCodeword(this.encodeASCIIDigits(e.getMessage().charCodeAt(e.pos),e.getMessage().charCodeAt(e.pos+1))),e.pos+=2;else{var n=e.getCurrentChar(),i=Je.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(i!==this.getEncodingMode())switch(i){case De:e.writeCodeword(Tf),e.signalEncoderChange(De);return;case Te:e.writeCodeword(Hi),e.signalEncoderChange(Te);return;case xe:e.writeCodeword(Pf),e.signalEncoderChange(xe);break;case Le:e.writeCodeword(Mf),e.signalEncoderChange(Le);break;case Ce:e.writeCodeword(Bf),e.signalEncoderChange(Ce);break;default:throw new Error("Illegal mode: "+i)}else Je.isExtendedASCII(n)?(e.writeCodeword(Rf),e.writeCodeword(n-128+1),e.pos++):(e.writeCodeword(n+1),e.pos++)}},r.prototype.encodeASCIIDigits=function(e,t){if(Je.isDigit(e)&&Je.isDigit(t)){var n=(e-48)*10+(t-48);return n+130}throw new Error("not digits: "+e+t)},r})(),kf=(function(){function r(){}return r.prototype.getEncodingMode=function(){return De},r.prototype.encode=function(e){var t=new X;for(t.append(0);e.hasMoreCharacters();){var n=e.getCurrentChar();t.append(n),e.pos++;var i=Je.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(i!==this.getEncodingMode()){e.signalEncoderChange(ne);break}}var a=t.length()-1,o=1,s=e.getCodewordCount()+a+o;e.updateSymbolInfo(s);var f=e.getSymbolInfo().getDataCapacity()-s>0;if(e.hasMoreCharacters()||f)if(a<=249)t.setCharAt(0,$.getCharAt(a));else if(a<=1555)t.setCharAt(0,$.getCharAt(Math.floor(a/250)+249)),t.insert(1,$.getCharAt(a%250));else throw new Error("Message length not in valid ranges: "+a);for(var u=0,n=t.length();u<n;u++)e.writeCodeword(this.randomize255State(t.charAt(u).charCodeAt(0),e.getCodewordCount()+1))},r.prototype.randomize255State=function(e,t){var n=149*t%255+1,i=e+n;return i<=255?i:i-256},r})(),en=(function(){function r(){}return r.prototype.getEncodingMode=function(){return Te},r.prototype.encodeMaximal=function(e){for(var t=new X,n=0,i=e.pos,a=0;e.hasMoreCharacters();){var o=e.getCurrentChar();e.pos++,n=this.encodeChar(o,t),t.length()%3===0&&(i=e.pos,a=t.length())}if(a!==t.length()){var s=Math.floor(t.length()/3*2),f=Math.floor(e.getCodewordCount()+s+1);e.updateSymbolInfo(f);var u=e.getSymbolInfo().getDataCapacity()-f,c=Math.floor(t.length()%3);(c===2&&u!==2||c===1&&(n>3||u!==1))&&(e.pos=i)}t.length()>0&&e.writeCodeword(Hi),this.handleEOD(e,t)},r.prototype.encode=function(e){for(var t=new X;e.hasMoreCharacters();){var n=e.getCurrentChar();e.pos++;var i=this.encodeChar(n,t),a=Math.floor(t.length()/3)*2,o=e.getCodewordCount()+a;e.updateSymbolInfo(o);var s=e.getSymbolInfo().getDataCapacity()-o;if(!e.hasMoreCharacters()){var f=new X;for(t.length()%3===2&&s!==2&&(i=this.backtrackOneCharacter(e,t,f,i));t.length()%3===1&&(i>3||s!==1);)i=this.backtrackOneCharacter(e,t,f,i);break}var u=t.length();if(u%3===0){var c=Je.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(c!==this.getEncodingMode()){e.signalEncoderChange(ne);break}}}this.handleEOD(e,t)},r.prototype.backtrackOneCharacter=function(e,t,n,i){var a=t.length(),o=t.toString().substring(0,a-i);t.setLengthToZero(),t.append(o),e.pos--;var s=e.getCurrentChar();return i=this.encodeChar(s,n),e.resetSymbolInfo(),i},r.prototype.writeNextTriplet=function(e,t){e.writeCodewords(this.encodeToCodewords(t.toString()));var n=t.toString().substring(3);t.setLengthToZero(),t.append(n)},r.prototype.handleEOD=function(e,t){var n=Math.floor(t.length()/3*2),i=t.length()%3,a=e.getCodewordCount()+n;e.updateSymbolInfo(a);var o=e.getSymbolInfo().getDataCapacity()-a;if(i===2){for(t.append("\0");t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(Er)}else if(o===1&&i===1){for(;t.length()>=3;)this.writeNextTriplet(e,t);e.hasMoreCharacters()&&e.writeCodeword(Er),e.pos--}else if(i===0){for(;t.length()>=3;)this.writeNextTriplet(e,t);(o>0||e.hasMoreCharacters())&&e.writeCodeword(Er)}else throw new Error("Unexpected case. Please report!");e.signalEncoderChange(ne)},r.prototype.encodeChar=function(e,t){if(e===32)return t.append(3),1;if(e>=48&&e<=57)return t.append(e-48+4),1;if(e>=65&&e<=90)return t.append(e-65+14),1;if(e<32)return t.append(0),t.append(e),2;if(e<=47)return t.append(1),t.append(e-33),2;if(e<=64)return t.append(1),t.append(e-58+15),2;if(e<=95)return t.append(1),t.append(e-91+22),2;if(e<=127)return t.append(2),t.append(e-96),2;t.append("1");var n=2;return n+=this.encodeChar(e-128,t),n},r.prototype.encodeToCodewords=function(e){var t=1600*e.charCodeAt(0)+40*e.charCodeAt(1)+e.charCodeAt(2)+1,n=t/256,i=t%256,a=new X;return a.append(n),a.append(i),a.toString()},r})(),Uf=(function(){function r(){}return r.prototype.getEncodingMode=function(){return Ce},r.prototype.encode=function(e){for(var t=new X;e.hasMoreCharacters();){var n=e.getCurrentChar();this.encodeChar(n,t),e.pos++;var i=t.length();if(i>=4){e.writeCodewords(this.encodeToCodewords(t.toString()));var a=t.toString().substring(4);t.setLengthToZero(),t.append(a);var o=Je.lookAheadTest(e.getMessage(),e.pos,this.getEncodingMode());if(o!==this.getEncodingMode()){e.signalEncoderChange(ne);break}}}t.append($.getCharAt(31)),this.handleEOD(e,t)},r.prototype.handleEOD=function(e,t){try{var n=t.length();if(n===0)return;if(n===1){e.updateSymbolInfo();var i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount(),a=e.getRemainingCharacters();if(a>i&&(e.updateSymbolInfo(e.getCodewordCount()+1),i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount()),a<=i&&i<=2)return}if(n>4)throw new Error("Count must not exceed 4");var o=n-1,s=this.encodeToCodewords(t.toString()),f=!e.hasMoreCharacters(),u=f&&o<=2;if(o<=2){e.updateSymbolInfo(e.getCodewordCount()+o);var i=e.getSymbolInfo().getDataCapacity()-e.getCodewordCount();i>=3&&(u=!1,e.updateSymbolInfo(e.getCodewordCount()+s.length))}u?(e.resetSymbolInfo(),e.pos-=o):e.writeCodewords(s)}finally{e.signalEncoderChange(ne)}},r.prototype.encodeChar=function(e,t){e>=32&&e<=63?t.append(e):e>=64&&e<=94?t.append($.getCharAt(e-64)):Je.illegalCharacter($.getCharAt(e))},r.prototype.encodeToCodewords=function(e){var t=e.length;if(t===0)throw new Error("StringBuilder must not be empty");var n=e.charAt(0).charCodeAt(0),i=t>=2?e.charAt(1).charCodeAt(0):0,a=t>=3?e.charAt(2).charCodeAt(0):0,o=t>=4?e.charAt(3).charCodeAt(0):0,s=(n<<18)+(i<<12)+(a<<6)+o,f=s>>16&255,u=s>>8&255,c=s&255,l=new X;return l.append(f),t>=2&&l.append(u),t>=3&&l.append(c),l.toString()},r})(),Hf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),Vf=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ee=(function(){function r(e,t,n,i,a,o,s,f){s===void 0&&(s=0),f===void 0&&(f=0),this.rectangular=e,this.dataCapacity=t,this.errorCodewords=n,this.matrixWidth=i,this.matrixHeight=a,this.dataRegions=o,this.rsBlockData=s,this.rsBlockError=f}return r.lookup=function(e,t,n,i,a){var o,s;t===void 0&&(t=0),n===void 0&&(n=null),i===void 0&&(i=null),a===void 0&&(a=!0);try{for(var f=Vf(Wf),u=f.next();!u.done;u=f.next()){var c=u.value;if(!(t===1&&c.rectangular)&&!(t===2&&!c.rectangular)&&!(n!=null&&(c.getSymbolWidth()<n.getWidth()||c.getSymbolHeight()<n.getHeight()))&&!(i!=null&&(c.getSymbolWidth()>i.getWidth()||c.getSymbolHeight()>i.getHeight()))&&e<=c.dataCapacity)return c}}catch(l){o={error:l}}finally{try{u&&!u.done&&(s=f.return)&&s.call(f)}finally{if(o)throw o.error}}if(a)throw new Error("Can't find a symbol arrangement that matches the message. Data codewords: "+e);return null},r.prototype.getHorizontalDataRegions=function(){switch(this.dataRegions){case 1:return 1;case 2:case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getVerticalDataRegions=function(){switch(this.dataRegions){case 1:case 2:return 1;case 4:return 2;case 16:return 4;case 36:return 6;default:throw new Error("Cannot handle this number of data regions")}},r.prototype.getSymbolDataWidth=function(){return this.getHorizontalDataRegions()*this.matrixWidth},r.prototype.getSymbolDataHeight=function(){return this.getVerticalDataRegions()*this.matrixHeight},r.prototype.getSymbolWidth=function(){return this.getSymbolDataWidth()+this.getHorizontalDataRegions()*2},r.prototype.getSymbolHeight=function(){return this.getSymbolDataHeight()+this.getVerticalDataRegions()*2},r.prototype.getCodewordCount=function(){return this.dataCapacity+this.errorCodewords},r.prototype.getInterleavedBlockCount=function(){return this.rsBlockData?this.dataCapacity/this.rsBlockData:1},r.prototype.getDataCapacity=function(){return this.dataCapacity},r.prototype.getErrorCodewords=function(){return this.errorCodewords},r.prototype.getDataLengthForInterleavedBlock=function(e){return this.rsBlockData},r.prototype.getErrorLengthForInterleavedBlock=function(e){return this.rsBlockError},r})(),Gf=(function(r){Hf(e,r);function e(){return r.call(this,!1,1558,620,22,22,36,-1,62)||this}return e.prototype.getInterleavedBlockCount=function(){return 10},e.prototype.getDataLengthForInterleavedBlock=function(t){return t<=8?156:155},e})(ee),Wf=[new ee(!1,3,5,8,8,1),new ee(!1,5,7,10,10,1),new ee(!0,5,7,16,6,1),new ee(!1,8,10,12,12,1),new ee(!0,10,11,14,6,2),new ee(!1,12,12,14,14,1),new ee(!0,16,14,24,10,1),new ee(!1,18,14,16,16,1),new ee(!1,22,18,18,18,1),new ee(!0,22,18,16,10,2),new ee(!1,30,20,20,20,1),new ee(!0,32,24,16,14,2),new ee(!1,36,24,22,22,1),new ee(!1,44,28,24,24,1),new ee(!0,49,28,22,14,2),new ee(!1,62,36,14,14,4),new ee(!1,86,42,16,16,4),new ee(!1,114,48,18,18,4),new ee(!1,144,56,20,20,4),new ee(!1,174,68,22,22,4),new ee(!1,204,84,24,24,4,102,42),new ee(!1,280,112,14,14,16,140,56),new ee(!1,368,144,16,16,16,92,36),new ee(!1,456,192,18,18,16,114,48),new ee(!1,576,224,20,20,16,144,56),new ee(!1,696,272,22,22,16,174,68),new ee(!1,816,336,24,24,16,136,56),new ee(!1,1050,408,18,18,36,175,68),new ee(!1,1304,496,20,20,36,163,62),new Gf],Xf=(function(){function r(e){this.msg=e,this.pos=0,this.skipAtEnd=0;for(var t=e.split("").map(function(s){return s.charCodeAt(0)}),n=new X,i=0,a=t.length;i<a;i++){var o=String.fromCharCode(t[i]&255);if(o==="?"&&e.charAt(i)!=="?")throw new Error("Message contains characters outside ISO-8859-1 encoding.");n.append(o)}this.msg=n.toString(),this.shape=0,this.codewords=new X,this.newEncoding=-1}return r.prototype.setSymbolShape=function(e){this.shape=e},r.prototype.setSizeConstraints=function(e,t){this.minSize=e,this.maxSize=t},r.prototype.getMessage=function(){return this.msg},r.prototype.setSkipAtEnd=function(e){this.skipAtEnd=e},r.prototype.getCurrentChar=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCurrent=function(){return this.msg.charCodeAt(this.pos)},r.prototype.getCodewords=function(){return this.codewords},r.prototype.writeCodewords=function(e){this.codewords.append(e)},r.prototype.writeCodeword=function(e){this.codewords.append(e)},r.prototype.getCodewordCount=function(){return this.codewords.length()},r.prototype.getNewEncoding=function(){return this.newEncoding},r.prototype.signalEncoderChange=function(e){this.newEncoding=e},r.prototype.resetEncoderSignal=function(){this.newEncoding=-1},r.prototype.hasMoreCharacters=function(){return this.pos<this.getTotalMessageCharCount()},r.prototype.getTotalMessageCharCount=function(){return this.msg.length-this.skipAtEnd},r.prototype.getRemainingCharacters=function(){return this.getTotalMessageCharCount()-this.pos},r.prototype.getSymbolInfo=function(){return this.symbolInfo},r.prototype.updateSymbolInfo=function(e){e===void 0&&(e=this.getCodewordCount()),(this.symbolInfo==null||e>this.symbolInfo.getDataCapacity())&&(this.symbolInfo=ee.lookup(e,this.shape,this.minSize,this.maxSize,!0))},r.prototype.resetSymbolInfo=function(){this.symbolInfo=null},r})(),zf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),jf=(function(r){zf(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getEncodingMode=function(){return xe},e.prototype.encode=function(t){for(var n=new X;t.hasMoreCharacters();){var i=t.getCurrentChar();t.pos++,this.encodeChar(i,n);var a=n.length();if(a%3===0){this.writeNextTriplet(t,n);var o=Je.lookAheadTest(t.getMessage(),t.pos,this.getEncodingMode());if(o!==this.getEncodingMode()){t.signalEncoderChange(ne);break}}}this.handleEOD(t,n)},e.prototype.encodeChar=function(t,n){switch(t){case 13:n.append(0);break;case 42:n.append(1);break;case 62:n.append(2);break;case 32:n.append(3);break;default:t>=48&&t<=57?n.append(t-48+4):t>=65&&t<=90?n.append(t-65+14):Je.illegalCharacter($.getCharAt(t));break}return 1},e.prototype.handleEOD=function(t,n){t.updateSymbolInfo();var i=t.getSymbolInfo().getDataCapacity()-t.getCodewordCount(),a=n.length();t.pos-=a,(t.getRemainingCharacters()>1||i>1||t.getRemainingCharacters()!==i)&&t.writeCodeword(Ff),t.getNewEncoding()<0&&t.signalEncoderChange(ne)},e})(en),Yf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),$f=(function(r){Yf(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getEncodingMode=function(){return Le},e.prototype.encodeChar=function(t,n){if(t===32)return n.append(3),1;if(t>=48&&t<=57)return n.append(t-48+4),1;if(t>=97&&t<=122)return n.append(t-97+14),1;if(t<32)return n.append(0),n.append(t),2;if(t<=47)return n.append(1),n.append(t-33),2;if(t<=64)return n.append(1),n.append(t-58+15),2;if(t>=91&&t<=95)return n.append(1),n.append(t-91+22),2;if(t===96)return n.append(2),n.append(0),2;if(t<=90)return n.append(2),n.append(t-65+1),2;if(t<=127)return n.append(2),n.append(t-123+27),2;n.append("1");var i=2;return i+=this.encodeChar(t-128,n),i},e})(en),Je=(function(){function r(){}return r.randomize253State=function(e){var t=149*e%253+1,n=Kn+t;return n<=254?n:n-254},r.encodeHighLevel=function(e,t,n,i,a){t===void 0&&(t=0),n===void 0&&(n=null),i===void 0&&(i=null),a===void 0&&(a=!1);var o=new en,s=[new Lf,o,new $f,new jf,new Uf,new kf],f=new Xf(e);f.setSymbolShape(t),f.setSizeConstraints(n,i),e.startsWith(qn)&&e.endsWith(Jn)?(f.writeCodeword(Df),f.setSkipAtEnd(2),f.pos+=qn.length):e.startsWith(Qn)&&e.endsWith(Jn)&&(f.writeCodeword(Nf),f.setSkipAtEnd(2),f.pos+=Qn.length);var u=ne;for(a&&(o.encodeMaximal(f),u=f.getNewEncoding(),f.resetEncoderSignal());f.hasMoreCharacters();)s[u].encode(f),f.getNewEncoding()>=0&&(u=f.getNewEncoding(),f.resetEncoderSignal());var c=f.getCodewordCount();f.updateSymbolInfo();var l=f.getSymbolInfo().getDataCapacity();c<l&&u!==ne&&u!==De&&u!==Ce&&f.writeCodeword("þ");var h=f.getCodewords();for(h.length()<l&&h.append(Kn);h.length()<l;)h.append(this.randomize253State(h.length()+1));return f.getCodewords().toString()},r.lookAheadTest=function(e,t,n){var i=this.lookAheadTestIntern(e,t,n);if(n===xe&&i===xe){for(var a=Math.min(t+3,e.length),o=t;o<a;o++)if(!this.isNativeX12(e.charCodeAt(o)))return ne}else if(n===Ce&&i===Ce){for(var a=Math.min(t+4,e.length),o=t;o<a;o++)if(!this.isNativeEDIFACT(e.charCodeAt(o)))return ne}return i},r.lookAheadTestIntern=function(e,t,n){if(t>=e.length)return n;var i;n===ne?i=[0,1,1,1,1,1.25]:(i=[1,2,2,2,2,2.25],i[n]=0);for(var a=0,o=new Uint8Array(6),s=[];;){if(t+a===e.length){_e.fill(o,0),_e.fill(s,0);var f=this.findMinimums(i,s,z.MAX_VALUE,o),u=this.getMinimumCount(o);if(s[ne]===f)return ne;if(u===1){if(o[De]>0)return De;if(o[Ce]>0)return Ce;if(o[Le]>0)return Le;if(o[xe]>0)return xe}return Te}var c=e.charCodeAt(t+a);if(a++,this.isDigit(c)?i[ne]+=.5:this.isExtendedASCII(c)?(i[ne]=Math.ceil(i[ne]),i[ne]+=2):(i[ne]=Math.ceil(i[ne]),i[ne]++),this.isNativeC40(c)?i[Te]+=2/3:this.isExtendedASCII(c)?i[Te]+=8/3:i[Te]+=4/3,this.isNativeText(c)?i[Le]+=2/3:this.isExtendedASCII(c)?i[Le]+=8/3:i[Le]+=4/3,this.isNativeX12(c)?i[xe]+=2/3:this.isExtendedASCII(c)?i[xe]+=13/3:i[xe]+=10/3,this.isNativeEDIFACT(c)?i[Ce]+=3/4:this.isExtendedASCII(c)?i[Ce]+=17/4:i[Ce]+=13/4,this.isSpecialB256(c)?i[De]+=4:i[De]++,a>=4){if(_e.fill(o,0),_e.fill(s,0),this.findMinimums(i,s,z.MAX_VALUE,o),s[ne]<this.min(s[De],s[Te],s[Le],s[xe],s[Ce]))return ne;if(s[De]<s[ne]||s[De]+1<this.min(s[Te],s[Le],s[xe],s[Ce]))return De;if(s[Ce]+1<this.min(s[De],s[Te],s[Le],s[xe],s[ne]))return Ce;if(s[Le]+1<this.min(s[De],s[Te],s[Ce],s[xe],s[ne]))return Le;if(s[xe]+1<this.min(s[De],s[Te],s[Ce],s[Le],s[ne]))return xe;if(s[Te]+1<this.min(s[ne],s[De],s[Ce],s[Le])){if(s[Te]<s[xe])return Te;if(s[Te]===s[xe]){for(var l=t+a+1;l<e.length;){var h=e.charCodeAt(l);if(this.isX12TermSep(h))return xe;if(!this.isNativeX12(h))break;l++}return Te}}}}},r.min=function(e,t,n,i,a){var o=Math.min(e,Math.min(t,Math.min(n,i)));return a===void 0?o:Math.min(o,a)},r.findMinimums=function(e,t,n,i){for(var a=0;a<6;a++){var o=t[a]=Math.ceil(e[a]);n>o&&(n=o,_e.fill(i,0)),n===o&&(i[a]=i[a]+1)}return n},r.getMinimumCount=function(e){for(var t=0,n=0;n<6;n++)t+=e[n];return t||0},r.isDigit=function(e){return e>=48&&e<=57},r.isExtendedASCII=function(e){return e>=128&&e<=255},r.isNativeC40=function(e){return e===32||e>=48&&e<=57||e>=65&&e<=90},r.isNativeText=function(e){return e===32||e>=48&&e<=57||e>=97&&e<=122},r.isNativeX12=function(e){return this.isX12TermSep(e)||e===32||e>=48&&e<=57||e>=65&&e<=90},r.isX12TermSep=function(e){return e===13||e===42||e===62},r.isNativeEDIFACT=function(e){return e>=32&&e<=94},r.isSpecialB256=function(e){return!1},r.determineConsecutiveDigitCount=function(e,t){t===void 0&&(t=0);for(var n=e.length,i=t;i<n&&this.isDigit(e.charCodeAt(i));)i++;return i-t},r.illegalCharacter=function(e){var t=z.toHexString(e.charCodeAt(0));throw t="0000".substring(0,4-t.length)+t,new Error("Illegal character: "+e+" (0x"+t+")")},r})(),Cr=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ei=(function(){function r(e){this.charset=e,this.name=e.name}return r.prototype.canEncode=function(e){try{return Ye.encode(e,this.charset)!=null}catch{return!1}},r})(),Zf=(function(){function r(e,t,n){var i,a,o,s,f,u;this.ENCODERS=["IBM437","ISO-8859-2","ISO-8859-3","ISO-8859-4","ISO-8859-5","ISO-8859-6","ISO-8859-7","ISO-8859-8","ISO-8859-9","ISO-8859-10","ISO-8859-11","ISO-8859-13","ISO-8859-14","ISO-8859-15","ISO-8859-16","windows-1250","windows-1251","windows-1252","windows-1256","Shift_JIS"].map(function(B){return new ei(Sf.forName(B))}),this.encoders=[];var c=[];c.push(new ei(If.ISO_8859_1));for(var l=t!=null&&t.name.startsWith("UTF"),h=0;h<e.length;h++){var d=!1;try{for(var v=(i=void 0,Cr(c)),p=v.next();!p.done;p=v.next()){var x=p.value,y=e.charAt(h),w=y.charCodeAt(0);if(w===n||x.canEncode(y)){d=!0;break}}}catch(B){i={error:B}}finally{try{p&&!p.done&&(a=v.return)&&a.call(v)}finally{if(i)throw i.error}}if(!d)try{for(var A=(o=void 0,Cr(this.ENCODERS)),C=A.next();!C.done;C=A.next()){var x=C.value;if(x.canEncode(e.charAt(h))){c.push(x),d=!0;break}}}catch(B){o={error:B}}finally{try{C&&!C.done&&(s=A.return)&&s.call(A)}finally{if(o)throw o.error}}d||(l=!0)}if(c.length===1&&!l)this.encoders=[c[0]];else{this.encoders=[];var O=0;try{for(var b=Cr(c),T=b.next();!T.done;T=b.next()){var x=T.value;this.encoders[O++]=x}}catch(B){f={error:B}}finally{try{T&&!T.done&&(u=b.return)&&u.call(b)}finally{if(f)throw f.error}}}var D=-1;if(t!=null){for(var h=0;h<this.encoders.length;h++)if(this.encoders[h]!=null&&t.name===this.encoders[h].name){D=h;break}}this.priorityEncoderIndex=D}return r.prototype.length=function(){return this.encoders.length},r.prototype.getCharsetName=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].name},r.prototype.getCharset=function(e){if(!(e<this.length()))throw new Error("index must be less than length");return this.encoders[e].charset},r.prototype.getECIValue=function(e){return this.encoders[e].charset.getValueIdentifier()},r.prototype.getPriorityEncoderIndex=function(){return this.priorityEncoderIndex},r.prototype.canEncode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return!0},r.prototype.encode=function(e,t){if(!(t<this.length()))throw new Error("index must be less than length");return Ye.encode($.getCharAt(e),this.encoders[t].name)},r})(),Kf=3,qf=(function(){function r(e,t,n){this.fnc1=n;var i=new Zf(e,t,n);if(i.length()===1)for(var a=0;a<this.bytes.length;a++){var o=e.charAt(a).charCodeAt(0);this.bytes[a]=o===n?1e3:o}else this.bytes=this.encodeMinimally(e,i,n)}return r.prototype.getFNC1Character=function(){return this.fnc1},r.prototype.length=function(){return this.bytes.length},r.prototype.haveNCharacters=function(e,t){if(e+t-1>=this.bytes.length)return!1;for(var n=0;n<t;n++)if(this.isECI(e+n))return!1;return!0},r.prototype.charAt=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(this.isECI(e))throw new Error("value at "+e+" is not a character but an ECI");return this.isFNC1(e)?this.fnc1:this.bytes[e]},r.prototype.subSequence=function(e,t){if(e<0||e>t||t>this.length())throw new Error(""+e);for(var n=new X,i=e;i<t;i++){if(this.isECI(i))throw new Error("value at "+i+" is not a character but an ECI");n.append(this.charAt(i))}return n.toString()},r.prototype.isECI=function(e){if(e<0||e>=this.length())throw new Error(""+e);return this.bytes[e]>255&&this.bytes[e]<=999},r.prototype.isFNC1=function(e){if(e<0||e>=this.length())throw new Error(""+e);return this.bytes[e]===1e3},r.prototype.getECIValue=function(e){if(e<0||e>=this.length())throw new Error(""+e);if(!this.isECI(e))throw new Error("value at "+e+" is not an ECI but a character");return this.bytes[e]-256},r.prototype.addEdge=function(e,t,n){(e[t][n.encoderIndex]==null||e[t][n.encoderIndex].cachedTotalSize>n.cachedTotalSize)&&(e[t][n.encoderIndex]=n)},r.prototype.addEdges=function(e,t,n,i,a,o){var s=e.charAt(i).charCodeAt(0),f=0,u=t.length();t.getPriorityEncoderIndex()>=0&&(s===o||t.canEncode(s,t.getPriorityEncoderIndex()))&&(f=t.getPriorityEncoderIndex(),u=f+1);for(var c=f;c<u;c++)(s===o||t.canEncode(s,c))&&this.addEdge(n,i+1,new ti(s,t,c,a,o))},r.prototype.encodeMinimally=function(e,t,n){var i=e.length,a=new ti[i+1][t.length()];this.addEdges(e,t,a,0,null,n);for(var o=1;o<=i;o++){for(var s=0;s<t.length();s++)a[o][s]!=null&&o<i&&this.addEdges(e,t,a,o,a[o][s],n);for(var s=0;s<t.length();s++)a[o-1][s]=null}for(var f=-1,u=z.MAX_VALUE,s=0;s<t.length();s++)if(a[i][s]!=null){var c=a[i][s];c.cachedTotalSize<u&&(u=c.cachedTotalSize,f=s)}if(f<0)throw new Error('Failed to encode "'+e+'"');for(var l=[],h=a[i][f];h!=null;){if(h.isFNC1())l.unshift(1e3);else for(var d=t.encode(h.c,h.encoderIndex),o=d.length-1;o>=0;o--)l.unshift(d[o]&255);var v=h.previous===null?0:h.previous.encoderIndex;v!==h.encoderIndex&&l.unshift(256+t.getECIValue(h.encoderIndex)),h=h.previous}for(var p=[],o=0;o<p.length;o++)p[o]=l[o];return p},r})(),ti=(function(){function r(e,t,n,i,a){this.c=e,this.encoderSet=t,this.encoderIndex=n,this.previous=i,this.fnc1=a,this.c=e===a?1e3:e;var o=this.isFNC1()?1:t.encode(e,n).length,s=i===null?0:i.encoderIndex;s!==n&&(o+=Kf),i!=null&&(o+=i.cachedTotalSize),this.cachedTotalSize=o}return r.prototype.isFNC1=function(){return this.c===1e3},r})(),Qf=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),ri;(function(r){r[r.ASCII=0]="ASCII",r[r.C40=1]="C40",r[r.TEXT=2]="TEXT",r[r.X12=3]="X12",r[r.EDF=4]="EDF",r[r.B256=5]="B256"})(ri||(ri={}));(function(r){Qf(e,r);function e(t,n,i,a,o){var s=r.call(this,t,n,i)||this;return s.shape=a,s.macroId=o,s}return e.prototype.getMacroId=function(){return this.macroId},e.prototype.getShapeHint=function(){return this.shape},e})(qf);var Jf=(function(){function r(){}return r.prototype.isCompact=function(){return this.compact},r.prototype.setCompact=function(e){this.compact=e},r.prototype.getSize=function(){return this.size},r.prototype.setSize=function(e){this.size=e},r.prototype.getLayers=function(){return this.layers},r.prototype.setLayers=function(e){this.layers=e},r.prototype.getCodeWords=function(){return this.codeWords},r.prototype.setCodeWords=function(e){this.codeWords=e},r.prototype.getMatrix=function(){return this.matrix},r.prototype.setMatrix=function(e){this.matrix=e},r})(),ni=(function(){function r(){}return r.singletonList=function(e){return[e]},r.min=function(e,t){return e.sort(t)[0]},r})(),e0=(function(){function r(e){this.previous=e}return r.prototype.getPrevious=function(){return this.previous},r})(),t0=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),tn=(function(r){t0(e,r);function e(t,n,i){var a=r.call(this,t)||this;return a.value=n,a.bitCount=i,a}return e.prototype.appendTo=function(t,n){t.appendBits(this.value,this.bitCount)},e.prototype.add=function(t,n){return new e(this,t,n)},e.prototype.addBinaryShift=function(t,n){return console.warn("addBinaryShift on SimpleToken, this simply returns a copy of this token"),new e(this,t,n)},e.prototype.toString=function(){var t=this.value&(1<<this.bitCount)-1;return t|=1<<this.bitCount,"<"+z.toBinaryString(t|1<<this.bitCount).substring(1)+">"},e})(e0),r0=(function(){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var a in i)i.hasOwnProperty(a)&&(n[a]=i[a])},r(e,t)};return function(e,t){r(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}})(),n0=(function(r){r0(e,r);function e(t,n,i){var a=r.call(this,t,0,0)||this;return a.binaryShiftStart=n,a.binaryShiftByteCount=i,a}return e.prototype.appendTo=function(t,n){for(var i=0;i<this.binaryShiftByteCount;i++)(i===0||i===31&&this.binaryShiftByteCount<=62)&&(t.appendBits(31,5),this.binaryShiftByteCount>62?t.appendBits(this.binaryShiftByteCount-31,16):i===0?t.appendBits(Math.min(this.binaryShiftByteCount,31),5):t.appendBits(this.binaryShiftByteCount-31,5)),t.appendBits(n[this.binaryShiftStart+i],8)},e.prototype.addBinaryShift=function(t,n){return new e(this,t,n)},e.prototype.toString=function(){return"<"+this.binaryShiftStart+"::"+(this.binaryShiftStart+this.binaryShiftByteCount-1)+">"},e})(tn);function i0(r,e,t){return new n0(r,e,t)}function Nt(r,e,t){return new tn(r,e,t)}var a0=["UPPER","LOWER","DIGIT","MIXED","PUNCT"],lt=0,ir=1,je=2,Vi=3,nt=4,o0=new tn(null,0,0),mr=[Int32Array.from([0,(5<<16)+28,(5<<16)+30,(5<<16)+29,656318]),Int32Array.from([(9<<16)+480+14,0,(5<<16)+30,(5<<16)+29,656318]),Int32Array.from([(4<<16)+14,(9<<16)+448+28,0,(9<<16)+448+29,932798]),Int32Array.from([(5<<16)+29,(5<<16)+28,656318,0,(5<<16)+30]),Int32Array.from([(5<<16)+31,656380,656382,656381,0])],s0=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};function f0(r){var e,t;try{for(var n=s0(r),i=n.next();!i.done;i=n.next()){var a=i.value;_e.fill(a,-1)}}catch(o){e={error:o}}finally{try{i&&!i.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}return r[lt][nt]=0,r[ir][nt]=0,r[ir][lt]=28,r[Vi][nt]=0,r[je][nt]=0,r[je][lt]=15,r}var Gi=f0(_e.createInt32Array(6,6)),u0=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},c0=(function(){function r(e,t,n,i){this.token=e,this.mode=t,this.binaryShiftByteCount=n,this.bitCount=i}return r.prototype.getMode=function(){return this.mode},r.prototype.getToken=function(){return this.token},r.prototype.getBinaryShiftByteCount=function(){return this.binaryShiftByteCount},r.prototype.getBitCount=function(){return this.bitCount},r.prototype.latchAndAppend=function(e,t){var n=this.bitCount,i=this.token;if(e!==this.mode){var a=mr[this.mode][e];i=Nt(i,a&65535,a>>16),n+=a>>16}var o=e===je?4:5;return i=Nt(i,t,o),new r(i,e,0,n+o)},r.prototype.shiftAndAppend=function(e,t){var n=this.token,i=this.mode===je?4:5;return n=Nt(n,Gi[this.mode][e],i),n=Nt(n,t,5),new r(n,this.mode,0,this.bitCount+i+5)},r.prototype.addBinaryShiftChar=function(e){var t=this.token,n=this.mode,i=this.bitCount;if(this.mode===nt||this.mode===je){var a=mr[n][lt];t=Nt(t,a&65535,a>>16),i+=a>>16,n=lt}var o=this.binaryShiftByteCount===0||this.binaryShiftByteCount===31?18:this.binaryShiftByteCount===62?9:8,s=new r(t,n,this.binaryShiftByteCount+1,i+o);return s.binaryShiftByteCount===2078&&(s=s.endBinaryShift(e+1)),s},r.prototype.endBinaryShift=function(e){if(this.binaryShiftByteCount===0)return this;var t=this.token;return t=i0(t,e-this.binaryShiftByteCount,this.binaryShiftByteCount),new r(t,this.mode,0,this.bitCount)},r.prototype.isBetterThanOrEqualTo=function(e){var t=this.bitCount+(mr[this.mode][e.mode]>>16);return this.binaryShiftByteCount<e.binaryShiftByteCount?t+=r.calculateBinaryShiftCost(e)-r.calculateBinaryShiftCost(this):this.binaryShiftByteCount>e.binaryShiftByteCount&&e.binaryShiftByteCount>0&&(t+=10),t<=e.bitCount},r.prototype.toBitArray=function(e){for(var t,n,i=[],a=this.endBinaryShift(e.length).token;a!==null;a=a.getPrevious())i.unshift(a);var o=new Se;try{for(var s=u0(i),f=s.next();!f.done;f=s.next()){var u=f.value;u.appendTo(o,e)}}catch(c){t={error:c}}finally{try{f&&!f.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r.prototype.toString=function(){return $.format("%s bits=%d bytes=%d",a0[this.mode],this.bitCount,this.binaryShiftByteCount)},r.calculateBinaryShiftCost=function(e){return e.binaryShiftByteCount>62?21:e.binaryShiftByteCount>31?20:e.binaryShiftByteCount>0?10:0},r.INITIAL_STATE=new r(o0,lt,0,0),r})();function l0(r){var e=$.getCharCode(" "),t=$.getCharCode("."),n=$.getCharCode(",");r[lt][e]=1;for(var i=$.getCharCode("Z"),a=$.getCharCode("A"),o=a;o<=i;o++)r[lt][o]=o-a+2;r[ir][e]=1;for(var s=$.getCharCode("z"),f=$.getCharCode("a"),o=f;o<=s;o++)r[ir][o]=o-f+2;r[je][e]=1;for(var u=$.getCharCode("9"),c=$.getCharCode("0"),o=c;o<=u;o++)r[je][o]=o-c+2;r[je][n]=12,r[je][t]=13;for(var l=["\0"," ","","","","","","","\x07","\b","	",`
`,"\v","\f","\r","\x1B","","","","","@","\\","^","_","`","|","~",""],h=0;h<l.length;h++)r[Vi][$.getCharCode(l[h])]=h;for(var d=["\0","\r","\0","\0","\0","\0","!","'","#","$","%","&","'","(",")","*","+",",","-",".","/",":",";","<","=",">","?","[","]","{","}"],h=0;h<d.length;h++)$.getCharCode(d[h])>0&&(r[nt][$.getCharCode(d[h])]=h);return r}var Sr=l0(_e.createInt32Array(5,256)),Zt=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},h0=(function(){function r(e){this.text=e}return r.prototype.encode=function(){for(var e=$.getCharCode(" "),t=$.getCharCode(`
`),n=ni.singletonList(c0.INITIAL_STATE),i=0;i<this.text.length;i++){var a=void 0,o=i+1<this.text.length?this.text[i+1]:0;switch(this.text[i]){case $.getCharCode("\r"):a=o===t?2:0;break;case $.getCharCode("."):a=o===e?3:0;break;case $.getCharCode(","):a=o===e?4:0;break;case $.getCharCode(":"):a=o===e?5:0;break;default:a=0}a>0?(n=r.updateStateListForPair(n,i,a),i++):n=this.updateStateListForChar(n,i)}var s=ni.min(n,function(f,u){return f.getBitCount()-u.getBitCount()});return s.toBitArray(this.text)},r.prototype.updateStateListForChar=function(e,t){var n,i,a=[];try{for(var o=Zt(e),s=o.next();!s.done;s=o.next()){var f=s.value;this.updateStateForChar(f,t,a)}}catch(u){n={error:u}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(n)throw n.error}}return r.simplifyStates(a)},r.prototype.updateStateForChar=function(e,t,n){for(var i=this.text[t]&255,a=Sr[e.getMode()][i]>0,o=null,s=0;s<=nt;s++){var f=Sr[s][i];if(f>0){if(o==null&&(o=e.endBinaryShift(t)),!a||s===e.getMode()||s===je){var u=o.latchAndAppend(s,f);n.push(u)}if(!a&&Gi[e.getMode()][s]>=0){var c=o.shiftAndAppend(s,f);n.push(c)}}}if(e.getBinaryShiftByteCount()>0||Sr[e.getMode()][i]===0){var l=e.addBinaryShiftChar(t);n.push(l)}},r.updateStateListForPair=function(e,t,n){var i,a,o=[];try{for(var s=Zt(e),f=s.next();!f.done;f=s.next()){var u=f.value;this.updateStateForPair(u,t,n,o)}}catch(c){i={error:c}}finally{try{f&&!f.done&&(a=s.return)&&a.call(s)}finally{if(i)throw i.error}}return this.simplifyStates(o)},r.updateStateForPair=function(e,t,n,i){var a=e.endBinaryShift(t);if(i.push(a.latchAndAppend(nt,n)),e.getMode()!==nt&&i.push(a.shiftAndAppend(nt,n)),n===3||n===4){var o=a.latchAndAppend(je,16-n).latchAndAppend(je,1);i.push(o)}if(e.getBinaryShiftByteCount()>0){var s=e.addBinaryShiftChar(t).addBinaryShiftChar(t+1);i.push(s)}},r.simplifyStates=function(e){var t,n,i,a,o=[];try{for(var s=Zt(e),f=s.next();!f.done;f=s.next()){var u=f.value,c=!0,l=function(x){if(x.isBetterThanOrEqualTo(u))return c=!1,"break";u.isBetterThanOrEqualTo(x)&&(o=o.filter(function(y){return y!==x}))};try{for(var h=(i=void 0,Zt(o)),d=h.next();!d.done;d=h.next()){var v=d.value,p=l(v);if(p==="break")break}}catch(x){i={error:x}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}c&&o.push(u)}}catch(x){t={error:x}}finally{try{f&&!f.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return o},r})(),d0=function(r){var e=typeof Symbol=="function"&&Symbol.iterator,t=e&&r[e],n=0;if(t)return t.call(r);if(r&&typeof r.length=="number")return{next:function(){return r&&n>=r.length&&(r=void 0),{value:r&&r[n++],done:!r}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};(function(){function r(){}return r.encodeBytes=function(e){return r.encode(e,r.DEFAULT_EC_PERCENT,r.DEFAULT_AZTEC_LAYERS)},r.encode=function(e,t,n){var i=new h0(e).encode(),a=z.truncDivision(i.getSize()*t,100)+11,o=i.getSize()+a,s,f,u,c,l;if(n!==r.DEFAULT_AZTEC_LAYERS){if(s=n<0,f=Math.abs(n),f>(s?r.MAX_NB_BITS_COMPACT:r.MAX_NB_BITS))throw new L($.format("Illegal value %s for layers",n));u=r.totalBitsInLayer(f,s),c=r.WORD_SIZE[f];var h=u-u%c;if(l=r.stuffBits(i,c),l.getSize()+a>h)throw new L("Data to large for user specified layer");if(s&&l.getSize()>c*64)throw new L("Data to large for user specified layer")}else{c=0,l=null;for(var d=0;;d++){if(d>r.MAX_NB_BITS)throw new L("Data too large for an Aztec code");if(s=d<=3,f=s?d+1:d,u=r.totalBitsInLayer(f,s),!(o>u)){(l==null||c!==r.WORD_SIZE[f])&&(c=r.WORD_SIZE[f],l=r.stuffBits(i,c));var h=u-u%c;if(!(s&&l.getSize()>c*64)&&l.getSize()+a<=h)break}}}var v=r.generateCheckWords(l,u,c),p=l.getSize()/c,x=r.generateModeMessage(s,f,p),y=(s?11:14)+f*4,w=new Int32Array(y),A;if(s){A=y;for(var d=0;d<w.length;d++)w[d]=d}else{A=y+1+2*z.truncDivision(z.truncDivision(y,2)-1,15);for(var C=z.truncDivision(y,2),O=z.truncDivision(A,2),d=0;d<C;d++){var b=d+z.truncDivision(d,15);w[C-d-1]=O-b-1,w[C+d]=O+b+1}}for(var T=new it(A),d=0,D=0;d<f;d++){for(var B=(f-d)*4+(s?9:12),M=0;M<B;M++)for(var ae=M*2,W=0;W<2;W++)v.get(D+ae+W)&&T.set(w[d*2+W],w[d*2+M]),v.get(D+B*2+ae+W)&&T.set(w[d*2+M],w[y-1-d*2-W]),v.get(D+B*4+ae+W)&&T.set(w[y-1-d*2-W],w[y-1-d*2-M]),v.get(D+B*6+ae+W)&&T.set(w[y-1-d*2-M],w[d*2+W]);D+=B*8}if(r.drawModeMessage(T,s,A,x),s)r.drawBullsEye(T,z.truncDivision(A,2),5);else{r.drawBullsEye(T,z.truncDivision(A,2),7);for(var d=0,M=0;d<z.truncDivision(y,2)-1;d+=15,M+=16)for(var W=z.truncDivision(A,2)&1;W<A;W+=2)T.set(z.truncDivision(A,2)-M,W),T.set(z.truncDivision(A,2)+M,W),T.set(W,z.truncDivision(A,2)-M),T.set(W,z.truncDivision(A,2)+M)}var Q=new Jf;return Q.setCompact(s),Q.setSize(A),Q.setLayers(f),Q.setCodeWords(p),Q.setMatrix(T),Q},r.drawBullsEye=function(e,t,n){for(var i=0;i<n;i+=2)for(var a=t-i;a<=t+i;a++)e.set(a,t-i),e.set(a,t+i),e.set(t-i,a),e.set(t+i,a);e.set(t-n,t-n),e.set(t-n+1,t-n),e.set(t-n,t-n+1),e.set(t+n,t-n),e.set(t+n,t-n+1),e.set(t+n,t+n-1)},r.generateModeMessage=function(e,t,n){var i=new Se;return e?(i.appendBits(t-1,2),i.appendBits(n-1,6),i=r.generateCheckWords(i,28,4)):(i.appendBits(t-1,5),i.appendBits(n-1,11),i=r.generateCheckWords(i,40,4)),i},r.drawModeMessage=function(e,t,n,i){var a=z.truncDivision(n,2);if(t)for(var o=0;o<7;o++){var s=a-3+o;i.get(o)&&e.set(s,a-5),i.get(o+7)&&e.set(a+5,s),i.get(20-o)&&e.set(s,a+5),i.get(27-o)&&e.set(a-5,s)}else for(var o=0;o<10;o++){var s=a-5+o+z.truncDivision(o,5);i.get(o)&&e.set(s,a-7),i.get(o+10)&&e.set(a+7,s),i.get(29-o)&&e.set(s,a+7),i.get(39-o)&&e.set(a-7,s)}},r.generateCheckWords=function(e,t,n){var i,a,o=e.getSize()/n,s=new Ui(r.getGF(n)),f=z.truncDivision(t,n),u=r.bitsToWords(e,n,f);s.encode(u,f-o);var c=t%n,l=new Se;l.appendBits(0,c);try{for(var h=d0(Array.from(u)),d=h.next();!d.done;d=h.next()){var v=d.value;l.appendBits(v,n)}}catch(p){i={error:p}}finally{try{d&&!d.done&&(a=h.return)&&a.call(h)}finally{if(i)throw i.error}}return l},r.bitsToWords=function(e,t,n){var i=new Int32Array(n),a,o;for(a=0,o=e.getSize()/t;a<o;a++){for(var s=0,f=0;f<t;f++)s|=e.get(a*t+f)?1<<t-f-1:0;i[a]=s}return i},r.getGF=function(e){switch(e){case 4:return Ve.AZTEC_PARAM;case 6:return Ve.AZTEC_DATA_6;case 8:return Ve.AZTEC_DATA_8;case 10:return Ve.AZTEC_DATA_10;case 12:return Ve.AZTEC_DATA_12;default:throw new L("Unsupported word size "+e)}},r.stuffBits=function(e,t){for(var n=new Se,i=e.getSize(),a=(1<<t)-2,o=0;o<i;o+=t){for(var s=0,f=0;f<t;f++)(o+f>=i||e.get(o+f))&&(s|=1<<t-1-f);(s&a)===a?(n.appendBits(s&a,t),o--):(s&a)===0?(n.appendBits(s|1,t),o--):n.appendBits(s,t)}return n},r.totalBitsInLayer=function(e,t){return((t?88:112)+16*e)*e},r.DEFAULT_EC_PERCENT=33,r.DEFAULT_AZTEC_LAYERS=0,r.MAX_NB_BITS=32,r.MAX_NB_BITS_COMPACT=4,r.WORD_SIZE=Int32Array.from([4,6,6,8,8,8,8,8,8,10,10,10,10,10,10,10,10,10,10,10,10,10,10,12,12,12,12,12,12,12,12,12,12]),r})();var Gt={};(function r(e,t,n,i){var a=!!(e.Worker&&e.Blob&&e.Promise&&e.OffscreenCanvas&&e.OffscreenCanvasRenderingContext2D&&e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype.transferControlToOffscreen&&e.URL&&e.URL.createObjectURL),o=typeof Path2D=="function"&&typeof DOMMatrix=="function",s=(function(){if(!e.OffscreenCanvas)return!1;var S=new OffscreenCanvas(1,1),_=S.getContext("2d");_.fillRect(0,0,1,1);var P=S.transferToImageBitmap();try{_.createPattern(P,"no-repeat")}catch{return!1}return!0})();function f(){}function u(S){var _=t.exports.Promise,P=_!==void 0?_:e.Promise;return typeof P=="function"?new P(S):(S(f,f),null)}var c=(function(S,_){return{transform:function(P){if(S)return P;if(_.has(P))return _.get(P);var k=new OffscreenCanvas(P.width,P.height),H=k.getContext("2d");return H.drawImage(P,0,0),_.set(P,k),k},clear:function(){_.clear()}}})(s,new Map),l=(function(){var S=Math.floor(16.666666666666668),_,P,k={},H=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(_=function(G){var j=Math.random();return k[j]=requestAnimationFrame(function U(Y){H===Y||H+S-1<Y?(H=Y,delete k[j],G()):k[j]=requestAnimationFrame(U)}),j},P=function(G){k[G]&&cancelAnimationFrame(k[G])}):(_=function(G){return setTimeout(G,S)},P=function(G){return clearTimeout(G)}),{frame:_,cancel:P}})(),h=(function(){var S,_,P={};function k(H){function G(j,U){H.postMessage({options:j||{},callback:U})}H.init=function(U){var Y=U.transferControlToOffscreen();H.postMessage({canvas:Y},[Y])},H.fire=function(U,Y,oe){if(_)return G(U,null),_;var de=Math.random().toString(36).slice(2);return _=u(function(fe){function ve(Oe){Oe.data.callback===de&&(delete P[de],H.removeEventListener("message",ve),_=null,c.clear(),oe(),fe())}H.addEventListener("message",ve),G(U,de),P[de]=ve.bind(null,{data:{callback:de}})}),_},H.reset=function(){H.postMessage({reset:!0});for(var U in P)P[U](),delete P[U]}}return function(){if(S)return S;if(!n&&a){var H=["var CONFETTI, SIZE = {}, module = {};","("+r.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{S=new Worker(URL.createObjectURL(new Blob([H])))}catch(G){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("🎊 Could not load worker",G),null}k(S)}return S}})(),d={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function v(S,_){return _?_(S):S}function p(S){return S!=null}function x(S,_,P){return v(S&&p(S[_])?S[_]:d[_],P)}function y(S){return S<0?0:Math.floor(S)}function w(S,_){return Math.floor(Math.random()*(_-S))+S}function A(S){return parseInt(S,16)}function C(S){return S.map(O)}function O(S){var _=String(S).replace(/[^0-9a-f]/gi,"");return _.length<6&&(_=_[0]+_[0]+_[1]+_[1]+_[2]+_[2]),{r:A(_.substring(0,2)),g:A(_.substring(2,4)),b:A(_.substring(4,6))}}function b(S){var _=x(S,"origin",Object);return _.x=x(_,"x",Number),_.y=x(_,"y",Number),_}function T(S){S.width=document.documentElement.clientWidth,S.height=document.documentElement.clientHeight}function D(S){var _=S.getBoundingClientRect();S.width=_.width,S.height=_.height}function B(S){var _=document.createElement("canvas");return _.style.position="fixed",_.style.top="0px",_.style.left="0px",_.style.pointerEvents="none",_.style.zIndex=S,_}function M(S,_,P,k,H,G,j,U,Y){S.save(),S.translate(_,P),S.rotate(G),S.scale(k,H),S.arc(0,0,1,j,U,Y),S.restore()}function ae(S){var _=S.angle*(Math.PI/180),P=S.spread*(Math.PI/180);return{x:S.x,y:S.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:S.startVelocity*.5+Math.random()*S.startVelocity,angle2D:-_+(.5*P-Math.random()*P),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:S.color,shape:S.shape,tick:0,totalTicks:S.ticks,decay:S.decay,drift:S.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:S.gravity*3,ovalScalar:.6,scalar:S.scalar,flat:S.flat}}function W(S,_){_.x+=Math.cos(_.angle2D)*_.velocity+_.drift,_.y+=Math.sin(_.angle2D)*_.velocity+_.gravity,_.velocity*=_.decay,_.flat?(_.wobble=0,_.wobbleX=_.x+10*_.scalar,_.wobbleY=_.y+10*_.scalar,_.tiltSin=0,_.tiltCos=0,_.random=1):(_.wobble+=_.wobbleSpeed,_.wobbleX=_.x+10*_.scalar*Math.cos(_.wobble),_.wobbleY=_.y+10*_.scalar*Math.sin(_.wobble),_.tiltAngle+=.1,_.tiltSin=Math.sin(_.tiltAngle),_.tiltCos=Math.cos(_.tiltAngle),_.random=Math.random()+2);var P=_.tick++/_.totalTicks,k=_.x+_.random*_.tiltCos,H=_.y+_.random*_.tiltSin,G=_.wobbleX+_.random*_.tiltCos,j=_.wobbleY+_.random*_.tiltSin;if(S.fillStyle="rgba("+_.color.r+", "+_.color.g+", "+_.color.b+", "+(1-P)+")",S.beginPath(),o&&_.shape.type==="path"&&typeof _.shape.path=="string"&&Array.isArray(_.shape.matrix))S.fill(ot(_.shape.path,_.shape.matrix,_.x,_.y,Math.abs(G-k)*.1,Math.abs(j-H)*.1,Math.PI/10*_.wobble));else if(_.shape.type==="bitmap"){var U=Math.PI/10*_.wobble,Y=Math.abs(G-k)*.1,oe=Math.abs(j-H)*.1,de=_.shape.bitmap.width*_.scalar,fe=_.shape.bitmap.height*_.scalar,ve=new DOMMatrix([Math.cos(U)*Y,Math.sin(U)*Y,-Math.sin(U)*oe,Math.cos(U)*oe,_.x,_.y]);ve.multiplySelf(new DOMMatrix(_.shape.matrix));var Oe=S.createPattern(c.transform(_.shape.bitmap),"no-repeat");Oe.setTransform(ve),S.globalAlpha=1-P,S.fillStyle=Oe,S.fillRect(_.x-de/2,_.y-fe/2,de,fe),S.globalAlpha=1}else if(_.shape==="circle")S.ellipse?S.ellipse(_.x,_.y,Math.abs(G-k)*_.ovalScalar,Math.abs(j-H)*_.ovalScalar,Math.PI/10*_.wobble,0,2*Math.PI):M(S,_.x,_.y,Math.abs(G-k)*_.ovalScalar,Math.abs(j-H)*_.ovalScalar,Math.PI/10*_.wobble,0,2*Math.PI);else if(_.shape==="star")for(var Z=Math.PI/2*3,Ue=4*_.scalar,$e=8*_.scalar,Ze=_.x,at=_.y,ht=5,tt=Math.PI/ht;ht--;)Ze=_.x+Math.cos(Z)*$e,at=_.y+Math.sin(Z)*$e,S.lineTo(Ze,at),Z+=tt,Ze=_.x+Math.cos(Z)*Ue,at=_.y+Math.sin(Z)*Ue,S.lineTo(Ze,at),Z+=tt;else S.moveTo(Math.floor(_.x),Math.floor(_.y)),S.lineTo(Math.floor(_.wobbleX),Math.floor(H)),S.lineTo(Math.floor(G),Math.floor(j)),S.lineTo(Math.floor(k),Math.floor(_.wobbleY));return S.closePath(),S.fill(),_.tick<_.totalTicks}function Q(S,_,P,k,H){var G=_.slice(),j=S.getContext("2d"),U,Y,oe=u(function(de){function fe(){U=Y=null,j.clearRect(0,0,k.width,k.height),c.clear(),H(),de()}function ve(){n&&!(k.width===i.width&&k.height===i.height)&&(k.width=S.width=i.width,k.height=S.height=i.height),!k.width&&!k.height&&(P(S),k.width=S.width,k.height=S.height),j.clearRect(0,0,k.width,k.height),G=G.filter(function(Oe){return W(j,Oe)}),G.length?U=l.frame(ve):fe()}U=l.frame(ve),Y=fe});return{addFettis:function(de){return G=G.concat(de),oe},canvas:S,promise:oe,reset:function(){U&&l.cancel(U),Y&&Y()}}}function he(S,_){var P=!S,k=!!x(_||{},"resize"),H=!1,G=x(_,"disableForReducedMotion",Boolean),j=a&&!!x(_||{},"useWorker"),U=j?h():null,Y=P?T:D,oe=S&&U?!!S.__confetti_initialized:!1,de=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,fe;function ve(Z,Ue,$e){for(var Ze=x(Z,"particleCount",y),at=x(Z,"angle",Number),ht=x(Z,"spread",Number),tt=x(Z,"startVelocity",Number),Xi=x(Z,"decay",Number),zi=x(Z,"gravity",Number),ji=x(Z,"drift",Number),rn=x(Z,"colors",C),Yi=x(Z,"ticks",Number),nn=x(Z,"shapes"),$i=x(Z,"scalar"),Zi=!!x(Z,"flat"),an=b(Z),on=Ze,dr=[],Ki=S.width*an.x,qi=S.height*an.y;on--;)dr.push(ae({x:Ki,y:qi,angle:at,spread:ht,startVelocity:tt,color:rn[on%rn.length],shape:nn[w(0,nn.length)],ticks:Yi,decay:Xi,gravity:zi,drift:ji,scalar:$i,flat:Zi}));return fe?fe.addFettis(dr):(fe=Q(S,dr,Y,Ue,$e),fe.promise)}function Oe(Z){var Ue=G||x(Z,"disableForReducedMotion",Boolean),$e=x(Z,"zIndex",Number);if(Ue&&de)return u(function(tt){tt()});P&&fe?S=fe.canvas:P&&!S&&(S=B($e),document.body.appendChild(S)),k&&!oe&&Y(S);var Ze={width:S.width,height:S.height};U&&!oe&&U.init(S),oe=!0,U&&(S.__confetti_initialized=!0);function at(){if(U){var tt={getBoundingClientRect:function(){if(!P)return S.getBoundingClientRect()}};Y(tt),U.postMessage({resize:{width:tt.width,height:tt.height}});return}Ze.width=Ze.height=null}function ht(){fe=null,k&&(H=!1,e.removeEventListener("resize",at)),P&&S&&(document.body.contains(S)&&document.body.removeChild(S),S=null,oe=!1)}return k&&!H&&(H=!0,e.addEventListener("resize",at,!1)),U?U.fire(Z,Ze,ht):ve(Z,Ze,ht)}return Oe.reset=function(){U&&U.reset(),fe&&fe.reset()},Oe}var Me;function Ie(){return Me||(Me=he(null,{useWorker:!0,resize:!0})),Me}function ot(S,_,P,k,H,G,j){var U=new Path2D(S),Y=new Path2D;Y.addPath(U,new DOMMatrix(_));var oe=new Path2D;return oe.addPath(Y,new DOMMatrix([Math.cos(j)*H,Math.sin(j)*H,-Math.sin(j)*G,Math.cos(j)*G,P,k])),oe}function ke(S){if(!o)throw new Error("path confetti are not supported in this browser");var _,P;typeof S=="string"?_=S:(_=S.path,P=S.matrix);var k=new Path2D(_),H=document.createElement("canvas"),G=H.getContext("2d");if(!P){for(var j=1e3,U=j,Y=j,oe=0,de=0,fe,ve,Oe=0;Oe<j;Oe+=2)for(var Z=0;Z<j;Z+=2)G.isPointInPath(k,Oe,Z,"nonzero")&&(U=Math.min(U,Oe),Y=Math.min(Y,Z),oe=Math.max(oe,Oe),de=Math.max(de,Z));fe=oe-U,ve=de-Y;var Ue=10,$e=Math.min(Ue/fe,Ue/ve);P=[$e,0,0,$e,-Math.round(fe/2+U)*$e,-Math.round(ve/2+Y)*$e]}return{type:"path",path:_,matrix:P}}function Wt(S){var _,P=1,k="#000000",H='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';typeof S=="string"?_=S:(_=S.text,P="scalar"in S?S.scalar:P,H="fontFamily"in S?S.fontFamily:H,k="color"in S?S.color:k);var G=10*P,j=""+G+"px "+H,U=new OffscreenCanvas(G,G),Y=U.getContext("2d");Y.font=j;var oe=Y.measureText(_),de=Math.ceil(oe.actualBoundingBoxRight+oe.actualBoundingBoxLeft),fe=Math.ceil(oe.actualBoundingBoxAscent+oe.actualBoundingBoxDescent),ve=2,Oe=oe.actualBoundingBoxLeft+ve,Z=oe.actualBoundingBoxAscent+ve;de+=ve+ve,fe+=ve+ve,U=new OffscreenCanvas(de,fe),Y=U.getContext("2d"),Y.font=j,Y.fillStyle=k,Y.fillText(_,Oe,Z);var Ue=1/P;return{type:"bitmap",bitmap:U.transferToImageBitmap(),matrix:[Ue,0,0,Ue,-de*Ue/2,-fe*Ue/2]}}t.exports=function(){return Ie().apply(this,arguments)},t.exports.reset=function(){Ie().reset()},t.exports.create=he,t.exports.shapeFromPath=ke,t.exports.shapeFromText=Wt})((function(){return typeof window<"u"?window:typeof self<"u"?self:this||{}})(),Gt,!1);const Wi=Gt.exports;Gt.exports.create;window.MobileApp=window.MobileApp||{showConfetti:()=>console.log("Confetti not ready yet"),checkCameraSupport:()=>Promise.resolve(!1),isInstalled:()=>!1,requestNotificationPermission:()=>Promise.resolve(!1)};window.BrowserMultiFormatReader=pf;window.confetti=Wi;window.MobileApp=window.MobileApp||{};Object.assign(window.MobileApp,{showConfetti(){var r=15e3,e=Date.now()+r,t={startVelocity:30,spread:360,ticks:60,zIndex:0};function n(a,o){return Math.random()*(o-a)+a}var i=setInterval(function(){var a=e-Date.now();if(a<=0)return clearInterval(i);var o=50*(a/r);Wi({...t,particleCount:o,origin:{x:n(.5,.5),y:Math.random()-.2},shapes:["star"]})},250)},getGreeting(){const r=new Date().getHours();return r<12?"Good morning":r<17?"Good afternoon":"Good evening"},formatDate(r){return new Date(r).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})},async checkCameraSupport(){try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return!1;if(navigator.mediaDevices.enumerateDevices)try{return(await navigator.mediaDevices.enumerateDevices()).some(t=>t.kind==="videoinput")}catch(r){return console.log("Could not enumerate devices:",r),!0}return!0}catch(r){return console.log("Camera support check failed:",r),!1}},async startCamera(r){try{console.log("Requesting camera access...");const e=await navigator.mediaDevices.getUserMedia({video:{facingMode:"environment",width:{ideal:1280,min:640},height:{ideal:720,min:480}}});return console.log("Camera access granted, setting up video element..."),r.srcObject=e,r.setAttribute("autoplay",""),r.setAttribute("playsinline",""),r.setAttribute("muted",""),new Promise((t,n)=>{r.onloadedmetadata=()=>{console.log("Video metadata loaded, starting playback..."),r.play().then(()=>{console.log("Video playback started successfully"),t(e)}).catch(i=>{console.error("Video play failed:",i),e.getTracks().forEach(a=>a.stop()),n(i)})},r.onerror=i=>{console.error("Video element error:",i),e.getTracks().forEach(a=>a.stop()),n(i)}})}catch(e){throw console.error("Error accessing camera:",e),e}},stopCamera(r){r&&r.getTracks().forEach(e=>e.stop())},async requestNotificationPermission(){if("Notification"in window){const r=await Notification.requestPermission();return r==="granted"&&this.initializeFCM(),r==="granted"}return!1},async initializeFCM(){try{console.log("FCM initialization would happen here")}catch(r){console.error("FCM initialization failed:",r)}},showLocalNotification(r,e={}){"Notification"in window&&Notification.permission==="granted"&&new Notification(r,{icon:"/icons/icon-192x192.png",badge:"/icons/icon-72x72.png",...e})},vibrate(r=[100,50,100]){"vibrate"in navigator&&navigator.vibrate(r)},isInstalled(){return window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone===!0},showInstallPrompt(){window.deferredPrompt&&(window.deferredPrompt.prompt(),window.deferredPrompt.userChoice.then(r=>{r.outcome==="accepted"&&console.log("User accepted the install prompt"),window.deferredPrompt=null}))}});let ii;window.addEventListener("beforeinstallprompt",r=>{r.preventDefault(),ii=r,window.deferredPrompt=ii});"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(r=>{console.log("SW registered: ",r)}).catch(r=>{console.log("SW registration failed: ",r)})})});export default v0();
