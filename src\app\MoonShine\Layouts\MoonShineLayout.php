<?php

declare(strict_types=1);

namespace App\MoonShine\Layouts;

use MoonShine\Laravel\Layouts\AppLayout;
use MoonShine\ColorManager\ColorManager;
use MoonShine\Contracts\ColorManager\ColorManagerContract;
use MoonShine\MenuManager\MenuGroup;
use MoonShine\MenuManager\MenuItem;

// Import all resource classes
use App\MoonShine\Resources\RoleResource;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\StudentResource;
use Sweet1s\MoonshineRBAC\Resource\PermissionResource;

use App\MoonShine\Resources\EnumClassLevelResource;
use App\MoonShine\Resources\SchoolResource;
use App\MoonShine\Resources\EnumSchoolTypeResource;
use App\MoonShine\Resources\UserAgreementResource;
use App\MoonShine\Resources\SchoolClassResource;
use App\MoonShine\Resources\UserSchoolResource;
use App\MoonShine\Resources\UserClassResource;
use App\MoonShine\Resources\AuthorResource;
use App\MoonShine\Resources\PublisherResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\BookQuestionResource;
use App\MoonShine\Resources\BookWordResource;
use App\MoonShine\Resources\CategoryResource;
use App\MoonShine\Resources\ClassBookResource;
use App\MoonShine\Resources\UserReadingLogResource;
use App\MoonShine\Resources\UserPointResource;
use App\MoonShine\Resources\ActivityCategoryResource;
use App\MoonShine\Resources\ActivityResource;
use App\MoonShine\Resources\UserActivityResource;
use App\MoonShine\Resources\UserActivityReviewResource;
use App\MoonShine\Resources\UserBookResource;
use App\MoonShine\Resources\UserAvatarResource;
use App\MoonShine\Resources\AvatarResource;
use App\MoonShine\Resources\BookTypeResource;
use App\MoonShine\Resources\TeamResource;
use App\MoonShine\Resources\RewardResource;
use App\MoonShine\Resources\UserRewardResource;
use App\MoonShine\Resources\TeamRewardResource;
use App\MoonShine\Resources\LevelResource;
use App\MoonShine\Resources\UserLevelResource;
use App\MoonShine\Resources\UserTeamResource;
use App\MoonShine\Resources\TaskResource;
use App\MoonShine\Resources\EnumTaskTypeResource;

use App\MoonShine\Resources\UserTaskResource;
use App\MoonShine\Resources\ChallengeResource;
use App\MoonShine\Resources\ChallengeTaskResource;
use App\MoonShine\Resources\ClassActivityResource;
use App\MoonShine\Resources\EnumTaskCycleResource;
use App\MoonShine\Resources\PanelBookResource;
use App\MoonShine\Resources\PanelClassBookResource;
use App\MoonShine\Resources\PanelRewardResource;
use App\MoonShine\Resources\PanelTaskResource;
use App\MoonShine\Resources\PanelUserActivityResource;
use App\MoonShine\Resources\PanelUserBookResource;
use App\MoonShine\Resources\RewardTaskResource;
use App\MoonShine\Resources\StudentTeamResource;
use App\MoonShine\Resources\MessageResource;
use App\MoonShine\Resources\MessageRecipientResource;
use MoonShine\UI\Components\Layout\Layout;
use Illuminate\Support\Facades\Vite;
use MoonShine\AssetManager\Css;
use MoonShine\UI\Components\Layout\Footer;
use Sweet1s\MoonshineRBAC\Components\MenuRBAC;

final class MoonShineLayout extends AppLayout
{
    protected function assets(): array
    {
        return [
            ...parent::assets(),
            Css::make(Vite::asset('resources/css/app.css'))
        ];
    }

    protected function menu(): array
    {
        return MenuRBAC::menu(
            MenuGroup::make(__('admin.system'), [
                MenuItem::make(__('admin.roles'), RoleResource::class),
                MenuItem::make('permissions', PermissionResource::class)
                    ->translatable('moonshine-rbac::ui')
                    ->icon('s.shield-check'),

                MenuItem::make(__('admin.school_types'), EnumSchoolTypeResource::class),
                MenuItem::make(__('admin.class_levels'), EnumClassLevelResource::class),
                MenuItem::make(__('admin.schools'), SchoolResource::class),
                MenuItem::make(__('admin.user_agreements'), UserAgreementResource::class),
            ]),
            MenuGroup::make(__('admin.users'), [
                MenuItem::make(__('admin.school_classes'), SchoolClassResource::class),
                MenuItem::make(__('admin.users'), UserResource::class),
                MenuItem::make(__('admin.school_assignments'), UserSchoolResource::class),
                MenuItem::make(__('admin.class_assignments'), UserClassResource::class),
                MenuItem::make(__('admin.teams'), TeamResource::class),
                MenuItem::make(__('admin.user_teams'), UserTeamResource::class),
            ]),
            MenuGroup::make(__('admin.books'), [
                MenuItem::make(__('admin.book_types'), BookTypeResource::class),
                MenuItem::make(__('admin.authors'), AuthorResource::class),
                MenuItem::make(__('admin.publishers'), PublisherResource::class),
                MenuItem::make(__('admin.categories'), CategoryResource::class),
                MenuItem::make(__('admin.books'), BookResource::class),
                MenuItem::make(__('admin.class_books'), ClassBookResource::class),
                MenuItem::make(__('admin.book_questions'), BookQuestionResource::class),
                MenuItem::make(__('admin.book_words'), BookWordResource::class),
            ]),
            MenuGroup::make(__('admin.reading'), [
                MenuItem::make(__('admin.user_books'), UserBookResource::class),
                MenuItem::make(__('admin.user_reading_logs'), UserReadingLogResource::class),
                MenuItem::make(__('admin.user_points'), UserPointResource::class),
                MenuItem::make(__('admin.activity_categories'), ActivityCategoryResource::class),
                MenuItem::make(__('admin.activities'), ActivityResource::class),
                MenuItem::make(__('admin.user_activities'), UserActivityResource::class),
                MenuItem::make(__('admin.user_activity_reviews'), UserActivityReviewResource::class),
                MenuItem::make(__('admin.class_activities'), ClassActivityResource::class),
            ]),
            MenuGroup::make(__('admin.gamification'), [
                MenuItem::make(__('admin.avatars'), AvatarResource::class),
                MenuItem::make(__('admin.user_avatars'), UserAvatarResource::class),
                MenuItem::make(__('admin.rewards'), RewardResource::class),
                MenuItem::make(__('admin.reward_tasks'), RewardTaskResource::class),
                MenuItem::make(__('admin.user_rewards'), UserRewardResource::class),
                MenuItem::make(__('admin.team_rewards'), TeamRewardResource::class),
                MenuItem::make(__('admin.levels'), LevelResource::class),
                MenuItem::make(__('admin.user_levels'), UserLevelResource::class),
            ]),
            MenuGroup::make(__('admin.task_management'), [
                MenuItem::make(__('admin.task_types'), EnumTaskTypeResource::class),
                MenuItem::make(__('admin.task_cycles'), EnumTaskCycleResource::class),
                MenuItem::make(__('admin.tasks'), TaskResource::class),
                MenuItem::make(__('admin.user_tasks'), UserTaskResource::class),

/*
                MenuItem::make(__('admin.stories'), StoryResource::class),
                MenuItem::make(__('admin.story_books'), StoryBookResource::class),
                MenuItem::make(__('admin.story_chapters'), StoryChapterResource::class),
                MenuItem::make(__('admin.story_characters'), StoryCharacterResource::class),
                MenuItem::make(__('admin.story_character_stages'), StoryCharacterStageResource::class),
                MenuItem::make(__('admin.story_achievements'), StoryAchievementResource::class),
                MenuItem::make(__('admin.story_rules'), StoryRuleResource::class),
                MenuItem::make(__('admin.story_rule_details'), StoryRuleDetailResource::class),
             */
            ]),

            MenuGroup::make(__('admin.challenge_management'), [
                MenuItem::make(__('admin.challenges'), ChallengeResource::class),
                MenuItem::make(__('admin.challenge_tasks'), ChallengeTaskResource::class),
            ]),

            MenuGroup::make(__('admin.communication'), [
                MenuItem::make(__('admin.messages'), MessageResource::class),
                MenuItem::make(__('admin.message_recipients'), MessageRecipientResource::class),
            ]),

            MenuItem::make(__('admin.students'), StudentResource::class),
            MenuItem::make(__('admin.teams'), StudentTeamResource::class),

            MenuItem::make(__('admin.books'), PanelBookResource::class),
            MenuItem::make(__('admin.class_books'), PanelClassBookResource::class),
            MenuItem::make(__('admin.user_reading_logs'), PanelUserBookResource::class),
            MenuItem::make(__('admin.book_activities'), PanelUserActivityResource::class),
            MenuItem::make(__('admin.tasks'), PanelTaskResource::class),
            MenuItem::make(__('admin.rewards'), PanelRewardResource::class),
        );            
    }

    /**
     * @param ColorManager $colorManager
     */
    protected function colors(ColorManagerContract $colorManager): void
    {
        parent::colors($colorManager);
//        $colorManager->set('body', '69, 25, 74');
//        $colorManager->set('dark-600', '95, 46, 154');

//        $colorManager->background('69, 25, 74');        
        $colorManager->background('77, 50, 116');        
        // $colorManager->primary('#00000');
    }

    public function build(): Layout
    {
        return parent::build();
    }
    
    protected function getFooterComponent(): Footer
    {
        return Footer::make();
        /*
        return Footer::make()
            ->copyright($this->getFooterCopyright())
            ->menu($this->getFooterMenu());
            */
    }

}
