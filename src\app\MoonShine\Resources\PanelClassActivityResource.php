<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\SchoolClass;
use App\Models\Activity;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Support\Attributes\Icon;
use MoonShine\Support\Enums\Layer;
use MoonShine\UI\Components\Alert;
use MoonShine\UI\Fields\{Number, Switcher};
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

#[Icon('academic-cap')]
class PanelClassActivityResource extends ClassActivityResource
{
    use WithRolePermissions;

    public function getTitle(): string
    {
        return __('admin.activity_types');
    }

    // add information about activities can be used for that class to top layer
    protected function onLoad(): void
    {
        $this->getIndexPage()
            ->pushToLayer(
                layer: Layer::TOP,
                component: Alert::make(type: 'info', icon: 'academic-cap')->content(__('admin.class_activity_alert')),
            );
    }    
    
    protected function indexFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            )
                ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                ->link(link: fn() => '#'),

            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            )
                ->link(link: fn() => '#'),

            Number::make(__('admin.points'), 'points')
                ->badge(fn($value) => $value ? 'green' : 'gray'),
            Switcher::make(__('admin.required'), 'required'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            ),
            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),
            Number::make(__('admin.question_count'), 'question_count'),
            Number::make(__('admin.min_grade'), 'min_grade'),
            Number::make(__('admin.allowed_tries'), 'allowed_tries'),
            Number::make(__('admin.min_word_count'), 'min_word_count'),
            Number::make(__('admin.points'), 'points'),
            Switcher::make(__('admin.required'), 'required'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
        ];
    }

}
