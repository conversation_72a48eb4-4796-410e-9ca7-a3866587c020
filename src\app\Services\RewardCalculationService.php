<?php

namespace App\Services;

use App\Models\{
    Re<PERSON>,
    RewardTask,
    User<PERSON><PERSON><PERSON>,
    Team<PERSON><PERSON>ard,
    UserReadingLog,
    UserActivity,
    UserPoint,
    UserBook,
    Task,
    Team,
    User,
    EnumTaskCycle,
    EnumTaskType,
    Role
};
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

/**
 * RewardCalculationService
 * 
 * Service for calculating and awarding rewards based on user reading activities.
 * This service implements direct database queries for reward task calculations that
 * respect absolute calendar periods (not task assignment dates) and supports book 
 * category filtering, multiple reward tasks, and various task cycles.
 * 
 * Key differences from UserTask calculations:
 * - TOTAL calculations cover ALL user activities since account creation
 * - Time-bounded cycles use absolute calendar periods (today, this week, this month)
 * - No start/end date restrictions from task assignments
 * 
 * @package App\Services
 */
class RewardCalculationService
{
    /**
     * Constructor - no dependencies needed for direct database calculations.
     */
    public function __construct()
    {
        // Direct database calculations - no service dependencies needed
    }

    /**
     * Check and award all eligible rewards for a user based on their current progress.
     * 
     * This is the main entry point for reward checking. It finds all active rewards
     * that the user might be eligible for and checks if they should be awarded.
     * 
     * @param int $userId The user ID to check rewards for
     * @param int|null $readingLogId Optional reading log ID that triggered the check
     * @param int|null $userActivityId Optional user activity ID that triggered the check
     * @return array Array of UserReward instances that were awarded
     */
    public function checkAndAwardUserRewards(int $userId, ?int $readingLogId = null, ?int $userActivityId = null): array
    {
        $awardedRewards = [];

        try {
            // Get all active rewards that have associated tasks (automatic rewards)
            $eligibleRewards = $this->getEligibleRewardsForUser($userId);

            foreach ($eligibleRewards as $reward) {
                $userReward = $this->checkAndAwardSingleReward($reward, $userId, $readingLogId, $userActivityId);
                if ($userReward) {
                    $awardedRewards[] = $userReward;
                }
            }

            // Log reward awarding for debugging
            if (!empty($awardedRewards)) {
                Log::info('Rewards awarded to user', [
                    'user_id' => $userId,
                    'reward_count' => count($awardedRewards),
                    'reward_ids' => collect($awardedRewards)->pluck('reward_id')->toArray(),
                    'reading_log_id' => $readingLogId,
                    'user_activity_id' => $userActivityId,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error in checkAndAwardUserRewards', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $awardedRewards;
    }

    /**
     * Check and award all eligible team rewards for a user's teams.
     * 
     * This method checks all active teams that the user belongs to and determines
     * if any team rewards should be awarded based on collective team progress.
     * 
     * @param int $userId The user ID whose teams to check
     * @param int|null $readingLogId Optional reading log ID that triggered the check
     * @param int|null $userActivityId Optional user activity ID that triggered the check
     * @return array Array of TeamReward instances that were awarded
     */
    public function checkAndAwardTeamRewards(int $userId, ?int $readingLogId = null, ?int $userActivityId = null): array
    {
        $awardedRewards = [];

        try {
            // Get all active teams the user belongs to
            $teams = Team::whereHas('users', function ($query) use ($userId) {
                $query->where('users.id', $userId);
            })->where('active', true)->get();

            foreach ($teams as $team) {
                $teamRewards = $this->checkAndAwardTeamRewardsForTeam($team, $readingLogId, $userActivityId);
                $awardedRewards = array_merge($awardedRewards, $teamRewards);
            }

            // Log team reward awarding for debugging
            if (!empty($awardedRewards)) {
                Log::info('Team rewards awarded', [
                    'user_id' => $userId,
                    'team_reward_count' => count($awardedRewards),
                    'team_reward_ids' => collect($awardedRewards)->pluck('reward_id')->toArray(),
                    'reading_log_id' => $readingLogId,
                    'user_activity_id' => $userActivityId,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error in checkAndAwardTeamRewards', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $awardedRewards;
    }

    /**
     * Get all rewards that are eligible for a user to earn.
     * 
     * This method filters rewards based on:
     * - Active status
     * - Has associated tasks (automatic rewards)
     * - User access permissions (created_by logic)
     * - Not already earned (for non-repeatable rewards)
     * 
     * @param int $userId The user ID to get eligible rewards for
     * @return Collection Collection of Reward instances
     */
    protected function getEligibleRewardsForUser(int $userId): Collection
    {
        $query = Reward::where('active', true)
            ->whereHas('rewardTasks') // Only automatic rewards with tasks            
            ->where(function ($query) use ($userId) {
                // User access logic: rewards created by user, their teachers, or system admins
                $query->whereNull('created_by')
                    ->orWhere('created_by', $userId)
                    ->orWhereHas('creator', function ($q) use ($userId) {
                        $q->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', [Role::SYSTEM_ADMIN]);
                        })
                        ->orWhereHas('classes.users', function ($sq) use ($userId) {
                            $sq->where('users.id', $userId);
                        });
                    });
            })
            ->where(function ($query) use ($userId) {
                // For non-repeatable rewards, exclude already earned ones
                $query->where('repeatable', true)
                    ->orWhere(function ($subQuery) use ($userId) {
                        $subQuery->where('repeatable', false)
                            ->whereDoesntHave('userRewards', function ($q) use ($userId) {
                                $q->where('user_rewards.user_id', $userId);
                            });
                    });
            })
            ->with(['rewardTasks.task.taskType', 'rewardTasks.task.taskCycle', 'rewardTasks.task.categories']);
        return $query->get();
    }

    /**
     * Check if a single reward should be awarded to a user and award it if eligible.
     *
     * This method checks if ALL associated reward tasks are completed. If they are,
     * it awards the reward to the user. This implements an atomic "all-or-nothing"
     * approach for multi-task rewards.
     *
     * @param Reward $reward The reward to check
     * @param int $userId The user ID to check for
     * @param int|null $readingLogId Optional reading log ID
     * @param int|null $userActivityId Optional user activity ID
     * @return UserReward|null The awarded UserReward instance, or null if not awarded
     */
    protected function checkAndAwardSingleReward(Reward $reward, int $userId, ?int $readingLogId = null, ?int $userActivityId = null): ?UserReward
    {
        try {
            // CRITICAL FIX: Check if repeatable reward was already awarded in current cycle
            if ($reward->repeatable && $this->isRepeatableRewardAlreadyAwardedInCurrentCycle($reward, $userId)) {
                return null; // Already awarded in current cycle
            }

            // Get all reward tasks for this reward
            $rewardTasks = $reward->rewardTasks()->with(['task.taskType', 'task.taskCycle', 'task.categories'])->get();

            if ($rewardTasks->isEmpty()) {
                return null; // No tasks to check
            }

            // Check if ALL reward tasks are completed
            $allTasksCompleted = true;
            foreach ($rewardTasks as $rewardTask) {
                if (!$this->isRewardTaskCompletedForUser($rewardTask, $userId)) {
                    $allTasksCompleted = false;
                    break;
                }
            }

            // Award the reward if all tasks are completed
            if ($allTasksCompleted) {
                return $this->awardRewardToUser($reward, $userId, $readingLogId, $userActivityId);
            }

        } catch (\Exception $e) {
            Log::error('Error checking single reward', [
                'reward_id' => $reward->id,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Check if a repeatable reward was already awarded to a user in the current cycle.
     *
     * This method determines the cycle type from the reward's tasks and checks if the
     * reward was already awarded within that cycle period (daily, weekly, monthly).
     *
     * @param Reward $reward The reward to check
     * @param int $userId The user ID to check for
     * @return bool True if already awarded in current cycle, false otherwise
     */
    protected function isRepeatableRewardAlreadyAwardedInCurrentCycle(Reward $reward, int $userId): bool
    {
        try {
            // Get the most restrictive cycle from reward tasks
            $taskCycle = $this->getMostRestrictiveCycleForReward($reward);

            if ($taskCycle === EnumTaskCycle::TOTAL) {
                // For TOTAL cycle, repeatable rewards can be awarded multiple times
                return false;
            }

            // Get date range for the cycle
            $dateRange = $this->getDateRangeForCycle($taskCycle);
            if (!$dateRange) {
                return false; // No date filtering for TOTAL cycle
            }

            // Check if reward was already awarded in this cycle
            $existingAward = UserReward::where('user_id', $userId)
                ->where('reward_id', $reward->id)
                ->whereBetween('awarded_date', [
                    $dateRange['start'],
                    $dateRange['end']
                ])
                ->exists();

            return $existingAward;

        } catch (\Exception $e) {
            Log::error('Error checking repeatable reward cycle', [
                'reward_id' => $reward->id,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            return false; // On error, allow awarding to avoid blocking legitimate rewards
        }
    }

    /**
     * Get the most restrictive cycle type from a reward's tasks.
     *
     * @param Reward $reward The reward to check
     * @return int The most restrictive cycle (DAILY > WEEKLY > MONTHLY > TOTAL)
     */
    protected function getMostRestrictiveCycleForReward(Reward $reward): int
    {
        $cycles = $reward->rewardTasks()
            ->with('task.taskCycle')
            ->get()
            ->pluck('task.task_cycle_id')
            ->unique()
            ->toArray();

        // Return most restrictive cycle (lower number = more restrictive)
        if (in_array(EnumTaskCycle::DAILY, $cycles)) {
            return EnumTaskCycle::DAILY;
        }
        if (in_array(EnumTaskCycle::WEEKLY, $cycles)) {
            return EnumTaskCycle::WEEKLY;
        }
        if (in_array(EnumTaskCycle::MONTHLY, $cycles)) {
            return EnumTaskCycle::MONTHLY;
        }

        return EnumTaskCycle::TOTAL;
    }

    /**
     * Check if a specific reward task is completed for a user.
     *
     * This method implements direct database queries for reward task calculations
     * that respect absolute calendar periods (not task assignment dates).
     *
     * @param RewardTask $rewardTask The reward task to check
     * @param int $userId The user ID to check for
     * @return bool True if the reward task is completed, false otherwise
     */
    protected function isRewardTaskCompletedForUser(RewardTask $rewardTask, int $userId): bool
    {
        $task = $rewardTask->task;
        if (!$task || !$task->taskType || !$task->taskCycle) {
            return false;
        }

        try {
            // Get current progress for this task type and cycle
            $currentProgress = $this->calculateRewardTaskProgress($task, $userId);

            // Task is completed if current progress >= target
            return $currentProgress >= $task->task_value;

        } catch (\Exception $e) {
            Log::error('Error checking reward task completion', [
                'reward_task_id' => $rewardTask->id,
                'task_id' => $task->id,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Calculate the current progress for a reward task based on task type and cycle.
     *
     * This method implements the exact specifications from the analysis document,
     * using absolute calendar periods for time-bounded cycles.
     *
     * @param Task $task The task to calculate progress for
     * @param int $userId The user ID to calculate progress for
     * @return int The current progress value
     */
    protected function calculateRewardTaskProgress(Task $task, int $userId): int
    {
        $taskType = $task->taskType->nr ?? 0;
        $taskCycle = $task->taskCycle->nr ?? 0;

        // Get date range for the cycle
        $dateRange = $this->getDateRangeForCycle($taskCycle);

        // Get category IDs for filtering (more efficient than collecting book IDs)
        $categoryIds = null;
        if ($task->categories && $task->categories->isNotEmpty()) {
            $categoryIds = $task->categories->pluck('id')->toArray();
        }

        switch ($taskType) {
            case EnumTaskType::READ_PAGES:
                return $this->calculateReadPagesProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::READ_BOOKS:
                return $this->calculateReadBooksProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::READ_MINUTES:
                return $this->calculateReadMinutesProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::READ_DAYS:
                return $this->calculateReadDaysProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::READ_STREAK:
                return $this->calculateReadStreakProgress($userId, $taskCycle, $categoryIds);

            case EnumTaskType::EARN_READING_POINTS:
                return $this->calculateReadingPointsProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::EARN_ACTIVITY_POINTS:
                return $this->calculateActivityPointsProgress($userId, $dateRange, $categoryIds);

            case EnumTaskType::COMPLETE_BOOK_ACTIVITY:
                return $this->calculateBookActivityProgress($userId, $dateRange, $task, $categoryIds);

            case EnumTaskType::COMPLETE_BOOK_LIST:
                return $this->calculateBookListProgress($userId, $dateRange, $categoryIds);

            default:
                Log::warning('Unknown task type for reward calculation', [
                    'task_type' => $taskType,
                    'task_id' => $task->id,
                ]);
                return 0;
        }
    }

    /**
     * Get date range for a task cycle.
     *
     * @param int $taskCycle The task cycle (TOTAL, DAILY, WEEKLY, MONTHLY)
     * @return array|null Array with 'start' and 'end' dates, or null for TOTAL
     */
    protected function getDateRangeForCycle(int $taskCycle): ?array
    {
        $now = Carbon::now();

        switch ($taskCycle) {
            case EnumTaskCycle::DAILY: 
                return [
                    'start' => $now->copy()->startOfDay(),
                    'end' => $now->copy()->endOfDay()
                ];

            case EnumTaskCycle::WEEKLY: 
                return [
                    'start' => $now->copy()->startOfWeek(),
                    'end' => $now->copy()->endOfWeek()
                ];

            case EnumTaskCycle::MONTHLY: 
                return [
                    'start' => $now->copy()->startOfMonth(),
                    'end' => $now->copy()->endOfMonth()
                ];

            case EnumTaskCycle::TOTAL: 
            default:
                return null; // No date filtering for TOTAL
        }
    }

    /**
     * Calculate READ_PAGES progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Total pages read
     */
    protected function calculateReadPagesProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserReadingLog::where('user_id', $userId);

        // Apply date filtering
        if ($dateRange) {
            $query->whereBetween('log_date', [
                $dateRange['start']->startOfDay(),
                $dateRange['end']->endOfDay()
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->sum('pages_read');
    }

    /**
     * Calculate READ_BOOKS progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Number of books completed
     */
    protected function calculateReadBooksProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserBook::where('user_id', $userId)
            ->whereNotNull('end_date');

        // Apply date filtering based on completion date
        if ($dateRange) {
            $query->whereBetween('end_date', [
                $dateRange['start']->startOfDay(),
                $dateRange['end']->endOfDay()
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->distinct('book_id')->count();
    }

    /**
     * Calculate READ_MINUTES progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Total minutes read
     */
    protected function calculateReadMinutesProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserReadingLog::where('user_id', $userId);

        // Apply date filtering
        if ($dateRange) {
            $query->whereBetween('log_date', [
                $dateRange['start']->startOfDay(),
                $dateRange['end']->endOfDay()
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->sum('reading_duration');
    }

    /**
     * Calculate READ_DAYS progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Number of unique days with reading activity
     */
    protected function calculateReadDaysProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserReadingLog::where('user_id', $userId);

        // Apply date filtering
        if ($dateRange) {
            $query->whereBetween('log_date', [
                $dateRange['start']->startOfDay(),
                $dateRange['end']->endOfDay()
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        // CRITICAL FIX: Count distinct calendar dates, not timestamps
        // Use DATE() function to extract date part from timestamp
        return (int) $query->selectRaw('DATE(log_date) as date_only')
            ->distinct()
            ->count('date_only');
    }

    /**
     * Calculate READ_STREAK progress for a user.
     *
     * @param int $userId User ID
     * @param int $taskCycle Task cycle for streak calculation
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Current reading streak
     */
    protected function calculateReadStreakProgress(int $userId, int $taskCycle, ?array $categoryIds): int
    {
        $now = Carbon::now();
        $streak = 0;
        $currentDate = $now->copy();

        // For TOTAL cycle, calculate from today backward
        // For other cycles, limit to the cycle period
        $maxDays = match($taskCycle) {
            EnumTaskCycle::DAILY => 1,      // DAILY
            EnumTaskCycle::WEEKLY => 7,      // WEEKLY
            EnumTaskCycle::MONTHLY => $now->daysInMonth, // MONTHLY
            default => 365 // Reasonable limit for TOTAL
        };

        // Calculate the start date for the streak
        $startDate = $currentDate->copy()->subDays($maxDays - 1)->startOfDay();

        // Prepare the base query
        $query = UserReadingLog::where('user_id', $userId)
            ->whereBetween('log_date', [$startDate, $currentDate->endOfDay()]);

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        // Fetch all logs within the date range in one query
        $logs = $query->get(['log_date']);

        // Create an array to track the days we have logs for
        $logDates = $logs->pluck('log_date')->map(function ($date) {
            return Carbon::parse($date)->toDateString();
        })->toArray();

        // Iterate backward and check for streak
        for ($i = 0; $i < $maxDays; $i++) {
            if (in_array($currentDate->toDateString(), $logDates)) {
                $streak++;
                $currentDate->subDay();
            } else {
                break; // Streak broken
            }
        }

        return (int) $streak;
    }

    /**
     * Calculate EARN_READING_POINTS progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Total reading points earned
     */
    protected function calculateReadingPointsProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserPoint::where('user_id', $userId)
            ->where('point_type', UserPoint::POINT_TYPE_PAGE);

        // Apply date filtering
        if ($dateRange) {
            $query->whereBetween('point_date', [
                $dateRange['start'],
                $dateRange['end']
            ]);
        }

        // For reading points, filter by book categories using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->sum('points');
    }

    /**
     * Calculate EARN_ACTIVITY_POINTS progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Total activity points earned
     */
    protected function calculateActivityPointsProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserPoint::where('user_id', $userId)
            ->where('point_type', UserPoint::POINT_TYPE_ACTIVITY);

        // Apply date filtering
        if ($dateRange) {
            $query->whereBetween('point_date', [
                $dateRange['start'],
                $dateRange['end']
            ]);
        }

        // For activity points, filter by book categories using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->sum('points');
    }

    /**
     * Calculate COMPLETE_BOOK_ACTIVITY progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param Task $task The task with activity type information
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Number of completed activities
     */
    protected function calculateBookActivityProgress(int $userId, ?array $dateRange, Task $task, ?array $categoryIds): int
    {
        $query = UserActivity::where('user_id', $userId)
            ->where('status', UserActivity::STATUS_COMPLETED);

        // Filter by activity if specified in task
        if ($task->activity_id) {
            $query->where('activity_id', $task->activity_id);
        }

        // Apply date filtering based on completion date
        if ($dateRange) {
            $query->whereBetween('updated_at', [
                $dateRange['start'],
                $dateRange['end']
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->count();
    }

    /**
     * Calculate COMPLETE_BOOK_LIST progress for a user.
     *
     * @param int $userId User ID
     * @param array|null $dateRange Date range for filtering
     * @param array|null $categoryIds Category IDs for filtering
     * @return int Number of books completed from the categories
     */
    protected function calculateBookListProgress(int $userId, ?array $dateRange, ?array $categoryIds): int
    {
        $query = UserBook::where('user_id', $userId)
            ->whereNotNull('end_date');

        // Apply date filtering based on completion date
        if ($dateRange) {
            $query->whereBetween('end_date', [
                $dateRange['start']->startOfDay(),
                $dateRange['end']->endOfDay()
            ]);
        }

        // Apply book category filtering using direct relationship
        if ($categoryIds) {
            $query->whereHas('book.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        return (int) $query->count();
    }



    /**
     * Award a reward to a user with proper logging and error handling.
     *
     * @param Reward $reward The reward to award
     * @param int $userId The user ID to award to
     * @param int|null $readingLogId Optional reading log ID
     * @param int|null $userActivityId Optional user activity ID
     * @return UserReward|null The created UserReward instance, or null on failure
     */
    protected function awardRewardToUser(Reward $reward, int $userId, ?int $readingLogId = null, ?int $userActivityId = null): ?UserReward
    {
        try {
            $userReward = UserReward::create([
                'user_id' => $userId,
                'reward_id' => $reward->id,
                'awarded_date' => Carbon::now(),
                'reading_log_id' => $readingLogId,
                'user_activity_id' => $userActivityId,
            ]);

            Log::info('Reward awarded to user', [
                'user_reward_id' => $userReward->id,
                'user_id' => $userId,
                'reward_id' => $reward->id,
                'reward_name' => $reward->name,
            ]);

            return $userReward;

        } catch (\Exception $e) {
            Log::error('Error awarding reward to user', [
                'reward_id' => $reward->id,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check and award team rewards for a specific team.
     *
     * @param Team $team The team to check rewards for
     * @param int|null $readingLogId Optional reading log ID
     * @param int|null $userActivityId Optional user activity ID
     * @return array Array of TeamReward instances that were awarded
     */
    protected function checkAndAwardTeamRewardsForTeam(Team $team, ?int $readingLogId = null, ?int $userActivityId = null): array
    {
        $awardedRewards = [];

        try {
            // Get all active team rewards
            $eligibleRewards = $this->getEligibleTeamRewards($team->id);

            foreach ($eligibleRewards as $reward) {
                $teamReward = $this->checkAndAwardSingleTeamReward($reward, $team, $readingLogId, $userActivityId);
                if ($teamReward) {
                    $awardedRewards[] = $teamReward;
                }
            }

        } catch (\Exception $e) {
            Log::error('Error checking team rewards', [
                'team_id' => $team->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $awardedRewards;
    }

    /**
     * Get all team rewards that are eligible for a team to earn.
     *
     * @param int $teamId The team ID to get eligible rewards for
     * @return Collection Collection of Reward instances
     */
    protected function getEligibleTeamRewards(int $teamId): Collection
    {
        return Reward::where('active', true)
            // Note: Team rewards are identified by being awarded to teams, not by a specific type field
            ->whereHas('rewardTasks') // Only automatic rewards with tasks
            ->where(function ($query) use ($teamId) {
                // For non-repeatable rewards, exclude already earned ones
                $query->where('repeatable', true)
                    ->orWhereDoesntHave('teamRewards', function ($q) use ($teamId) {
                        $q->where('team_id', $teamId);
                    });
            })
            ->with(['rewardTasks.task.taskType', 'rewardTasks.task.taskCycle', 'rewardTasks.task.categories'])
            ->get();
    }

    /**
     * Check if a single team reward should be awarded and award it if eligible.
     *
     * @param Reward $reward The reward to check
     * @param Team $team The team to check for
     * @param int|null $readingLogId Optional reading log ID
     * @param int|null $userActivityId Optional user activity ID
     * @return TeamReward|null The awarded TeamReward instance, or null if not awarded
     */
    protected function checkAndAwardSingleTeamReward(Reward $reward, Team $team, ?int $readingLogId = null, ?int $userActivityId = null): ?TeamReward
    {
        try {
            // Get all reward tasks for this reward
            $rewardTasks = $reward->rewardTasks()->with(['task.taskType', 'task.taskCycle', 'task.categories'])->get();

            if ($rewardTasks->isEmpty()) {
                return null; // No tasks to check
            }

            // Get team member IDs
            $memberIds = $team->users()->pluck('users.id')->toArray();
            if (empty($memberIds)) {
                return null; // No team members
            }

            // Check if ALL reward tasks are completed by the team
            $allTasksCompleted = true;
            foreach ($rewardTasks as $rewardTask) {
                if (!$this->isTeamRewardTaskCompleted($rewardTask, $memberIds)) {
                    $allTasksCompleted = false;
                    break;
                }
            }

            // Award the reward if all tasks are completed
            if ($allTasksCompleted) {
                return $this->awardRewardToTeam($reward, $team, $readingLogId, $userActivityId);
            }

        } catch (\Exception $e) {
            Log::error('Error checking single team reward', [
                'reward_id' => $reward->id,
                'team_id' => $team->id,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Check if a team reward task is completed.
     *
     * @param RewardTask $rewardTask The reward task to check
     * @param array $memberIds Array of team member user IDs
     * @return bool True if the team has completed the task
     */
    protected function isTeamRewardTaskCompleted(RewardTask $rewardTask, array $memberIds): bool
    {
        $task = $rewardTask->task;
        if (!$task || !$task->taskType || !$task->taskCycle || empty($memberIds)) {
            return false;
        }

        try {
            // Calculate total team progress by summing all members' progress
            $totalProgress = 0;
            $taskType = $task->taskType->nr ?? null;
            $taskCycle = $task->taskCycle->nr ?? null;

            // Get date range for the cycle
            $dateRange = $this->getDateRangeForCycle($taskCycle);

            // Get category IDs for filtering (more efficient than collecting book IDs)
            $categoryIds = null;
            if ($task->categories && $task->categories->isNotEmpty()) {
                $categoryIds = $task->categories->pluck('id')->toArray();
            }

            // Sum progress from all team members
            foreach ($memberIds as $memberId) {
                switch ($taskType) {
                    case 1: // READ_PAGES
                        $totalProgress += $this->calculateReadPagesProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 2: // READ_BOOKS
                        $totalProgress += $this->calculateReadBooksProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 3: // READ_MINUTES
                        $totalProgress += $this->calculateReadMinutesProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 4: // READ_DAYS
                        $totalProgress += $this->calculateReadDaysProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 5: // READ_STREAK
                        // For streaks, take the maximum streak among team members
                        $memberStreak = $this->calculateReadStreakProgress($memberId, $taskCycle, $categoryIds);
                        $totalProgress = max($totalProgress, $memberStreak);
                        break;

                    case 6: // EARN_READING_POINTS
                        $totalProgress += $this->calculateReadingPointsProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 7: // EARN_ACTIVITY_POINTS
                        $totalProgress += $this->calculateActivityPointsProgress($memberId, $dateRange, $categoryIds);
                        break;

                    case 8: // COMPLETE_BOOK_ACTIVITY
                        $totalProgress += $this->calculateBookActivityProgress($memberId, $dateRange, $task, $categoryIds);
                        break;

                    case 9: // COMPLETE_BOOK_LIST
                        $totalProgress += $this->calculateBookListProgress($memberId, $dateRange, $categoryIds);
                        break;
                }
            }

            // Team task is completed if total progress >= target
            return $totalProgress >= $task->task_value;

        } catch (\Exception $e) {
            Log::error('Error checking team reward task completion', [
                'reward_task_id' => $rewardTask->id,
                'task_id' => $task->id,
                'member_ids' => $memberIds,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Award a reward to a team with proper logging and error handling.
     *
     * @param Reward $reward The reward to award
     * @param Team $team The team to award to
     * @param int|null $readingLogId Optional reading log ID
     * @param int|null $userActivityId Optional user activity ID
     * @return TeamReward|null The created TeamReward instance, or null on failure
     */
    protected function awardRewardToTeam(Reward $reward, Team $team, ?int $readingLogId = null, ?int $userActivityId = null): ?TeamReward
    {
        try {
            $teamReward = TeamReward::create([
                'team_id' => $team->id,
                'reward_id' => $reward->id,
                'awarded_date' => Carbon::now(),
                'reading_log_id' => $readingLogId,
                'user_activity_id' => $userActivityId,
            ]);

            Log::info('Reward awarded to team', [
                'team_reward_id' => $teamReward->id,
                'team_id' => $team->id,
                'reward_id' => $reward->id,
                'reward_name' => $reward->name,
            ]);

            return $teamReward;

        } catch (\Exception $e) {
            Log::error('Error awarding reward to team', [
                'reward_id' => $reward->id,
                'team_id' => $team->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }
}
