<?php

declare(strict_types=1);

namespace App\MoonShine\Pages;

use Carbon\Carbon;
use MoonShine\Contracts\UI\ComponentContract;
use MoonShine\Laravel\Pages\Page;
use MoonShine\UI\Components\Metrics\Wrapped\ValueMetric;

use App\Models\{Reward, User, UserActivity, UserActivityReview, UserReadingLog};
use MoonShine\Contracts\Core\TypeCasts\DataWrapperContract;
use MoonShine\UI\Components\{Layout\Box, Layout\Column, Layout\Grid, Table\TableBuilder, Heading};
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\{Fieldset, Image, Text};

#[\MoonShine\MenuManager\Attributes\SkipMenu]

class Dashboard extends Page
{
    /**
     * @return array<string, string>
     */
    public function getBreadcrumbs(): array
    {
        return [
            '#' => $this->getTitle()
        ];
    }

    public function getTitle(): string
    {
        return $this->title ?: __('admin.dashboard');
    }

    /**
     * @return list<ComponentContract>
     */
    protected function components(): iterable
    {
        $user = auth('moonshine')->user();

        if (!$user || !($user instanceof User)) {
            return [];
        }

        // Return role-specific dashboard components
        if ($user->isTeacher()) {
            return $this->getTeacherDashboardComponents($user);
        }

        if ($user->isSystemAdmin()) {
            return $this->getSystemAdminDashboardComponents($user);
        }

        if ($user->isSchoolAdmin()) {
            return $this->getSchoolAdminDashboardComponents($user);
        }

        if ($user->isParent()) {
            return $this->getParentDashboardComponents($user);
        }

        return [];
    }

    /**
     * Get dashboard components for teachers.
     */
    protected function getTeacherDashboardComponents(User $teacher): array
    {
        // get teacher default class id if exists, if not return empty 
        $teacherClassIds = [];
        $defaultClass = $teacher->getDefaultClass();
        if ($defaultClass) {
            $teacherClassIds = [$defaultClass->class_id];
        }
        
        // Disabled for now - Fetch teacher class IDs once to avoid redundant queries
        // $teacherClassIds = $teacher->activeUserClasses()->pluck('class_id')->toArray();

        // Pre-fetch all students with necessary relationships for ranking calculations
        $allStudents = $this->getTeacherStudentsWithRelationships($teacherClassIds);

        return [
            // Dashboard Metrics
            Grid::make([
                    ValueMetric::make(__('admin.activities_pending_approval'))
                        ->value(fn() => $this->getActivitiesPendingApprovalCount($teacherClassIds))
                        ->icon('clipboard-document-check')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.students_read_last_24h'))
                        ->value(fn() => $this->getStudentsReadLast24Hours($teacherClassIds))
                        ->icon('users')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.pages_read_last_24h'))
                        ->value(fn() => $this->getPagesReadLast24Hours($teacherClassIds))
                        ->icon('book-open')
                        ->columnSpan(3),
                    ValueMetric::make(__('admin.activities_done_last_24h'))
                        ->value(fn() => $this->getActivitiesDoneLast24Hours($teacherClassIds))
                        ->icon('pencil-square')
                        ->columnSpan(3),
            ]),

            // Statistical Tables
            Heading::make(__('admin.student_rankings'), 2)->class('mt-8 mb-4'),
            Grid::make([
                // Left Column - Top Rankings
                Column::make([
                    Box::make(__('admin.most_books_read'), [
                        $this->getTopStudentsByBooksReadOptimized($allStudents)
                    ]),

                    Box::make(__('admin.highest_level_students'), [
                        $this->getTopStudentsByLevelOptimized($allStudents)
                    ]),

                    Box::make(__('admin.most_badges'), [
                        $this->getTopStudentsByBadgesOptimized($allStudents)
                    ]),

                    Box::make(__('admin.longest_reading_streak'), [
                        $this->getTopStudentsByReadingStreakOptimized($allStudents)
                    ]),

                    Box::make(__('admin.highest_activity_score'), [
                        $this->getTopStudentsByActivityScoreOptimized($allStudents)
                    ]),
                ])->columnSpan(6)->class('mb-8'),

                // Right Column - Bottom Rankings & Gaps
                Column::make([
                    Box::make(__('admin.fewest_books_read'), [
                        $this->getBottomStudentsByBooksReadOptimized($allStudents)
                    ]),

                    Box::make(__('admin.lowest_level_students'), [
                        $this->getBottomStudentsByLevelOptimized($allStudents)
                    ]),

                    Box::make(__('admin.fewest_badges'), [
                        $this->getBottomStudentsByBadgesOptimized($allStudents)
                    ]),

                    Box::make(__('admin.longest_reading_gap'), [
                        $this->getStudentsWithLongestReadingGapOptimized($allStudents)
                    ]),

                    Box::make(__('admin.lowest_activity_score'), [
                        $this->getBottomStudentsByActivityScoreOptimized($allStudents)
                    ]),
                ])->columnSpan(6),
            ]),
        ];
    }

    // ========== DASHBOARD METRICS METHODS ==========

    /**
     * Get count of activities pending approval for teacher's students.
     */
    protected function getActivitiesPendingApprovalCount(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        return UserActivityReview::where('status', UserActivityReview::STATUS_WAITING)
            ->whereHas('userActivity', function ($q) use ($teacherClassIds) {
                $q->whereHas('user.activeUserClasses', function ($subQ) use ($teacherClassIds) {
                    $subQ->whereIn('class_id', $teacherClassIds);
                });
            })
            ->count();
    }

    /**
     * Get students who read in last 24 hours / total students ratio.
     */
    protected function getStudentsReadLast24Hours(array $teacherClassIds): string
    {
        if (empty($teacherClassIds)) {
            return '0 / 0';
        }

        // Get total students in teacher's classes
        $totalStudents = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->count();

        // Get students who read in last 24 hours
        $studentsReadLast24h = User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->whereHas('readingLogs', function ($q) {
                $q->where('log_date', '>=', Carbon::now()->subDay());
            })
            ->count();

        return "{$studentsReadLast24h} / {$totalStudents}";
    }

    /**
     * Get total pages read in last 24 hours by teacher's students.
     */
    protected function getPagesReadLast24Hours(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        $pagesReadLast24h = UserReadingLog::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('log_date', '>=', Carbon::now()->subDay())
            ->sum('pages_read') ?? 0;

        return (int)$pagesReadLast24h;
    }

    /**
     * Get activities done in last 24 hours by teacher's students (including pending, excluding rejected).
     */
    protected function getActivitiesDoneLast24Hours(array $teacherClassIds): int
    {
        if (empty($teacherClassIds)) {
            return 0;
        }

        return UserActivity::whereHas('user.activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->where('activity_date', '>=', Carbon::now()->subDay())
            ->whereIn('status', [
                UserActivity::STATUS_PENDING,
                UserActivity::STATUS_APPROVED,
                UserActivity::STATUS_COMPLETED
            ])
            ->count();
    }

    // ========== STUDENT RANKING METHODS ==========

    /**
     * Get students filtered by teacher's classes.
     */
    protected function getTeacherStudents(array $teacherClassIds)
    {
        if (empty($teacherClassIds)) {
            return User::where('id', 0); // Return empty query
        }

        return User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            });
    }

    /**
     * Get all teacher's students with necessary relationships loaded for ranking calculations.
     * This reduces database queries by eager loading all needed data at once.
     */
    protected function getTeacherStudentsWithRelationships(array $teacherClassIds)
    {
        if (empty($teacherClassIds)) {
            return collect(); // Return empty collection
        }

        return User::withRole('student')
            ->whereHas('activeUserClasses', function ($q) use ($teacherClassIds) {
                $q->whereIn('class_id', $teacherClassIds);
            })
            ->withCount([
                'userBooks as books_completed' => function ($q) {
                    $q->whereNotNull('end_date');
                },
                'userRewards as badges_count' => function ($q) {
                    $q->whereHas('reward', function ($subQ) {
                        $subQ->where('reward_type', Reward::TYPE_BADGE);
                    });
                }
            ])
            ->get()
            ->map(function ($student) {
                // Calculate complex metrics that require method calls
                $student->current_level_number = $student->getCurrentLevel() ? $student->getCurrentLevel()->nr : 0;
                $student->reading_streak = $student->getCurrentReadingStreak();
                $student->activity_points = $student->getTotalActivityPoints();
                $student->days_since_reading = $student->getDaysSinceLastReading();
                return $student;
            });
    }

    // ========== OPTIMIZED RANKING METHODS (Using Pre-fetched Data) ==========

    // get ranking tablebuilder 

    protected function getStudentRankingsTable($students, $metric, $label, $badgeColor): TableBuilder
    {
        return TableBuilder::make()
            ->fields([
                Fieldset::make(__('admin.student_name'), [
                    Flex::make([
                        Image::make(__('admin.avatar'), 'avatar_display_image')
                        ->class('w-12 h-12 rounded-full'),
                        Text::make(__('admin.student_name'), 'name'),
                    ])->justifyAlign('start'),
                ]),
                Text::make(__($label), $metric)->badge($badgeColor),
            ])
            ->trAttributes(fn(?DataWrapperContract $data, int $row): array => ['class' => $row % 2 ? 'bg-gray-100' : ''])        
            ->tdAttributes(fn(?DataWrapperContract $data, int $row, int $cell): array => ['class' => 'py-1'])        
            ->items($students);
    }


    /**
     * Top 5 students who read the most books (optimized version).
     */
    protected function getTopStudentsByBooksReadOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('books_completed')->take(5);
        
        return $this->getStudentRankingsTable($students, 'books_completed', 'admin.books_count', 'primary');
    }

    /**
     * Bottom 5 students who read the fewest books (optimized version).
     */
    protected function getBottomStudentsByBooksReadOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortBy('books_completed')->take(5);

        return $this->getStudentRankingsTable($students, 'books_completed', 'admin.books_count', 'secondary');
    }

    /**
     * Top 5 highest-level students (optimized version).
     */
    protected function getTopStudentsByLevelOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('current_level_number')->take(5);

        return $this->getStudentRankingsTable($students, 'current_level_number', 'admin.level', 'success');
    }

    /**
     * Top 5 students with the most badges (optimized version).
     */
    protected function getTopStudentsByBadgesOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('badges_count')->take(5);

        return $this->getStudentRankingsTable($students, 'badges_count', 'admin.badges_count', 'warning');
    }

    /**
     * Top 5 students with the longest reading streak (optimized version).
     */
    protected function getTopStudentsByReadingStreakOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('reading_streak')->take(5);

        return $this->getStudentRankingsTable($students, 'reading_streak', 'admin.streak_days', 'info');
    }

    /**
     * Top 5 students with the highest activity score (optimized version).
     */
    protected function getTopStudentsByActivityScoreOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('activity_points')->take(5);

        return $this->getStudentRankingsTable($students, 'activity_points', 'admin.activity_points', 'primary');
    }

    /**
     * Bottom 5 students with the lowest activity score (optimized version).
     */
    protected function getBottomStudentsByActivityScoreOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortBy('activity_points')->take(5);

        return $this->getStudentRankingsTable($students, 'activity_points', 'admin.activity_points', 'secondary');
    }

    /**
     * Students with the longest reading gap (optimized version).
     */
    protected function getStudentsWithLongestReadingGapOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortByDesc('days_since_reading')->take(5);

        return $this->getStudentRankingsTable($students, 'days_since_reading', 'admin.days_since_reading', 'danger');
    }

    /**
     * Bottom 5 lowest-level students (optimized version).
     */
    protected function getBottomStudentsByLevelOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortBy('current_level_number')->take(5);

        return $this->getStudentRankingsTable($students, 'current_level_number', 'admin.level', 'secondary');
    }

    /**
     * Bottom 5 students with the fewest badges (optimized version).
     */
    protected function getBottomStudentsByBadgesOptimized($allStudents): TableBuilder
    {
        $students = $allStudents->sortBy('badges_count')->take(5);

        return $this->getStudentRankingsTable($students, 'badges_count', 'admin.badges_count', 'secondary');
    }

    /**
     * Get dashboard components for system admins.
     */
    protected function getSystemAdminDashboardComponents(User $admin): array
    {
        return [
            Box::make(__('admin.system_admin_dashboard'), [
                Heading::make(__('admin.system_overview'))
                    ->h(2)
                    ->class('mb-4'),
                // Add system admin specific components here
            ])
        ];
    }

    /**
     * Get dashboard components for school admins.
     */
    protected function getSchoolAdminDashboardComponents(User $admin): array
    {
        return [
            Box::make(__('admin.school_admin_dashboard'), [
                Heading::make(__('admin.school_overview'))
                    ->h(2)
                    ->class('mb-4'),
                // Add school admin specific components here
            ])
        ];
    }

    /**
     * Get dashboard components for parents.
     */
    protected function getParentDashboardComponents(User $parent): array
    {
        return [
            Box::make(__('admin.parent_dashboard'), [
                Heading::make(__('admin.students'))
                    ->h(2)
                    ->class('mb-4'),
                // Add parent specific components here
            ])
        ];
    }    
}
