<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Laravel\QueryTags\QueryTag;
use Sweet1s\MoonshineRBAC\Traits\WithRolePermissions;

use App\Models\{Reward, Task, Team, User};
use MoonShine\Contracts\UI\{ActionButtonContract, ComponentContract};
use MoonShine\Laravel\Fields\Relationships\{BelongsTo, BelongsToMany, HasMany};
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Laravel\MoonShineRequest;
use MoonShine\Support\{Attributes\Icon, Enums\JsEvent, AlpineJs, ListOf};
use MoonShine\Support\Enums\HttpMethod;
use MoonShine\Support\Enums\ToastType;
use MoonShine\UI\Components\{ActionButton, Layout\Box, CardsBuilder, FormBuilder, Modal};
use MoonShine\UI\Fields\{Date, Image, Select, Switcher, Text, Textarea};

#[Icon('star')]
class PanelRewardResource extends RewardResource
{
    use WithRolePermissions;

    // add query tags for user created rewards and system rewards
    public function queryTags(): array
    {
        return [
            QueryTag::make(
                __('admin.my_rewards'), 
                static fn(Builder $query) => $query->userRewards()
            )                
            ->default(),
            QueryTag::make(
                __('admin.system_rewards'),
                static fn(Builder $query) => $query->systemRewards()
            ),
        ];        
    }

    protected function indexFields(): iterable
    {
        return [];
    }

    protected function formFields(): iterable
    {
        return [
            Box::make(__('admin.basic_info'), [
                Text::make(__('admin.reward_name'), 'name')
                    ->required()
                    ->placeholder(__('admin.enter_reward_name')),

                Select::make(__('admin.reward_type'), 'reward_type')
                    ->options(Reward::getVisibleRewardTypes())
                    ->required()
                    ->default(Reward::TYPE_BADGE),

                Textarea::make(__('admin.reward_description'), 'description')
                    ->placeholder(__('admin.enter_reward_description')),

                Image::make(__('admin.reward_image'), 'image')
                    ->dir('rewards')
                    ->removable()
                    ->hint(__('admin.reward_image_hint')),

                Switcher::make(__('admin.active'), 'active')
                    ->default(true),

                Switcher::make(__('admin.repeatable'), 'repeatable')
                    ->default(false)
                    ->hint(__('admin.repeatable_reward_hint')),
            ]),

            Box::make(__('admin.reward_tasks'), [
                BelongsToMany::make(
                    __('admin.associated_tasks'),
                    'tasks',
                    formatted: fn(Task $task) => $task->name . ' (' . $task->taskType->name . ')',
                    resource: PanelTaskResource::class
                )
                    ->valuesQuery(function (Builder $query) { return $query->createdBy(auth('moonshine')->user()->id); })
                    ->selectMode()
                    ->creatable()
                    ->hint(__('admin.select_tasks_for_reward')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        // check if reward has tasks
        $tasks = $this->item->tasks->isNotEmpty() ? [
            HasMany::make(__('admin.associated_tasks'), 'tasks', PanelTaskResource::class)
            ->searchable(false),
        ] : [];

        return [
            Image::make(__('admin.reward_image'), 'image'),
            Text::make(__('admin.reward_name'), 'name'),
            Text::make(__('admin.reward_type_display'), 'reward_type_display'),
            Text::make(__('admin.awarding_type'), 'awarding_type'),
            Text::make(__('admin.reward_description'), 'description'),
            Switcher::make(__('admin.active'), 'active'),            
            Switcher::make(__('admin.repeatable'), 'repeatable'),
            ...$tasks,
            HasMany::make(__('admin.user_rewards'), 'userRewards', PanelUserRewardResource::class)
            ->creatable()
            ->fields([
                BelongsTo::make(
                    __('admin.student'),
                    'user',
                    formatted: fn(User $user) => $user->name,
                    resource: StudentResource::class
                ),
                Date::make(__('admin.awarded_date'), 'awarded_date')
                    ->format('d.m.Y'),
                Text::make(__('admin.trigger_source'), 'trigger_source'),                    
            ]),
            HasMany::make(__('admin.team_rewards'), 'teamRewards', PanelTeamRewardResource::class)
            ->creatable()
            ->fields([
                BelongsTo::make(
                    __('admin.team'),
                    'team',
                    formatted: fn(Team $team) => $team->name,
                    resource: StudentTeamResource::class
                ),  
                Date::make(__('admin.awarded_date'), 'awarded_date')
                    ->format('d.m.Y'),
                Text::make(__('admin.trigger_source'), 'trigger_source'),                    
            ]),
        ];
    }

    public function getListEventName(?string $name = null, array $params = []): string
    {
        $name ??= $this->getListComponentName();

        return AlpineJs::event(JsEvent::CARDS_UPDATED, $name, $params);
    }

    public function modifyListComponent(ComponentContract $component): ComponentContract
    {
        return CardsBuilder::make($this->getItems(), []) 
            ->cast($this->getCaster())
            ->name($this->getListComponentName())
            ->async()
            ->columnSpan(2, 6)
            ->title( fn($reward) => html_entity_decode($reward->name))
            ->subtitle(fn($reward) => $reward->reward_type_display . ' (' . $reward->awarding_type . ')')
            ->url(fn($reward) => $this->getDetailPageUrl($reward->getKey()))
            ->thumbnail(fn($reward) => asset('storage/' . $reward->image));
    }    

    protected function detailButtons(): ListOf
    {        
        // system reward : no buttons 
        // has tasks : detail buttons
        // no tasks : detail buttons + award to class, team, students

        $buttons = new ListOf(ActionButtonContract::class, []);  
        if(!$this->item->is_system_reward) {
            $buttons = parent::detailButtons();
        }      
        
        // if reward is not system reward and is active and has no tasks, add award buttons
        if(!$this->item->is_system_reward && $this->item->tasks->isEmpty() && $this->item->active) {            
            $buttons->prepend(
                ActionButton::make(__('admin.award_to_class'))
                    ->method('awardToClass') 
                    ->success()
                    ->withConfirm(__('admin.confirm_award_to_class'),
                        name: 'class-modal',
                    )
                    ->icon('user-group'),

                ActionButton::make(__('admin.award_to_team'))
                    ->method('awardToTeam')
                    ->withConfirm(__('admin.confirm_award_to_team'),
                        fields: [
                            Select::make(__('admin.team'), 'team_id')
                                ->options(Team::createdBy(auth('moonshine')->id())->pluck('name', 'id')->toArray())
                                ->required(),
                        ],
                        name: 'team-modal',
                    )
                    ->success()
                    ->icon('users'),

                ActionButton::make(__('admin.award_to_students'))
                    ->method('awardToStudents')
                    ->withConfirm (__('admin.confirm_award_to_students'),
                        fields: [
                            Select::make(__('admin.students'), 'student_ids')
                                ->options(User::forCurrentUser()->role('student')->pluck('name', 'id')->toArray())
                                ->required()
                                ->multiple(),
                        ],
                        name: 'students-modal',
                    )
                    ->success()
                    ->icon('user'),
            );
        }

        return $buttons;
    }

    // method to award reward to class
    public function awardToClass(MoonShineRequest $request): MoonShineJsonResponse
    {
        $id = $request->get('resourceItem');
        if (!$id) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }        
        // if reward is not active, return error
        $reward = Reward::find($id);
        if (!$reward || !$reward->active) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_not_found'), ToastType::ERROR);
        }

        // get all students in user default class
        $user = auth('moonshine')->user();
        $defaultClass = $user->getDefaultClass();
        if (!$defaultClass) {
            return MoonShineJsonResponse::make()->toast(__('admin.default_class_not_found'), ToastType::ERROR);
        }
        $students = $defaultClass->schoolClass->users()->role('student')->get();
        // award reward to each student
        foreach ($students as $student) {
            $student->userRewards()->create([
                'reward_id' => $id,
                'awarded_by' => $user->id,
            ]);
        }
        return MoonShineJsonResponse::make()
            ->toast(__('admin.reward_awarded_to_class'), ToastType::SUCCESS)
            ->redirect($this->getDetailPageUrl($id));
    }

    public function awardToTeam(MoonShineRequest $request): MoonShineJsonResponse
    {
        $teamId = (int) $request->get('team_id');
        $rewardId = (int) $request->get('resourceItem');
        if (!$teamId || !$rewardId) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }
        $reward = Reward::find($rewardId);
        if (!$reward) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_not_found'), ToastType::ERROR);
        }
        if (!$reward->active) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_not_active'), ToastType::ERROR);
        }

        // award reward to all members of the team and then to the team
        $team = Team::find($teamId);
        if (!$team) {
            return MoonShineJsonResponse::make()->toast(__('admin.team_not_found'), ToastType::ERROR);
        }
        foreach ($team->users as $user) {
            $userReward = $reward->awardToUser($user->id, auth('moonshine')->id());
            if (!$userReward) {
                return MoonShineJsonResponse::make()->toast(__('admin.reward_already_awarded'), ToastType::WARNING);
            }
        }

        $teamReward = $reward->awardToTeam($teamId, auth('moonshine')->id());
        if (!$teamReward) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_already_awarded'), ToastType::WARNING);
        }
        return MoonShineJsonResponse::make()
            ->toast(__('admin.reward_awarded_to_team'), ToastType::SUCCESS)
            ->redirect($this->getDetailPageUrl($rewardId));        
    }

    public function awardToStudents(MoonShineRequest $request): MoonShineJsonResponse
    {
        $studentIds = $request->get('student_ids');
        $rewardId = (int) $request->get('resourceItem');
        if (!$studentIds || !$rewardId) {
            return MoonShineJsonResponse::make()->toast(__('admin.invalid_request'), ToastType::ERROR);
        }
        $reward = Reward::find($rewardId);
        if (!$reward) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_not_found'), ToastType::ERROR);
        }
        if (!$reward->active) {
            return MoonShineJsonResponse::make()->toast(__('admin.reward_not_active'), ToastType::ERROR);
        }

        foreach ($studentIds as $studentId) {
            $user = User::find($studentId);
            if (!$user) {
                return MoonShineJsonResponse::make()->toast(__('admin.student_not_found'), ToastType::ERROR);
            }
            $userReward = $reward->awardToUser($user->id, auth('moonshine')->id());
            if (!$userReward) {
                return MoonShineJsonResponse::make()->toast(__('admin.reward_already_awarded'), ToastType::WARNING);
            }
        }
        return MoonShineJsonResponse::make()
            ->toast(__('admin.reward_awarded_to_students'), ToastType::SUCCESS)
            ->redirect($this->getDetailPageUrl($rewardId));        
    }


}
