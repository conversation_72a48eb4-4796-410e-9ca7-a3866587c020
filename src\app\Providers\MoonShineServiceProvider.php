<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use MoonShine\Contracts\Core\DependencyInjection\ConfiguratorContract;
use MoonShine\Contracts\Core\DependencyInjection\CoreContract;
use MoonShine\Laravel\DependencyInjection\MoonShine;
use MoonShine\Laravel\DependencyInjection\MoonShineConfigurator;
use App\MoonShine\Resources\UserResource;
use App\MoonShine\Resources\RoleResource;
use App\MoonShine\Resources\SchoolResource;
use App\MoonShine\Resources\SchoolClassResource;
use App\MoonShine\Resources\UserSchoolResource;
use App\MoonShine\Resources\UserClassResource;
use App\MoonShine\Resources\AuthorResource;
use App\MoonShine\Resources\PublisherResource;
use App\MoonShine\Resources\CategoryResource;
use App\MoonShine\Resources\BookResource;
use App\MoonShine\Resources\EnumClassLevelResource;
use App\MoonShine\Resources\BookQuestionResource;
use App\MoonShine\Resources\BookWordResource;
use App\MoonShine\Resources\AvatarResource;
use App\MoonShine\Resources\BookTypeResource;
use App\MoonShine\Resources\ClassBookResource;
use App\MoonShine\Resources\UserReadingLogResource;
use App\MoonShine\Resources\UserPointResource;
use App\MoonShine\Resources\ActivityCategoryResource;
use App\MoonShine\Resources\ActivityResource;
use App\MoonShine\Resources\ClassActivityResource;
use App\MoonShine\Resources\UserActivityResource;
use App\MoonShine\Resources\UserActivityReviewResource;
use App\MoonShine\Resources\UserBookResource;
use App\MoonShine\Resources\UserAvatarResource;
use App\MoonShine\Resources\TeamResource;
use App\MoonShine\Resources\UserTeamResource;
use App\MoonShine\Resources\RewardResource;
use App\MoonShine\Resources\UserRewardResource;
use App\MoonShine\Resources\TeamRewardResource;

use App\MoonShine\Resources\UserAgreementResource;
use Sweet1s\MoonshineRBAC\Resource\PermissionResource;
use App\MoonShine\Resources\EnumSchoolTypeResource;
use App\MoonShine\Resources\PagePointResource;
use App\MoonShine\Resources\TaskResource;
use App\MoonShine\Resources\EnumTaskTypeResource;
use App\MoonShine\Resources\EnumTaskCycleResource;

use App\MoonShine\Resources\UserTaskResource;
use App\MoonShine\Resources\ChallengeResource;
use App\MoonShine\Resources\ChallengeTaskResource;
use App\MoonShine\Resources\PanelBookResource;
use App\MoonShine\Resources\PanelBookWordResource;
use App\MoonShine\Resources\PanelClassBookResource;
use App\MoonShine\Resources\PanelRewardResource;
use App\MoonShine\Resources\PanelTaskResource;
use App\MoonShine\Resources\PanelTeamRewardResource;
use App\MoonShine\Resources\PanelUserBookResource;
use App\MoonShine\Resources\PanelUserRewardResource;
use App\MoonShine\Resources\PanelUserTaskResource;
use App\MoonShine\Resources\LevelResource;
use App\MoonShine\Resources\PanelClassActivityResource;
use App\MoonShine\Resources\PanelUserActivityResource;
use App\MoonShine\Resources\UserLevelResource;
use App\MoonShine\Resources\RewardTaskResource;

use App\MoonShine\Resources\StudentResource;

use App\MoonShine\Resources\StudentTeamResource;
use App\MoonShine\Resources\StudentTeamRewardResource;

use App\MoonShine\Resources\MessageResource;
use App\MoonShine\Resources\MessageRecipientResource;

class MoonShineServiceProvider extends ServiceProvider
{
    /**
     * @param  MoonShine  $core
     * @param  MoonShineConfigurator  $config
     *
     */
    public function boot(CoreContract $core, ConfiguratorContract $config): void
    {
        // $config->authEnable();

        // set config filesystem public disk url based on request scheme with host 
        config(['filesystems.disks.public.url' => request()->getSchemeAndHttpHost().'/storage']);
        

        $core
            ->resources([
                // System Resources
                RoleResource::class,
                UserResource::class,
                PermissionResource::class,
                UserAgreementResource::class,

                // Academic Resources
                EnumSchoolTypeResource::class,
                EnumClassLevelResource::class,
                SchoolResource::class,
                SchoolClassResource::class,
                UserSchoolResource::class,
                UserClassResource::class,

                // Avatar Resources
                AvatarResource::class,

                // Book Resources
                BookTypeResource::class,
                AuthorResource::class,
                CategoryResource::class,
                PublisherResource::class,
                BookResource::class,
                BookQuestionResource::class,
                BookWordResource::class,
                PagePointResource::class,
                ClassBookResource::class,
                UserReadingLogResource::class,
                UserPointResource::class,
                ActivityCategoryResource::class,
                ActivityResource::class,
                ClassActivityResource::class,
                UserActivityResource::class,
                UserActivityReviewResource::class,
                UserBookResource::class,
                UserAvatarResource::class,

                // Team Resources
                TeamResource::class,
                UserTeamResource::class,

                // Task Management Resources
                TaskResource::class,
                EnumTaskTypeResource::class,
                EnumTaskCycleResource::class,



                // Challenge Management Resources
                ChallengeResource::class,
                ChallengeTaskResource::class,

                // Unified Task Management Resource
                UserTaskResource::class,

                // Reward System Resources
                RewardResource::class,
                RewardTaskResource::class,
                UserRewardResource::class,
                TeamRewardResource::class,

                // Level System Resources
                LevelResource::class,
                UserLevelResource::class,

                // Message System Resources
                MessageResource::class,
                MessageRecipientResource::class,

                StudentResource::class,
                StudentTeamResource::class,

                StudentTeamRewardResource::class,
                PanelBookResource::class,
                PanelBookWordResource::class,
                PanelClassBookResource::class,
                PanelUserBookResource::class,
                PanelTaskResource::class,
                PanelUserTaskResource::class,
                PanelRewardResource::class,
                PanelTeamRewardResource::class,
                PanelUserRewardResource::class,
                PanelUserActivityResource::class,
                PanelClassActivityResource::class,

//                BookActivityTypeResource::class,
/*
                // Gamification Resources
                StoryResource::class,
                StoryRuleResource::class,
                StoryRuleDetailResource::class,
                StoryChapterResource::class,
                StoryCharacterResource::class,
                StoryCharacterStageResource::class,
                StoryAchievementResource::class,
                StoryBookResource::class,

                // Reading Program Resources
                ProgramResource::class,
                ProgramSchoolResource::class,
                ProgramClassResource::class,
                ProgramBookResource::class,
                ProgramTeamResource::class,
                ProgramTeamMemberResource::class,
                ProgramUserLevelResource::class,
                ProgramUserAchievementResource::class,
                ProgramUserCharacterResource::class,
                ProgramUserMapResource::class,
                ProgramUserPointResource::class,
                ProgramUserBookResource::class,

                // Task Management Resources
                ProgramTaskResource::class,
                ProgramTaskInstanceResource::class,
                ProgramTaskActionResource::class,

                // Assessment System Resources
                ProgramBookQuizResource::class,
                ProgramBookActivityResource::class,

                // Reading Log System Resources
                ProgramReadingLogResource::class,
*/                
            ])
            ->pages([
                ...$config->getPages(),
            ])
        ;
    }
}
