<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Avatar;
use App\Models\UserAvatar;

class MobileController extends Controller
{
    /**
     * Show the mobile splash screen.
     */
    public function splash()
    {
        return view('mobile.splash');
    }

    /**
     * Show the mobile login screen.
     */
    public function showLogin()
    {
        // If already authenticated, redirect to appropriate screen
        if (Auth::check()) {
            return $this->redirectAfterLogin();
        }

        return view('mobile.login');
    }

    /**
     * Handle mobile login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $credentials = $request->only('username', 'password');
        $remember = $request->boolean('remember', true); // Default to true as per requirements

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            // Store username in session for dropdown
            $this->storeUsernameForDropdown($request->username);

            return response()->json([
                'success' => true,
                'redirect' => $this->getRedirectUrl()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 422);
    }

    /**
     * Handle mobile logout.
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('mobile.login');
    }

    /**
     * Show avatar selection screen.
     */
    public function avatarSelection()
    {
        $user = Auth::user();
        
        // Check if user already has an avatar
        if (UserAvatar::where('user_id', $user->id)->exists()) {
            return redirect()->route('mobile.home');
        }

        // Get available avatars (required_points = 0)
        $avatars = Avatar::where('required_points', 0)
                        ->where('active', true)
                        ->get();

        return view('mobile.avatar-selection', compact('avatars'));
    }

    /**
     * Show welcome celebration screen.
     */
    public function welcome()
    {
        return view('mobile.welcome');
    }

    /**
     * Show mobile home screen.
     */
    public function home()
    {
        return view('mobile.home');
    }

    /**
     * Get previously used usernames for dropdown.
     */
    public function getPreviousUsernames()
    {
        $usernames = session('mobile_usernames', []);
        return response()->json($usernames);
    }

    /**
     * Determine redirect URL after login.
     */
    public function getRedirectUrl(): string
    {
        $user = Auth::user();
        
        // Check if user has avatar
        if (!UserAvatar::where('user_id', $user->id)->exists()) {
            return route('mobile.avatar-selection');
        }

        return route('mobile.home');
    }

    /**
     * Redirect after login based on user state.
     */
    private function redirectAfterLogin()
    {
        return redirect($this->getRedirectUrl());
    }

    /**
     * Store username for future dropdown use.
     */
    private function storeUsernameForDropdown(string $username): void
    {
        $usernames = session('mobile_usernames', []);
        
        // Remove if already exists
        $usernames = array_filter($usernames, fn($u) => $u !== $username);
        
        // Add to beginning
        array_unshift($usernames, $username);
        
        // Keep only last 5
        $usernames = array_slice($usernames, 0, 5);
        
        session(['mobile_usernames' => $usernames]);
    }
}
