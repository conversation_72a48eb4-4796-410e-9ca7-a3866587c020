<?php

declare(strict_types=1);

namespace App\MoonShine\Resources;

use App\Models\ClassActivity;
use App\Models\SchoolClass;
use App\Models\Activity;
use Illuminate\Contracts\Database\Eloquent\Builder;
use MoonShine\Contracts\Core\DependencyInjection\FieldsContract;
use MoonShine\Laravel\Fields\Relationships\BelongsTo;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;
use MoonShine\Support\Attributes\Icon;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Fields\{Hidden, Number, Switcher, Select};

#[Icon('academic-cap')]
class ClassActivityResource extends BaseResource
{
    protected string $model = ClassActivity::class;

    protected string $column = 'id';

    protected array $with = ['schoolClass', 'activity'];

    public function getTitle(): string
    {
        return __('admin.class_activities');
    }

    protected function indexFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            ),
            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),
            Number::make(__('admin.points'), 'points')
                ->badge(fn($value) => $value ? 'green' : 'gray'),
            Switcher::make(__('admin.required'), 'required'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
        ];
    }

    protected function formFields(): iterable
    {       
        return [
            Box::make([
                Flex::make([
                    BelongsTo::make(
                        __('admin.class'),
                        'schoolClass',
                        formatted: fn(SchoolClass $class) => $class->name,
                        resource: SchoolClassResource::class
                    )
                        ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); })
                        ->required()
                        ->searchable(),
                    
                    BelongsTo::make(
                        __('admin.activity'),
                        'activity',
                        formatted: fn(Activity $activity) => $activity->name . ' (' . Activity::getActivityTypeOptions()[$activity->activity_type] . ')',
                        resource: ActivityResource::class
                    )
                        ->reactive(function (FieldsContract $fields, $value, $field, $values) {
                            // find activity type and show/hide fields accordingly
                            $activity = Activity::find($value);
                            $fields->findByColumn('question_count')->disabled(!$activity->isTestActivity());
                            $fields->findByColumn('min_grade')->disabled(!$activity->isTestActivity());
                            $fields->findByColumn('allowed_tries')->disabled(!$activity->isTestActivity());
                            $fields->findByColumn('min_word_count')->disabled($activity->activity_type !== Activity::ACTIVITY_TYPE_WRITING);
                            return $fields;
                        })
                        ->nullable()
                        ->required()
                        ->searchable(),
                ]),
            ]),

            Box::make(__('admin.test_settings'), [
                Flex::make([
                    Number::make(__('admin.question_count'), 'question_count')
                        ->disabled(!$this->item || !$this->item->activity->isTestActivity())
                        ->reactive()
                        ->min(1)
                        ->max(50)
                        ->nullable()
                        ->hint(__('admin.question_count_hint')),
                    
                    Number::make(__('admin.min_grade'), 'min_grade')
                        ->disabled(!$this->item || !$this->item->activity->isTestActivity())
                        ->reactive()
                        ->min(0)
                        ->max(100)
                        ->nullable()
                        ->hint(__('admin.min_grade_hint')),
                ]),
                
                Number::make(__('admin.allowed_tries'), 'allowed_tries')
                    ->disabled(!$this->item || !$this->item->activity->isTestActivity())
                    ->reactive()
                    ->min(1)
                    ->max(10)
                    ->nullable()
                    ->hint(__('admin.allowed_tries_hint')),
            ]),

            Box::make(__('admin.writing_settings'), [
                Number::make(__('admin.min_word_count'), 'min_word_count')
                    ->disabled(!$this->item || !$this->item->activity->activity_type === Activity::ACTIVITY_TYPE_WRITING)
                    ->reactive()
                    ->min(1)
                    ->nullable()
                    ->hint(__('admin.min_word_count_hint')),
            ]),

            Box::make(__('admin.general_settings'), [
                    Number::make(__('admin.points'), 'points')
                        ->min(0)
                        ->nullable()
                        ->hint(__('admin.activity_points_hint')),
                    
                    Switcher::make(__('admin.required'), 'required')
                        ->hint(__('admin.required_activity_hint')),
                    Switcher::make(__('admin.need_approval'), 'need_approval')
                        ->hint(__('admin.need_approval_hint')),
                    
                    Switcher::make(__('admin.active'), 'active')
                        ->default(true)
                        ->hint(__('admin.active_activity_hint')),
            ]),
        ];
    }

    protected function detailFields(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            ),
            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),
            Number::make(__('admin.question_count'), 'question_count'),
            Number::make(__('admin.min_grade'), 'min_grade'),
            Number::make(__('admin.allowed_tries'), 'allowed_tries'),
            Number::make(__('admin.min_word_count'), 'min_word_count'),
            Number::make(__('admin.points'), 'points'),
            Switcher::make(__('admin.required'), 'required'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
            ...parent::getCommonDetailFields(),
        ];
    }

    protected function filters(): iterable
    {
        return [
            BelongsTo::make(
                __('admin.class'),
                'schoolClass',
                formatted: fn(SchoolClass $class) => $class->name,
                resource: SchoolClassResource::class
            )
                ->valuesQuery(function (Builder $query) { return $query->forCurrentUser(); }),
            BelongsTo::make(
                __('admin.activity'),
                'activity',
                formatted: fn(Activity $activity) => $activity->name,
                resource: ActivityResource::class
            ),
            Switcher::make(__('admin.required'), 'required'),
            Switcher::make(__('admin.need_approval'), 'need_approval'),
            Switcher::make(__('admin.active'), 'active'),
        ];
    }

    public function rules(mixed $item): array
    {
        $rules = [
            'class_id' => ['required', 'exists:school_classes,id'],
            'activity_id' => ['required', 'exists:activities,id'],
            'question_count' => ['nullable', 'integer', 'min:1', 'max:50'],
            'min_grade' => ['nullable', 'integer', 'min:0', 'max:100'],
            'allowed_tries' => ['nullable', 'integer', 'min:1', 'max:10'],
            'min_word_count' => ['nullable', 'integer', 'min:1'],
            'points' => ['nullable', 'integer', 'min:0'],
            'required' => ['boolean'],
            'need_approval' => ['boolean'],
            'active' => ['boolean'],
        ];

        // Add unique constraint for create operations
        if (!$item) {
            $rules['class_id'][] = 'unique:class_activities,class_id,NULL,id,activity_id,' . request('activity_id');
        } else {
            $rules['class_id'][] = 'unique:class_activities,class_id,' . $item->id . ',id,activity_id,' . request('activity_id');
        }

        return array_merge($rules, parent::getCommonRules($item));
    }

    public function search(): array
    {
        return [
            'schoolClass.name',
            'activity.name',
        ];
    }
        protected function modifyQueryBuilder(Builder $builder): Builder
    {
        return $builder->whereHas('schoolClass', function ($q) {
            $q->forCurrentUser();
        });
    }
}
