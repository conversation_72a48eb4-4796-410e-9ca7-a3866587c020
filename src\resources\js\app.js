import './bootstrap';

// Initialize MobileApp object immediately to prevent undefined errors
window.MobileApp = window.MobileApp || {
    showConfetti: () => console.log('Confetti not ready yet'),
    checkCameraSupport: () => Promise.resolve(false),
    isInstalled: () => false,
    requestNotificationPermission: () => Promise.resolve(false)
};

// Alpine.js extensions for Livewire compatibility
import './alpine-extensions';

// ZXing for QR code scanning
import { BrowserMultiFormatReader } from '@zxing/library';
window.BrowserMultiFormatReader = BrowserMultiFormatReader;

// Canvas Confetti for celebrations
import confetti from 'canvas-confetti';
window.confetti = confetti;

// Ensure MobileApp is available immediately
window.MobileApp = window.MobileApp || {};

// Mobile App Utilities
Object.assign(window.MobileApp, {
    // Show confetti animation
    showConfetti() {

/*        
        confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
*/

        var duration = 15 * 1000;
        var animationEnd = Date.now() + duration;
        var defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

        function randomInRange(min, max) {
        return Math.random() * (max - min) + min;
        }

        var interval = setInterval(function() {
        var timeLeft = animationEnd - Date.now();

        if (timeLeft <= 0) {
            return clearInterval(interval);
        }

        var particleCount = 50 * (timeLeft / duration);
        confetti({ ...defaults, particleCount, origin: { x: randomInRange(0.5, 0.5), y: Math.random() - 0.2 },shapes: ['star'] });
        }, 250);

    },

    // Get time-based greeting
    getGreeting() {
        const hour = new Date().getHours();
        if (hour < 12) return 'Good morning';
        if (hour < 17) return 'Good afternoon';
        return 'Good evening';
    },

    // Format date for display
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
    },

    // Check if device supports camera (without requesting permissions)
    async checkCameraSupport() {
        try {
            // Check if MediaDevices API is available
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                return false;
            }

            // Check if we can enumerate devices (this doesn't require permissions)
            if (navigator.mediaDevices.enumerateDevices) {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const hasVideoInput = devices.some(device => device.kind === 'videoinput');
                    return hasVideoInput;
                } catch (error) {
                    console.log('Could not enumerate devices:', error);
                    // Fallback: assume camera is available if API exists
                    return true;
                }
            }

            // Fallback: assume camera is available if getUserMedia exists
            return true;
        } catch (error) {
            console.log('Camera support check failed:', error);
            return false;
        }
    },

    // Enhanced camera access for barcode scanning
    async startCamera(videoElement) {
        try {
            console.log('Requesting camera access...');

            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // Prefer back camera for scanning
                    width: { ideal: 1280, min: 640 },
                    height: { ideal: 720, min: 480 }
                }
            });

            console.log('Camera access granted, setting up video element...');

            // Set the stream to the video element
            videoElement.srcObject = stream;

            // Ensure video plays automatically
            videoElement.setAttribute('autoplay', '');
            videoElement.setAttribute('playsinline', '');
            videoElement.setAttribute('muted', '');

            // Wait for the video to be ready and start playing
            return new Promise((resolve, reject) => {
                videoElement.onloadedmetadata = () => {
                    console.log('Video metadata loaded, starting playback...');
                    videoElement.play()
                        .then(() => {
                            console.log('Video playback started successfully');
                            resolve(stream);
                        })
                        .catch((playError) => {
                            console.error('Video play failed:', playError);
                            // Clean up stream if play fails
                            stream.getTracks().forEach(track => track.stop());
                            reject(playError);
                        });
                };

                videoElement.onerror = (error) => {
                    console.error('Video element error:', error);
                    stream.getTracks().forEach(track => track.stop());
                    reject(error);
                };
            });

        } catch (error) {
            console.error('Error accessing camera:', error);
            throw error;
        }
    },

    // Stop camera stream
    stopCamera(stream) {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }
    },

    // Request notification permission
    async requestNotificationPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                // Initialize FCM if available
                this.initializeFCM();
            }
            return permission === 'granted';
        }
        return false;
    },

    // Initialize Firebase Cloud Messaging
    async initializeFCM() {
        try {
            // This would be implemented when Firebase config is added
            console.log('FCM initialization would happen here');

            // Example implementation:
            // const messaging = firebase.messaging();
            // const token = await messaging.getToken({ vapidKey: 'your-vapid-key' });
            // console.log('FCM Token:', token);
            // Send token to your server for storage
        } catch (error) {
            console.error('FCM initialization failed:', error);
        }
    },

    // Send local notification
    showLocalNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/icons/icon-192x192.png',
                badge: '/icons/icon-72x72.png',
                ...options
            });
        }
    },

    // Vibrate device (if supported)
    vibrate(pattern = [100, 50, 100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    },

    // Check if app is installed (PWA)
    isInstalled() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
    },

    // Show install prompt
    showInstallPrompt() {
        if (window.deferredPrompt) {
            window.deferredPrompt.prompt();
            window.deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('User accepted the install prompt');
                }
                window.deferredPrompt = null;
            });
        }
    }
});

// PWA Install Prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
    window.deferredPrompt = deferredPrompt;
});

// Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
