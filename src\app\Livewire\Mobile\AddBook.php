<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Book;
use App\Models\UserBook;
use App\Services\BookDiscovery\BookDiscoveryService;

class AddBook extends Component
{
    public $searchMethod = 'isbn'; // 'isbn', 'scan' - default to camera scanning
    public $isbn = '';
    public $searchResults = [];
    public $selectedBook = null;
    public $discoveredBookData = null;
    public $showBookTypeSelection = false;
    public $isScanning = false;
    public $cameraSupported = false;
    public $cameraInitializing = false;
    public $isLoading = false;
    public $errorMessage = '';
    public $successMessage = '';

    protected $listeners = ['checkCameraSupport', 'go-back', 'book-created'];

    public function mount()
    {
        // Camera support will be checked by Alpine.js component
    }

    public function checkCameraSupport($supported = false)
    {
        $this->cameraSupported = $supported;

        // If camera is not supported and we're defaulting to scan, switch to ISBN
        if (!$supported && $this->searchMethod === 'scan') {
            $this->searchMethod = 'isbn';
        }
    }

    public function updatedIsbn()
    {
        // Clear previous messages when ISBN changes
        $this->errorMessage = '';
        $this->successMessage = '';
        $this->selectedBook = null;

        // Auto-search if ISBN looks complete (10 or 13 digits)
        $cleanIsbn = $this->cleanIsbn($this->isbn);
        if (strlen($cleanIsbn) >= 10 && $this->isValidIsbn($cleanIsbn)) {
            // Small delay to avoid too many API calls while typing
            $this->dispatch('isbn-ready', $cleanIsbn);
        }
    }

    public function setSearchMethod($method)
    {
        $this->searchMethod = $method;
        $this->resetSearch();
    }

    public function resetSearch()
    {
        $this->isbn = '';
        $this->searchResults = [];
        $this->selectedBook = null;
        $this->isScanning = false;
        $this->errorMessage = '';
        $this->successMessage = '';
    }

    public function searchByIsbn()
    {
        $this->errorMessage = '';
        $this->successMessage = '';
        $this->showBookTypeSelection = false;

        // Validate ISBN format
        $cleanIsbn = $this->cleanIsbn($this->isbn);
        if (!$this->isValidIsbn($cleanIsbn)) {
            $this->errorMessage = __('mobile.invalid_isbn');
            return;
        }

        $this->isLoading = true;

        try {
            // Use the new Book Discovery Service
            $discoveryService = app(BookDiscoveryService::class);
            $bookData = $discoveryService->searchByIsbn($cleanIsbn);

            if ($bookData) {
                if (isset($bookData['exists_locally']) && $bookData['exists_locally']) {
                    // Book exists locally, show it directly
                    $this->selectedBook = $bookData;
                    $this->successMessage = __('mobile.book_found');
                } else {
                    // Book found from external source, show book type selection
                    $this->discoveredBookData = $bookData;
                    $this->showBookTypeSelection = true;
                    $this->successMessage = __('mobile.book_found_external');
                }
            } else {
                $this->errorMessage = __('mobile.book_not_found_try_again');
            }
        } catch (\Exception $e) {
            Log::error(__('mobile.isbn_search_error') . ' ' . $e->getMessage());
            $this->errorMessage = __('mobile.an_error_occurred_try_again');
        }

        $this->isLoading = false;
    }

    private function cleanIsbn($isbn)
    {
        // Remove all non-digit characters except X (for ISBN-10)
        return preg_replace('/[^0-9X]/i', '', strtoupper($isbn));
    }

    private function isValidIsbn($isbn)
    {
        $length = strlen($isbn);

        // Basic length check
        if ($length !== 10 && $length !== 13) {
            return false;
        }

        // Check if it contains only digits (and X for ISBN-10)
        if ($length === 10) {
            return preg_match('/^[0-9]{9}[0-9X]$/i', $isbn);
        } else {
            return preg_match('/^[0-9]{13}$/', $isbn);
        }
    }

    public function goBack()
    {
        $this->showBookTypeSelection = false;
        $this->discoveredBookData = null;
        $this->errorMessage = '';
        $this->successMessage = '';
    }

    public function bookCreated($data = null)
    {
        $this->showBookTypeSelection = false;
        $this->discoveredBookData = null;
        $this->successMessage = __('mobile.book_created_success');

        // Redirect to books page after short delay
        $this->dispatch('redirect-to-books');
    }

    public function addSelectedBook()
    {
        if (!$this->selectedBook) {
            $this->errorMessage = __('mobile.no_book_selected');
            return;
        }

        $user = Auth::user();

        try {
            // Check if book is already in user's list
            $existingUserBook = UserBook::where('user_id', $user->id)
                ->where('book_id', $this->selectedBook['id'])
                ->first();

            if ($existingUserBook) {
                $this->errorMessage = __('mobile.book_already_in_list');
                return;
            }

            // Add book to user's list
            UserBook::create([
                'user_id' => $user->id,
                'book_id' => $this->selectedBook['id'],
                'start_date' => now(),
            ]);

            session()->flash('success', __('mobile.book_added_success') );
            return redirect()->route('mobile.books');
        } catch (\Exception $e) {
            Log::error('Add book error: ' . $e->getMessage());
            $this->errorMessage = 'Failed to add book to your list. Please try again.';
        }
    }



    public function startScanning()
    {
        $this->cameraInitializing = true;
        $this->errorMessage = '';
        $this->dispatch('start-barcode-scanning');
    }

    public function cameraInitialized()
    {
        $this->cameraInitializing = false;
        $this->isScanning = true;
    }

    public function cameraError($message)
    {
        $this->cameraInitializing = false;
        $this->isScanning = false;
        $this->errorMessage = $message;
    }

    public function stopScanning()
    {
        $this->isScanning = false;
        $this->cameraInitializing = false;
        $this->dispatch('stop-barcode-scanning');
    }

    public function handleScannedCode($code)
    {
        Log::info('Barcode scanned: ' . $code);

        $this->stopScanning();

        // Clean and validate the scanned code
        $cleanIsbn = $this->cleanIsbn($code);
        Log::info('Cleaned ISBN: ' . $cleanIsbn);

        if ($this->isValidIsbn($cleanIsbn)) {
            $this->isbn = $cleanIsbn;
            $this->successMessage = __('mobile.barcode_scanned_successfully');

            Log::info('Valid ISBN detected, starting book search...');

            // Automatically search for the book using the scanned ISBN
            $this->searchByIsbn();
        } else {
            Log::warning('Invalid barcode format: ' . $code . ' (cleaned: ' . $cleanIsbn . ')');
            $this->errorMessage = __('mobile.invalid_barcode_format');
        }
    }

    public function render()
    {
        return view('livewire.mobile.add-book');
    }
}
