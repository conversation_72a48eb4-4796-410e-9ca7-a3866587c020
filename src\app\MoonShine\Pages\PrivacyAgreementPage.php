<?php

namespace App\MoonShine\Pages;

use App\Models\UserAgreement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use MoonShine\Laravel\Pages\Page;
use MoonShine\UI\Components\Layout\Box;
use MoonShine\UI\Components\Layout\Div;
use MoonShine\UI\Components\Layout\Flex;
use MoonShine\UI\Components\FormBuilder;
use MoonShine\UI\Components\Heading;
use MoonShine\UI\Components\Html;
use MoonShine\UI\Fields\Checkbox;
use MoonShine\UI\Fields\Hidden;
use MoonShine\Laravel\Http\Responses\MoonShineJsonResponse;

class PrivacyAgreementPage extends Page
{
    /**
     * Page identifier
     */
    protected ?string $alias = 'privacy-agreement';
    
    /**
     * Page title
     */
    public function getTitle(): string
    {
        return __('admin.privacy_agreement_title');
    }

    /**
     * Page breadcrumbs
     */
    public function getBreadcrumbs(): array
    {
        return [
            '#' => $this->getTitle(),
        ];
    }

    /**
     * Page components
     */
    protected function components(): iterable
    {
        return [
            Box::make([
                Heading::make(__('admin.privacy_agreement_title'))
                    ->h(1)
                    ->class('text-2xl font-bold mb-6'),

                // Privacy Policy Content
                Html::make('privacy-policy-content')
                    ->html($this->getPrivacyPolicyContent())
                    ->class('prose max-w-none mb-8 p-4 bg-gray-50 rounded-lg border max-h-96 overflow-y-auto'),

                // Consent Form
                FormBuilder::make(route('moonshine.process-privacy-consent'))
                    ->method('POST')
                    ->fields([
                        Hidden::make('_token')->value(csrf_token()),
                        
                        Div::make([
                            Checkbox::make(__('admin.privacy_agreement_consent'), 'privacy_consent')
                                ->required()
                                ->class('privacy-consent-checkbox')
                                ->attributes([
                                    'id' => 'privacy_consent',
                                    'onchange' => 'toggleSubmitButton()'
                                ]),
                        ])->class('mb-6'),

                        Flex::make([
                            Html::make('submit-button')
                                ->html('
                                    <button type="submit" 
                                            id="submit-btn" 
                                            disabled 
                                            class="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium disabled:bg-gray-400 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors">
                                        ' . __('admin.accept_and_continue') . '
                                    </button>
                                ')
                        ])->justifyBetween(),
                    ])
                    ->class('privacy-agreement-form')
                    ->attributes([
                        'onsubmit' => 'return validateForm()'
                    ]),

                // JavaScript for client-side validation
                Html::make('privacy-agreement-script')
                    ->html($this->getJavaScript()),
            ])
            ->class('max-w-4xl mx-auto')
        ];
    }

    /**
     * Get privacy policy content
     */
    protected function getPrivacyPolicyContent(): string
    {
        return '
            <h3 class="text-lg font-semibold mb-4">' . __('admin.privacy_policy_title') . '</h3>
            
            <div class="space-y-4">
                <p>' . __('admin.privacy_policy_intro') . '</p>
                
                <h4 class="font-semibold">' . __('admin.data_collection_title') . '</h4>
                <p>' . __('admin.data_collection_content') . '</p>
                
                <h4 class="font-semibold">' . __('admin.data_usage_title') . '</h4>
                <p>' . __('admin.data_usage_content') . '</p>
                
                <h4 class="font-semibold">' . __('admin.data_sharing_title') . '</h4>
                <p>' . __('admin.data_sharing_content') . '</p>
                
                <h4 class="font-semibold">' . __('admin.data_security_title') . '</h4>
                <p>' . __('admin.data_security_content') . '</p>
                
                <h4 class="font-semibold">' . __('admin.user_rights_title') . '</h4>
                <p>' . __('admin.user_rights_content') . '</p>
                
                <h4 class="font-semibold">' . __('admin.contact_info_title') . '</h4>
                <p>' . __('admin.contact_info_content') . '</p>
                
                <p class="text-sm text-gray-600 mt-6">
                    <strong>' . __('admin.last_updated') . ':</strong> ' . date('d.m.Y') . '<br>
                    <strong>' . __('admin.version') . ':</strong> ' . UserAgreement::CURRENT_PRIVACY_VERSION . '
                </p>
            </div>
        ';
    }

    /**
     * Get JavaScript for form validation
     */
    protected function getJavaScript(): string
    {
        return '
            <script>
                function toggleSubmitButton() {
                    const checkbox = document.getElementById("privacy_consent");
                    const submitBtn = document.getElementById("submit-btn");
                    
                    if (checkbox && submitBtn) {
                        submitBtn.disabled = !checkbox.checked;
                    }
                }
                
                function validateForm() {
                    const checkbox = document.getElementById("privacy_consent");
                    
                    if (!checkbox || !checkbox.checked) {
                        alert("' . __('admin.privacy_consent_required') . '");
                        return false;
                    }
                    
                    return true;
                }
                
                // Initialize on page load
                document.addEventListener("DOMContentLoaded", function() {
                    toggleSubmitButton();
                });
            </script>
        ';
    }

    /**
     * Handle privacy consent processing
     */
    public function processPrivacyConsent(Request $request)
    {
        // Validate the request
        $request->validate([
            'privacy_consent' => 'required|accepted',
        ], [
            'privacy_consent.required' => __('admin.privacy_consent_required'),
            'privacy_consent.accepted' => __('admin.privacy_consent_required'),
        ]);

        try {
            $user = Auth::guard('moonshine')->user();
            
            if (!$user) {
                return redirect()->route('moonshine.login')
                    ->with('error', __('admin.authentication_required'));
            }

            // Record the privacy policy acceptance
            UserAgreement::recordPrivacyPolicyAcceptance(
                $user->id,
                $request->ip()
            );

            // Get the intended URL or redirect to dashboard
            $intendedUrl = $request->session()->pull('privacy_agreement.intended_url', route('moonshine.index'));
            
            return redirect($intendedUrl)
                ->with('success', __('admin.privacy_agreement_accepted'));

        } catch (\Exception $e) {
            \Log::error('Privacy agreement acceptance failed', [
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return back()
                ->with('error', __('admin.privacy_agreement_error'))
                ->withInput();
        }
    }
}
