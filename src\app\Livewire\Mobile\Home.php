<?php

namespace App\Livewire\Mobile;

use App\Models\EnumTaskCycle;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserBook;
use App\Models\UserPoint;
use App\Models\UserReadingLog;
use App\Models\UserReward;
use App\Models\UserTask;
use App\Models\MessageRecipient;
use App\Services\TaskProgressCalculationService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class Home extends Component
{
    public $activeTab = 'read';
    public $user;
    public $greeting;
    public $stats = [];
    public $currentBooks;
    public $completedBooks;
    public $readingStreak = [];
    public $hasReadingHistory = false;
    public $avatarImage;
    public $assignedTasks = [];
    public $activeChallenges = [];
    public $unreadMessagesCount = 0;

    public function mount()
    {
        $this->user = Auth::user();
        $this->greeting = $this->getTimeBasedGreeting();
        $this->loadUserStats();
        $this->loadBooks();
        $this->loadReadingStreak();
        $this->loadAssignedTasks();
        $this->loadActiveChallenges();
        $this->loadUnreadMessagesCount();
        $this->avatarImage = $this->user->getAvatarDisplayImage();
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function logout()
    {
        Auth::logout();
        session()->invalidate();
        session()->regenerateToken();

        return redirect()->route('mobile.login');
    }

    private function getTimeBasedGreeting()
    {
        $hour = now()->hour;

        if ($hour < 12) {
            return __('mobile.good_morning');
        } elseif ($hour < 17) {
            return __('mobile.good_afternoon');
        } else {
            return __('mobile.good_evening');
        }
    }

    private function loadUserStats()
    {
        $userId = $this->user->id;

        $this->stats = [
            'books_read' => UserBook::where('user_id', $userId)->completed()->count(),
            'rewards_earned' => UserReward::where('user_id', $userId)->count(), // All rewards
            'total_points' => UserPoint::where('user_id', $userId)->sum('points') ?? 0,
            'current_level' => $this->user->getCurrentLevelNumber(),
        ];
    }

    private function loadBooks()
    {
        $userId = $this->user->id;

        $this->currentBooks = UserBook::where('user_id', $userId)
            ->inProgress()
            ->with('book')
            ->get();

        $this->completedBooks = UserBook::where('user_id', $userId)
            ->completed()
            ->with('book')
            ->latest('end_date')
            ->take(10)
            ->get();

        $this->hasReadingHistory = $this->currentBooks->count() > 0 || $this->completedBooks->count() > 0;
    }

    private function loadReadingStreak()
    {
        $userId = $this->user->id;
        $today = Carbon::today();
        $streak = [];

        // Get last 7 days
        for ($i = 6; $i >= 0; $i--) {
            $date = $today->copy()->subDays($i);
            $hasReading = UserReadingLog::where('user_id', $userId)
                ->whereDate('log_date', $date)
                ->exists();

            $streak[] = [
                'date' => $date,
                'day' => $date->format('j M'),
                'short_day' => $date->format('j'),
                'has_reading' => $hasReading,
                'is_today' => $date->isToday(),
            ];
        }

        $this->readingStreak = $streak;
    }

    public function getReadingProgress($userBook)
    {
        if (!$userBook->book->page_count) {
            return 0;
        }

        $totalPages = UserReadingLog::where('user_id', $userBook->user_id)
            ->where('book_id', $userBook->book_id)
            ->sum('pages_read');

        return min(100, ($totalPages / $userBook->book->page_count) * 100);
    }

    public function getChallengeInfo($userBook)
    {
        // Check if user has active challenge tasks that might relate to this book
        $activeChallenge = UserTask::where('user_id', $userBook->user_id)
            ->where('task_type', UserTask::TASK_TYPE_CHALLENGE)
            ->where('completed', false)
            ->with(['challengeTask.challenge', 'challengeTask.task.taskType'])
            ->whereHas('challengeTask.challenge', function ($query) {
                $query->where('active', true)
                      ->where('start_date', '<=', now())
                      ->where('end_date', '>=', now());
            })
            ->first();

        if ($activeChallenge) {
            return [
                'type' => 'challenge',
                'name' => $activeChallenge->challengeTask->challenge->name ?? __('mobile.challenge'),
                'progress' => $this->getReadingProgress($userBook)
            ];
        }

        // Default to general reading progress
        return [
            'type' => 'reading',
            'name' => __('mobile.progress'),
            'progress' => $this->getReadingProgress($userBook)
        ];
    }

    public function getTotalPagesRead($userBook)
    {
        return UserReadingLog::where('user_id', $userBook->user_id)
            ->where('book_id', $userBook->book_id)
            ->sum('pages_read');
    }

    /**
     * Check if a book has incomplete required activities.
     */
    public function hasIncompleteRequiredActivities($book)
    {
        // Create a temporary UserReadingLog instance to use the method
        $tempLog = new UserReadingLog([
            'user_id' => $this->user->id,
            'book_id' => $book->id,
        ]);

        $incompleteActivities = $tempLog->getIncompleteRequiredActivities();
        return count($incompleteActivities) > 0;
    }

    /**
     * Load assigned tasks for the current user.
     */
    private function loadAssignedTasks()
    {        
        $this->assignedTasks = UserTask::where('user_id', $this->user->id)
            ->where('task_type', UserTask::TASK_TYPE_STANDALONE)
//            ->where('completed', false)
            ->where('due_date', '>=', now())        
            ->with(['task.taskType', 'task.taskCycle', 'assignedBy', 'reward'])
            ->orderBy('due_date', 'asc')
            ->orderBy('assign_date', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($userTask) {
                return [
                    'id' => $userTask->id,
                    'name' => $userTask->getTaskNameWithTeamOrClassName(),
                    'description' => $userTask->task->description,
                    'type' => $userTask->task->taskType->name,
                    'cycle' => $userTask->task->taskCycle->name,
                    'value' => $userTask->task->task_value,
                    'unit' => $this->getTaskTypeUnit($userTask),
                    'due_date' => $userTask->due_date,
                    'assigned_by' => $userTask->assignedBy->name ?? '',
                    'progress' => $this->calculateTaskProgress($userTask),
                    'reward' => $userTask->reward ? [
                        'name' => $userTask->reward->name,
                        'type' => $userTask->reward->reward_type_display,
                        'image' => $userTask->reward->image,
                    ] : null,
                ];
            });
    }
    private function getTaskTypeUnit($userTask)
    {
        // if task cycle is total, return taskType->unit, if task cycle is daily return 'day', if weekly return 'week', if monthly return 'month'
        // refactor using switch    
        switch ($userTask->task->taskCycle->nr) {
            case EnumTaskCycle::TOTAL:
                return $userTask->task->taskType->unit;
            case EnumTaskCycle::DAILY:
                return __('mobile.days');
            case EnumTaskCycle::WEEKLY:
                return __('mobile.weeks');
            case EnumTaskCycle::MONTHLY:
                return __('mobile.months');
            default:
                return $userTask->task->taskType->unit ?? '';
        }        
    }

    /**
     * Load unread messages count for the current user.
     */
    private function loadUnreadMessagesCount()
    {
        $this->unreadMessagesCount = MessageRecipient::where('user_id', $this->user->id)
            ->where('read', false)
            ->count();
    }

    /**
     * Load active challenges for the current user.
     */
    private function loadActiveChallenges()
    {
        $this->activeChallenges = UserTask::where('user_id', $this->user->id)
            ->where('task_type', UserTask::TASK_TYPE_CHALLENGE)
            ->where('completed', false)
            ->with(['challengeTask.challenge', 'challengeTask.task.taskType'])
            ->whereHas('challengeTask.challenge', function ($query) {
                $query->where('active', true)
                      ->where('start_date', '<=', now())
                      ->where('end_date', '>=', now());
            })
            ->limit(3)
            ->get()
            ->map(function ($userTask) {
                return [
                    'id' => $userTask->id,
                    'challenge_name' => $userTask->challengeTask->challenge->name,
                    'task_name' => $userTask->challengeTask->task->name,
                    'task_type' => $userTask->challengeTask->task->taskType->name,
                    'end_date' => $userTask->challengeTask->challenge->end_date,
                    'progress' => $this->calculateChallengeProgress($userTask),
                ];
            });
    }

    /**
     * Calculate progress for a standalone task using TaskProgressCalculationService.
     */
    private function calculateTaskProgress($userTask)
    {
        $service = app(TaskProgressCalculationService::class);
        return $service->calculateProgress($userTask);
    }

    /**
     * Calculate progress for a challenge task using TaskProgressCalculationService.
     */
    private function calculateChallengeProgress($userTask)
    {
        $service = app(TaskProgressCalculationService::class);
        return $service->calculateProgress($userTask);
    }

    public function render()
    {
        return view('livewire.mobile.home');
    }
}
