<div class="min-h-screen bg-base-200">
    <x-mobile-page-header route="{{ route('mobile.books') }}" header="{{ __('mobile.activities') }}" />

    <div class="p-4">
        <!-- Required Activities Warning -->
        @php
            $incompleteRequiredActivities = collect($availableActivities)->where('is_required', true)->count() +
                                          collect($rejectedActivities)->where('is_required', true)->count() +
                                          collect($failedActivities)->where('is_required', true)->count();
        @endphp

        @if($incompleteRequiredActivities > 0)
            <div class="bg-orange-50 border border-orange-200 rounded-2xl p-4 mb-6">
                <div class="flex items-start space-x-3">
                    <span class="text-2xl">⚠️</span>
                    <div class="flex-1">
                        <h4 class="font-semibold text-orange-900 mb-2">{{ __('mobile.required_activities_pending') }}</h4>
                        <p class="text-sm text-orange-800">
                            {{ __('mobile.complete_required_activities_message') }}
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Book Info -->
        <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
            <div class="flex items-center space-x-4">
                <img src="{{ $book->cover_image ? asset('storage/' . $book->cover_image) : '/images/default-book-cover.png' }}"
                     alt="{!! $book->name !!}"
                     class="mobile-book-cover">

                <div class="flex-1">
                    <h3 class="font-semibold text-gray-900 mb-1">{!! $book->name !!}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ $book->authors->pluck('name')->join(', ') }}</p>
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="mobile-badge">⭐  {{ __('mobile.earn_points') }}</span>
                        @if($userBook && $userBook->isCompleted())
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-semibold">✅ {{ __('mobile.completed') }}</span>
                        @else
                            <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-semibold">📖 {{ __('mobile.in_progress') }}</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        @if(session('success'))
            <div class="bg-green-50 border border-green-200 rounded-xl p-3 mb-4">
                <p class="text-green-600 text-sm">{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-50 border border-red-200 rounded-xl p-3 mb-4">
                <p class="text-red-600 text-sm">{{ session('error') }}</p>
            </div>
        @endif

        <!-- Avatar Unlock Progress -->
        @if($avatarUnlockProgress && $avatarUnlockProgress['can_unlock'])
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-4 mb-6">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🎭</span>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-purple-900 mb-1">{{ __('mobile.avatar_unlock_progress') }}</h4>
                        <p class="text-sm text-purple-700">
                            {!! __('mobile.complete_activities_to_unlock', ['points' => $avatarUnlockProgress['points_needed']])  !!}
                        </p>
                        <p class="text-xs text-purple-600 mt-1">
                             {{ __('mobile.next_unlock') .  $avatarUnlockProgress['next_avatar']->name }}
                        </p>
                    </div>
                </div>
            </div>
        @endif

        <!-- Pending Activities -->
        @if(count($pendingActivities) > 0)
            <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
                <h3 class="font-semibold text-gray-900 mb-4">⏳ {{ __('mobile.pending_approval') }}</h3>
                <div class="space-y-3">
                    @foreach($pendingActivities as $activity)
                        <div class="border border-yellow-200 bg-yellow-50 rounded-xl p-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                                    @if($activity['activity_type'] === 'writing')
                                        <span class="text-2xl">✍️</span>
                                    @elseif($activity['activity_type'] === 'rating')
                                        <span class="text-2xl">⭐</span>
                                    @elseif($activity['activity_type'] === 'upload')
                                        <span class="text-2xl">🎨</span>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">{{ $activity['name'] }}</h4>
                                    <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                                    <p class="text-sm text-yellow-600 font-semibold mt-1">
                                         {{ __('mobile.submitted_on') . \Carbon\Carbon::parse($activity['user_activity']['activity_date'])->format('M d, Y') }}
                                    </p>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-yellow-600">{{ $activity['points'] }}</div>
                                    <div class="text-xs text-gray-600">points</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Completed Activities -->
        @if(count($completedActivities) > 0)
            <div class="bg-white rounded-2xl p-4 mb-6 shadow-sm">
                <h3 class="font-semibold text-gray-900 mb-4">✅ {{ __('mobile.completed_activities') }}</h3>
                <div class="space-y-3">
                    @foreach($completedActivities as $activity)
                        <div class="border border-green-200 bg-green-50 rounded-xl p-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                                    @if($activity['activity_type'] === 'writing')
                                        <span class="text-2xl">✍️</span>
                                    @elseif($activity['activity_type'] === 'rating')
                                        <span class="text-2xl">⭐</span>
                                    @elseif($activity['activity_type'] === 'upload')
                                        <span class="text-2xl">🎨</span>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">{{ $activity['name'] }}</h4>
                                    <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                                    <p class="text-sm text-green-600 font-semibold mt-1">
                                        ✅ {{ __('mobile.completed_on') . ' ' . \Carbon\Carbon::parse($activity['user_activity']['activity_date'])->format('M d, Y') }}
                                    </p>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <div class="text-center">
                                        <div class="text-lg font-bold text-green-600">+{{ $activity['points'] }}</div>
                                        <div class="text-xs text-gray-600">{{ __('mobile.points') }}</div>
                                    </div>
                                    <button
                                        wire:click="viewActivity({{ $activity['id'] }})"
                                        class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-lg hover:bg-blue-200 transition-colors"
                                    >
                                        {{ __('mobile.view') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Rejected Activities -->
        @if(count($rejectedActivities) > 0)
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-semibold text-gray-900 mb-4">❌ {{ __('mobile.rejected_activities') }}</h3>
                <div class="space-y-3">
                    @foreach($rejectedActivities as $activity)
                        <div class="border border-red-200 rounded-xl p-4 bg-red-50">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                                    @if($activity['activity_type'] === 'writing')
                                        <span class="text-2xl">✍️</span>
                                    @elseif($activity['activity_type'] === 'rating')
                                        <span class="text-2xl">⭐</span>
                                    @elseif($activity['activity_type'] === 'upload')
                                        <span class="text-2xl">🎨</span>
                                    @else
                                        <span class="text-2xl">📝</span>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">{{ $activity['name'] }}</h4>
                                    <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                                    <p class="text-sm text-red-600 font-semibold mt-1">
                                        ❌ {{ __('mobile.rejected') }} - {{ __('mobile.please_revise_and_resubmit') }}
                                    </p>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <div class="text-center">
                                        <div class="text-lg font-bold text-gray-600">{{ $activity['points'] }}</div>
                                        <div class="text-xs text-gray-600">{{ __('mobile.points') }}</div>
                                    </div>
                                    <button
                                        wire:click="resubmitActivity({{ $activity['id'] }})"
                                        class="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-lg hover:bg-orange-200 transition-colors"
                                    >
                                        {{ __('mobile.edit_and_resubmit') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Failed Test Activities -->
        @if(count($failedActivities) > 0)
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-semibold text-gray-900 mb-4">💔 {{ __('mobile.failed_tests') }}</h3>
                <div class="space-y-3">
                    @foreach($failedActivities as $activity)
                        <div class="border border-red-200 rounded-xl p-4 bg-red-50">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                                    @if($activity['activity_type'] === 'quiz')
                                        <span class="text-2xl">📝</span>
                                    @elseif($activity['activity_type'] === 'vocabulary')
                                        <span class="text-2xl">📚</span>
                                    @else
                                        <span class="text-2xl">❌</span>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">
                                        {{ $activity['name'] }}
                                        @if($activity['is_required'] ?? false)
                                            <span class="text-red-500 text-sm">*</span>
                                        @endif
                                    </h4>
                                    <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                                    @if(isset($activity['test_results']))
                                        <p class="text-sm text-red-600 font-semibold mt-1">
                                            💔 {{ __('mobile.test_failed') }} - {{ $activity['test_results']['score_percentage'] }}%
                                            ({{ __('mobile.required') }}: {{ $activity['test_results']['min_grade_required'] }}%)
                                        </p>
                                    @else
                                        <p class="text-sm text-red-600 font-semibold mt-1">
                                            💔 {{ __('mobile.test_failed') }}
                                        </p>
                                    @endif
                                    <p class="text-xs text-gray-500 mt-1">
                                        {{ __('mobile.remaining_attempts') }}: {{ $activity['allowed_tries'] - $activity['attempt_count'] }}
                                    </p>
                                </div>
                                <div class="flex flex-col space-y-2">
                                    <div class="text-center">
                                        <div class="text-lg font-bold text-gray-400">{{ $activity['points'] }}</div>
                                        <div class="text-xs text-gray-400">{{ __('mobile.points') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Available Activities -->
        @if(count($availableActivities) > 0)
            <div class="bg-white rounded-2xl p-4 shadow-sm">
                <h3 class="font-semibold text-gray-900 mb-4">🎯 {{ __('mobile.available_activities') }}</h3>
                <div class="space-y-3">
                    @foreach($availableActivities as $activity)                    
                        <div
                            wire:click="startActivity({{ $activity['id'] }})"
                            class="border border-gray-200 rounded-xl p-4 hover:border-primary transition-colors cursor-pointer"
                        >
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-primary bg-opacity-10 rounded-xl flex items-center justify-center">
                                    @if($activity['activity_type'] === 'writing')
                                        <span class="text-2xl">✍️</span>
                                    @elseif($activity['activity_type'] === 'rating')
                                        <span class="text-2xl">⭐</span>
                                    @elseif($activity['activity_type'] === 'upload')
                                        <span class="text-2xl">🎨</span>
                                    @elseif($activity['activity_type'] === 'quiz')
                                        <span class="text-2xl">📝</span>
                                    @elseif($activity['activity_type'] === 'vocabulary')
                                        <span class="text-2xl">📚</span>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900">
                                        {{ $activity['name'] }}
                                        @if($activity['is_required'] ?? false)
                                            <span class="text-red-500 text-sm">*</span>
                                        @endif
                                    </h4>
                                    <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <span class="mobile-badge">{{ $activity['points'] }} {{ __('mobile.points') }}</span>
                                        @if($activity['activity_type'] === 'writing')
                                            <span class="text-xs text-gray-500">📝 {{ __('mobile.writing') }}</span>
                                        @elseif($activity['activity_type'] === 'rating')
                                            <span class="text-xs text-gray-500">⭐ {{ __('mobile.rating') }}</span>
                                        @elseif($activity['activity_type'] === 'upload')
                                            <span class="text-xs text-gray-500">🎨 {{ __('mobile.art_upload') }}</span>
                                        @elseif($activity['activity_type'] === 'quiz')
                                            <span class="text-xs text-gray-500">📝 {{ __('mobile.quiz') }}</span>
                                        @elseif($activity['activity_type'] === 'vocabulary')
                                            <span class="text-xs text-gray-500">📚 {{ __('mobile.vocabulary_test') }}</span>
                                        @endif
                                        @if($activity['is_required'] ?? false)
                                            <span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">{{ __('mobile.required') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <!-- No Available Activities -->
            <div class="bg-white rounded-3xl p-8 text-center shadow-sm">
                <div class="w-20 h-20 bg-gray-100 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">{{ __('mobile.all_activities_completed') }}</h3>
                <p class="text-gray-600">{{ __('mobile.great_job_completed') }}</p>
            </div>
        @endif
    </div>
</div>
