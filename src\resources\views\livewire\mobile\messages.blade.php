<div class="min-h-screen bg-gray-50 pb-20"> <!-- x-data="messagesPage" -->
    <x-mobile-page-header route="{{ route('mobile.home') }}" header="{{ __('mobile.my_messages') . ' ' . ($unreadCount > 0 ? '(' . $unreadCount . ')' : '') }}" />

    <!-- Messages List -->
    <div class="p-4 space-y-3">
        <!-- App Install Card - disabled for now 
        <div x-show="showInstallCard"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             class="bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg shadow-lg p-4 text-white">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="font-bold text-lg mb-1">{{ __('mobile.add_to_home_screen') }}</h3>
                    <p class="text-sm text-white/90 mb-3">{{ __('mobile.install_app_better') }}</p>
                    <button @click="installApp()"
                            class="w-full bg-white text-violet-600 py-2 px-4 rounded-lg font-semibold text-sm hover:bg-white/90 transition-colors">
                        {{ __('mobile.install') }}
                    </button>
                </div>
            </div>
        </div>
        -->
        <!-- Notification Permission Card - disabled for now 
        <div x-show="showNotificationCard"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-4"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg p-4 text-white">
            <div class="flex items-start space-x-3">
                <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                </div>
                <div class="flex-1">
                    <h3 class="font-bold text-lg mb-1">{{ __('mobile.enable_notifications') }}</h3>
                    <p class="text-sm text-white/90 mb-3">{{ __('mobile.get_notified_progress') }}</p>
                    <button @click="allowNotifications()"
                            class="w-full bg-white text-blue-600 py-2 px-4 rounded-lg font-semibold text-sm hover:bg-white/90 transition-colors">
                        {{ __('mobile.allow') }}
                    </button>
                </div>
            </div>
        </div>
        -->

        @forelse($messages as $messageRecipient)
            <div wire:click="viewMessage({{ $messageRecipient->id }})" 
                 class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow {{ !$messageRecipient->read ? 'border-l-4 border-blue-500' : '' }}">
                
                <!-- Message Header -->
                <div class="flex items-start justify-between mb-2">
                    <h3 class="font-bold text-lg {{ !$messageRecipient->read ? 'text-blue-600' : 'text-gray-800' }}">
                        {!! $messageRecipient->message->title !!}
                    </h3>
                    @if(!$messageRecipient->read)
                        <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                            {{ __('mobile.unread') }}
                        </span>
                    @endif
                </div>

                <!-- Message Date -->
                <div class="flex items-center text-xs text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {{ $messageRecipient->sent_date->format('d.m.Y H:i') }}
                </div>
            </div>
        @empty
            <!-- No Messages State -->
            <div class="text-center py-12">
                <div class="mb-4">
                    <svg class="w-24 h-24 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">{{ __('mobile.no_messages') }}</h3>
                <p class="text-gray-500">{{ __('mobile.no_unread_messages') }}</p>
            </div>
        @endforelse
    </div>
</div>
<!-- 
// manual guides for now 

<script>
// Capture the beforeinstallprompt event
window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();
    // Stash the event so it can be triggered later
    window.deferredPrompt = e;
    console.log('beforeinstallprompt event captured');

    // Trigger Alpine to re-check PWA status
    if (window.Alpine) {
        // Dispatch a custom event that Alpine can listen to
        window.dispatchEvent(new CustomEvent('pwa-prompt-ready'));
    }
});

document.addEventListener('alpine:init', () => {
    Alpine.data('messagesPage', () => ({
        showInstallCard: false,
        showNotificationCard: false,

        init() {
            this.checkPWAStatus();

            // Listen for PWA prompt ready event
            window.addEventListener('pwa-prompt-ready', () => {
                this.checkPWAStatus();
            });

            // Re-check periodically in case the event fires before Alpine is ready
            const checkInterval = setInterval(() => {
                if (window.deferredPrompt && !this.showInstallCard) {
                    this.checkPWAStatus();
                }
            }, 500);

            // Stop checking after 5 seconds
            setTimeout(() => clearInterval(checkInterval), 5000);
        },

        checkPWAStatus() {
            // Check if app is installed
            const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                               window.navigator.standalone === true;

            // Check notification permission
            const notificationPermission = 'Notification' in window ? Notification.permission : 'denied';

            // Show install card ONLY if app is not installed AND deferredPrompt exists
            this.showInstallCard = !isInstalled && !!window.deferredPrompt;

            // Show notification card ONLY if notifications permission is 'default'
            this.showNotificationCard = notificationPermission === 'default';

            console.log('PWA Status Check:', {
                isInstalled,
                hasDeferredPrompt: !!window.deferredPrompt,
                notificationPermission,
                showInstallCard: this.showInstallCard,
                showNotificationCard: this.showNotificationCard
            });
        },

        installApp() {
            if (window.deferredPrompt) {
                // Show the install prompt
                window.deferredPrompt.prompt();

                // Wait for the user to respond to the prompt
                window.deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                        this.showInstallCard = false;
                    } else {
                        console.log('User dismissed the install prompt');
                        // Card remains visible - user must take action
                    }
                    // Clear the deferredPrompt for next time
                    window.deferredPrompt = null;
                });
            } else {
                console.log('Install prompt not available');
            }
        },

        async allowNotifications() {
            if ('Notification' in window) {
                const permission = await Notification.requestPermission();
                console.log('Notification permission:', permission);

                if (permission === 'granted') {
                    this.showNotificationCard = false;

                    // Initialize FCM if available
                    if (typeof window.MobileApp !== 'undefined' && window.MobileApp.initializeFCM) {
                        window.MobileApp.initializeFCM();
                    }
                } else if (permission === 'denied') {
                    // Hide card if user explicitly denied
                    this.showNotificationCard = false;
                }
                // If permission is still 'default', card remains visible
            }
        }
    }));
});
</script>
-->
