<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class MobileAuthentication
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            // Store intended URL for redirect after login
            if (!$request->wantsJson() && !$request->header('X-Livewire')) {
                session(['mobile_intended_url' => $request->url()]);
            }

            // If it's an AJAX request (Livewire), return JSON response
            if ($request->wantsJson() || $request->header('X-Livewire')) {
                return response()->json([
                    'message' => __('mobile.login_again'),
                    'redirect' => route('mobile.login')
                ], 401);
            }

            // Otherwise redirect to mobile login with flash message
            return redirect()->route('mobile.login')
                ->with('info', __('mobile.login_to_continue') );
        }

        // Update last activity timestamp
        session(['mobile_last_activity' => now()]);

        // Check if user has student role (assuming role-based access)
        $user = Auth::user();

        // You can add additional role checks here if needed
        // For now, we'll allow any authenticated user

        return $next($request);
    }
}
