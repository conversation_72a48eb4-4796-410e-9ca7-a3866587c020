SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;


INSERT INTO `activities` (`id`, `category_id`, `activity_type`, `name`, `description`, `question_count`, `choices_count`, `min_grade`, `required`, `allowed_tries`, `min_word_count`, `min_rating`, `max_rating`, `media_url`, `points`, `need_approval`, `media_type`, `active`) VALUES
(1, 1, 1, 'Kitap Yorum<PERSON>', '<PERSON>u kitap hakkındaki düşüncelerin ne<PERSON>? Bize anlatabilir misin?', NULL, NULL, NULL, 0, 1, 10, NULL, NULL, NULL, 10, 0, NULL, 1),
(2, 2, 2, 'Kitaba puan ver', NULL, NULL, NULL, NULL, 0, 1, NULL, 1, 10, NULL, 3, 0, NULL, 1),
(3, 3, 3, 'Sen olsaydın bu kitap için nasıl bir kapak tasarlardın? Çiz, fotoğrafını çek gönder', NULL, NULL, NULL, NULL, 0, 1, NULL, NULL, NULL, NULL, 10, 1, 1, 1),
(4, 6, 6, 'Kitap sorularını çöz', NULL, 5, 4, 70, 0, 1, NULL, NULL, NULL, NULL, 10, 0, NULL, 1);

INSERT INTO `activity_categories` (`id`, `name`, `active`) VALUES
(1, 'Fikir ve Yorumlar', 1),
(2, 'Puanlamalar', 1),
(3, 'Sanatsal Çalışmalar', 1),
(4, 'Fiziki Aktiviteler', 1),
(5, 'Kelime Oyunları', 1),
(6, 'Testler', 1);

INSERT INTO `authors` (`id`, `created_by`, `name`) VALUES
(1, NULL, 'Mustafa Orakçı'),
(2, NULL, 'Mustafa Ecevit');

INSERT INTO `avatars` (`id`, `name`, `description`, `base_image`, `happy_image`, `sad_image`, `sleepy_image`, `required_points`, `active`) VALUES
(1, 'Pembe Robot', 'Pembe Robot', 'azBpgaQpXwGEMWZdC7nyxEeX9DuoW1hD5DhJKcLf.png', 'zvEtCV4OhP71wzstyeQxXemxOyAyAba4vZsisIVg.png', 'g8o4fnIQioQ49FY48VxZnu00vs2t6rFP9WORqEW5.png', 'Qzr6gINt2sIvdIlT5MVeWFVC4zC6dJ1bv42a4jDi.png', 0, 1),
(2, 'Erkek çocuk', 'Erkek çocuk', 'G0BgZ6x8lW0z8YDHFnhiJIbBzMwlzmYzzwJrAr7l.png', '2VJRNTNrKJvH3Y0enaAgVPHF1HCVYy1JKCROKewD.png', 'IRodA38uh3xqlz9huGJuKMTU8xrD6jHOzE2WB5bZ.png', 'N6FD80wCs2wbSVcO8fpftcByuVcXawJHlEL33Tas.png', 0, 1),
(3, 'Aslancık', 'Aslancık', '93spVyVBI3AxJdY2Z5uVPtweN7uIzRLPQ3NfiSLg.png', '1Q5DMyOp7vg2DwyF46nydGTEU49TAPMLZKk5ZwXE.png', '5GZ7o5xLmdFMtl9kLsANYHXkwYDYLBniZJkP0Di7.png', 'ddr67gpBgA5ADfgpTYVaRQTumX8dvgBuzpSuCVXg.png', 20, 1);

INSERT INTO `books` (`id`, `created_by`, `name`, `isbn`, `publisher_id`, `book_type_id`, `page_count`, `year_of_publish`, `cover_image`, `active`) VALUES
(1, NULL, 'Dünyadaki Son Robot - Haşmet 2.0', '9786255978189', 1, 3, 64, '2025', 'axtBxIa4OTrxoraX2rxPEggRfNGBVHrKOx7gwDjc.jpg', 1),
(2, NULL, 'Kikurcan ve Hayalet Tayfa 3 - Sisler Ormanı', '9786257841412', 2, 2, 88, '2021', 'vCy8slp0SFGTOIkcPQ4dB8EXd4UNaaJOZ90VRT69.jpg', 1),
(3, NULL, 'Hacı Bektaşi Veli&#039;yle Bir Gün', '9786050820966', 1, 2, 48, '2015', 'oiDqpNqVJXlzBKDRBBML38yoKZSBcpKGLdUfLfO8.jpg', 1);

INSERT INTO `book_authors` (`id`, `book_id`, `author_id`) VALUES
(1, 1, 1),
(2, 2, 2),
(3, 3, 1);

INSERT INTO `book_categories` (`id`, `book_id`, `category_id`) VALUES
(1, 1, 2),
(2, 2, 2),
(3, 3, 1);

INSERT INTO `book_types` (`id`, `name`, `description`, `thumbnail`) VALUES
(1, 'Çok resimli az yazılı', 'Çok resimli az yazılı', '3r1wZxOsn8evJz2fg1PA0g8pYmLvbZj6j4cIao9x.png'),
(2, 'Resim ve yazı dengeli', 'Resim ve yazı dengeli', 'PCOg4iexj4iEXQ9aO1Qc0whz9a8yYvywg7loojnr.png'),
(3, 'Çok yazılı az resimli', 'Çok yazılı az resimli', '6efgIr4dWkhyekOn3KVjMhnmB1A2TM9XYk157Lri.png'),
(4, 'Tamamen yazılı büyük puntolu', 'Tamamen yazılı büyük puntolu', 'ktVb9L9g3J66lzrdTdZx8lyrFxhuArEP635TX7wH.png'),
(5, 'Tamamen yazılı küçük puntolu', 'Tamamen yazılı küçük puntolu', 'g9qOtekn8aUW7CSUPM8s9Xnryp8vBNXrwcvfHcSB.png');

INSERT INTO `categories` (`id`, `name`) VALUES
(1, 'Hikaye'),
(2, 'Roman');

INSERT INTO `challenges` (`id`, `created_by`, `name`, `description`, `image`, `start_date`, `end_date`, `prize`, `active`) VALUES
(2, NULL, 'Yaz yarışması', '', NULL, '2025-08-08', '2025-09-07', '', 1),
(3, 5, 'test', 'test açıklama', 'challenges/TknNr7pioqkX2zkzbJnGSCEv21UnTWbeC4762B0L.jpg', '2025-08-28', '2025-09-10', 'ödül', 1),
(4, 5, 'test yarışma', 'aaaa', 'challenges/gnvlvtOnanl7V8v4k8Vj5Xbi9QNEqfq4Tt7e6Bmu.jpg', '2025-08-29', '2025-09-11', 'aaaa', 0),
(5, 5, 'test2', 'aaaa', 'challenges/2m3sfRBTOiuTh3ve2OBLOjYN3eg41mqLv7OJ822x.png', '2025-08-29', '2025-09-11', 'ğaei', 0);

INSERT INTO `challenge_classes` (`id`, `challenge_id`, `school_class_id`) VALUES
(2, 2, 1);

INSERT INTO `challenge_schools` (`id`, `challenge_id`, `school_id`) VALUES
(2, 2, 1);

INSERT INTO `challenge_tasks` (`id`, `created_by`, `challenge_id`, `task_id`, `start_date`, `end_date`, `reward_id`) VALUES
(3, NULL, 2, 2, '2025-08-08', '2025-09-07', NULL);

INSERT INTO `challenge_teams` (`id`, `challenge_id`, `team_id`) VALUES
(2, 2, 1);

INSERT INTO `class_activities` (`id`, `class_id`, `activity_id`, `question_count`, `min_grade`, `allowed_tries`, `min_word_count`, `points`, `required`, `need_approval`, `active`) VALUES
(1, 1, 4, 5, 70, 1, NULL, 10, 0, 0, 1),
(2, 2, 4, 5, 70, 1, NULL, 10, 0, 0, 1);

INSERT INTO `enum_class_levels` (`id`, `name`) VALUES
(1, '1.Sınıf'),
(2, '2.Sınıf'),
(3, '3.Sınıf'),
(4, '4.Sınıf'),
(5, '5.Sınıf');

INSERT INTO `enum_school_types` (`id`, `name`) VALUES
(1, 'İlkokul'),
(2, 'Ortaokul');

INSERT INTO `enum_task_cycles` (`id`, `nr`, `name`) VALUES
(1, 1, 'Toplam'),
(2, 2, 'Günlük'),
(3, 3, 'Haftalık'),
(4, 4, 'Aylık');

INSERT INTO `enum_task_types` (`id`, `nr`, `name`) VALUES
(1, 1, 'Sayfa Oku'),
(2, 2, 'Kitap Oku'),
(3, 3, 'Dakika Oku'),
(4, 4, 'Gün Oku'),
(5, 5, 'Gün Arka Arkaya Oku'),
(6, 6, 'Okuma Puanı Kazan'),
(7, 7, 'Aktivite Puanı Kazan'),
(8, 8, 'Bir Kitap Aktivitesi Tamamla'),
(9, 9, 'Kitap Listesini Tamamla'),
(10, 10, 'Evet/Hayır Görevi');

INSERT INTO `goals` (`id`, `created_by`, `name`, `description`, `motto`, `active`) VALUES
(1, NULL, '100 sayfa kitap oku', NULL, NULL, 1),
(2, NULL, 'Aylık 3 adet hikaye oku ve kitaba yorum yaz', NULL, '3 hikaye okuyan kazanır', 1),
(8, 1, 'Aylık 3 adet hikaye oku ve kitaba yorum yaz', NULL, '3 hikaye okuyan kazanır', 1),
(9, NULL, 'Yeni hedef', NULL, NULL, 1);

INSERT INTO `goal_tasks` (`id`, `created_by`, `goal_id`, `task_id`, `start_date`, `end_date`) VALUES
(1, NULL, 1, 1, '2025-08-08', '2025-09-07'),
(2, NULL, 2, 2, '2025-08-08', '2025-09-07'),
(3, NULL, 2, 3, '2025-08-08', '2025-09-07'),
(4, NULL, 9, 12, '2025-09-02', '2025-10-02');

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(77, '0001_01_01_000000_create_users_table', 1),
(78, '0001_01_01_000001_create_cache_table', 1),
(79, '0001_01_01_000002_create_jobs_table', 1),
(80, '2020_10_04_115514_create_moonshine_roles_table', 1),
(81, '2020_10_05_173148_create_moonshine_tables', 1),
(82, '2024_12_19_000001_create_user_agreements_table', 1),
(83, '2025_01_01_000010_create_permission_tables', 1),
(84, '2025_01_04_000000_create_enum_class_levels_table', 1),
(85, '2025_01_04_000010_create_enum_school_types_table', 1),
(86, '2025_01_04_000020_create_schools_table', 1),
(87, '2025_01_04_000030_create_school_classes_table', 1),
(88, '2025_01_04_120000_create_user_schools_table', 1),
(89, '2025_01_04_120001_create_user_classes_table', 1),
(90, '2025_03_17_073436_create_notifications_table', 1),
(91, '2025_06_04_090749_create_publishers_table', 1),
(92, '2025_06_04_090750_create_authors_table', 1),
(93, '2025_06_04_090751_create_book_types_table', 1),
(94, '2025_06_04_090751_create_books_table', 1),
(95, '2025_06_04_090751_create_page_points_table', 1),
(96, '2025_06_04_090752_create_book_authors_table', 1),
(97, '2025_06_04_090752_create_categories_table', 1),
(98, '2025_06_04_090753_create_book_categories_table', 1),
(99, '2025_06_04_090754_create_class_books_table', 1),
(100, '2025_06_04_090757_create_activity_categories_table', 1),
(101, '2025_06_04_090758_create_activities_table', 1),
(102, '2025_06_04_091008_create_book_questions_table', 1),
(103, '2025_06_04_091009_create_book_words_table', 1),
(104, '2025_06_04_091804_create_avatars_table', 1),
(105, '2025_06_04_190764_create_user_avatars_table', 1),
(106, '2025_06_04_190770_create_teams_table', 1),
(107, '2025_06_04_190771_create_user_teams_table', 1),
(108, '2025_06_04_190790_create_enum_task_types_table', 1),
(109, '2025_06_04_190791_create_enum_task_cycles_table', 1),
(110, '2025_06_04_190792_create_tasks_table', 1),
(111, '2025_06_04_190793_create_task_books_table', 1),
(112, '2025_06_04_190794_create_task_book_categories_table', 1),
(113, '2025_06_04_190800_create_goals_table', 1),
(114, '2025_06_04_190801_create_goal_tasks_table', 1),
(115, '2025_06_04_190802_create_user_goals_table', 1),
(116, '2025_07_29_124912_create_activity_log_table', 1),
(117, '2025_07_29_124913_add_event_column_to_activity_log_table', 1),
(118, '2025_07_29_124914_add_batch_uuid_column_to_activity_log_table', 1),
(119, '2025_08_08_120000_create_challenges_table', 1),
(120, '2025_08_08_120001_create_challenge_tasks_table', 1),
(121, '2025_08_08_120002_create_challenge_classes_table', 1),
(122, '2025_08_08_120003_create_challenge_schools_table', 1),
(123, '2025_08_08_120004_create_challenge_teams_table', 1),
(124, '2025_08_08_120006_create_user_tasks_table', 1),
(125, '2025_08_18_090755_create_user_reading_logs_table', 1),
(126, '2025_08_18_090756_create_user_points_table', 1),
(127, '2025_08_18_090759_create_user_activities_table', 1),
(128, '2025_08_18_090760_create_user_activity_reviews_table', 1),
(129, '2025_08_18_090762_create_user_books_table', 1),
(130, '2025_08_20_120000_create_rewards_table', 1),
(131, '2025_08_20_120001_create_reward_tasks_table', 1),
(132, '2025_08_20_120002_create_user_rewards_table', 1),
(133, '2025_08_20_120003_create_team_rewards_table', 1),
(134, '2025_08_20_120004_add_reward_id_to_challenge_tasks_table', 1),
(135, '2025_08_20_130000_remove_badge_system_tables', 1),
(136, '2025_08_23_140002_set_initial_default_schools_and_classes', 1),
(137, '2025_08_25_000000_add_permission_fields_to_tables', 1),
(138, '2025_09_03_000000_create_class_activities_table', 1),
(139, 'add_role_priority_to_roles_table', 1),
(140, 'create_or_supplement_users_table', 1);

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(2, 'App\\Models\\User', 4),
(3, 'App\\Models\\User', 5),
(3, 'App\\Models\\User', 6),
(3, 'App\\Models\\User', 7),
(3, 'App\\Models\\User', 8),
(4, 'App\\Models\\User', 9),
(4, 'App\\Models\\User', 10),
(4, 'App\\Models\\User', 11),
(4, 'App\\Models\\User', 12),
(4, 'App\\Models\\User', 13),
(4, 'App\\Models\\User', 14),
(4, 'App\\Models\\User', 15),
(4, 'App\\Models\\User', 16),
(4, 'App\\Models\\User', 17),
(4, 'App\\Models\\User', 18),
(4, 'App\\Models\\User', 19),
(4, 'App\\Models\\User', 20),
(4, 'App\\Models\\User', 21),
(4, 'App\\Models\\User', 22),
(4, 'App\\Models\\User', 23),
(4, 'App\\Models\\User', 24),
(4, 'App\\Models\\User', 25),
(4, 'App\\Models\\User', 26),
(4, 'App\\Models\\User', 27),
(4, 'App\\Models\\User', 28),
(4, 'App\\Models\\User', 30);

INSERT INTO `moonshine_user_roles` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Admin', '2025-09-11 09:38:47', '2025-09-11 09:38:47');

INSERT INTO `page_points` (`id`, `book_type_id`, `class_level_id`, `point`) VALUES
(1, 1, 1, 1.00),
(2, 1, 2, 0.75),
(3, 1, 3, 0.50),
(4, 1, 4, 0.25),
(5, 1, 5, 0.10),
(6, 2, 1, 2.00),
(7, 2, 2, 1.75),
(8, 2, 3, 1.50),
(9, 2, 4, 1.00),
(10, 2, 5, 0.75),
(11, 3, 1, 3.00),
(12, 3, 2, 2.50),
(13, 3, 3, 2.00),
(14, 3, 4, 1.50),
(15, 3, 5, 1.00),
(16, 4, 1, 3.00),
(17, 4, 2, 2.50),
(18, 4, 3, 2.00),
(19, 4, 4, 1.50),
(20, 4, 5, 1.25),
(21, 5, 1, 4.00),
(22, 5, 2, 3.50),
(23, 5, 3, 3.00),
(24, 5, 4, 2.50),
(25, 5, 5, 2.00);

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'RoleResource.viewAny', 'moonshine', '2025-08-07 23:37:18', '2025-08-07 23:37:18'),
(2, 'RoleResource.view', 'moonshine', '2025-08-07 23:37:18', '2025-08-07 23:37:18'),
(3, 'RoleResource.create', 'moonshine', '2025-08-07 23:37:18', '2025-08-07 23:37:18'),
(4, 'RoleResource.update', 'moonshine', '2025-08-07 23:37:18', '2025-08-07 23:37:18'),
(5, 'RoleResource.delete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(6, 'RoleResource.massDelete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(7, 'RoleResource.restore', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(8, 'RoleResource.forceDelete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(9, 'PermissionResource.viewAny', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(10, 'PermissionResource.view', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(11, 'PermissionResource.create', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(12, 'PermissionResource.update', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(13, 'PermissionResource.delete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(14, 'PermissionResource.massDelete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(15, 'PermissionResource.restore', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(16, 'PermissionResource.forceDelete', 'moonshine', '2025-08-07 23:37:19', '2025-08-07 23:37:19'),
(17, 'EnumSchoolTypeResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(18, 'EnumClassLevelResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(19, 'SchoolResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(20, 'SchoolClassResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(21, 'UserResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(22, 'UserSchoolResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(23, 'UserClassResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(24, 'TeamResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(25, 'UserTeamResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(26, 'BookTypeResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(27, 'AuthorResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(28, 'PublisherResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(29, 'CategoryResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(30, 'BookResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(31, 'ClassBookResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(32, 'BookQuestionResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(33, 'BookWordResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(34, 'UserBookResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(35, 'UserReadingLogResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(36, 'UserPointResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(37, 'ActivityCategoryResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(38, 'ActivityResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(39, 'UserActivityResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(40, 'UserActivityReviewResource.viewAny', 'moonshine', '2025-08-24 06:26:45', '2025-08-24 06:26:45'),
(41, 'AvatarResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(42, 'UserAvatarResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(43, 'RewardResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(44, 'RewardTaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(45, 'UserRewardResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(46, 'TeamRewardResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(47, 'EnumTaskTypeResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(48, 'EnumTaskCycleResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(49, 'TaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(50, 'GoalResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(51, 'GoalTaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(52, 'UserGoalResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(53, 'UserGoalTaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(54, 'ChallengeResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(55, 'ChallengeTaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(56, 'UserChallengeTaskResource.viewAny', 'moonshine', '2025-08-24 06:26:46', '2025-08-24 06:26:46'),
(57, 'StudentResource.view', 'moonshine', '2025-08-31 04:35:00', '2025-08-31 04:35:00'),
(58, 'StudentResource.viewAny', 'moonshine', '2025-08-31 04:35:14', '2025-08-31 04:35:14'),
(59, 'StudentResource.create', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12'),
(60, 'StudentResource.update', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12'),
(61, 'StudentResource.delete', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12'),
(62, 'StudentResource.massDelete', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12'),
(63, 'StudentResource.restore', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12'),
(64, 'StudentResource.forceDelete', 'moonshine', '2025-08-31 11:11:12', '2025-08-31 11:11:12');

INSERT INTO `publishers` (`id`, `created_by`, `name`) VALUES
(1, NULL, 'Timaş Çocuk'),
(2, NULL, 'Çamlıca Çocuk Yayınları');

INSERT INTO `rewards` (`id`, `created_by`, `reward_type`, `name`, `description`, `image`, `active`) VALUES
(1, NULL, 1, 'İlk Kitabı Bitirdin', 'İlk Kitabı Okumayı Bitirdin', 'sBEyq5YbQdcTwGTlcg96BXjJx1PsbTTMKcXsGnSQ.png', 1),
(2, NULL, 1, '3 Kitap Bitirdin', NULL, 'H3csIYqeZJSn1NUaiOkljLp4UFkxqwcaIf2RQd41.png', 1);

INSERT INTO `reward_tasks` (`id`, `created_by`, `reward_id`, `task_id`) VALUES
(1, NULL, 1, 4),
(2, NULL, 2, 5);

INSERT INTO `roles` (`id`, `name`, `guard_name`, `role_priority`, `description`, `created_at`, `updated_at`) VALUES
(1, 'system_admin', 'moonshine', NULL, 'Sistem Yöneticisi', '2025-08-07 23:12:52', '2025-08-07 23:37:10'),
(2, 'school_admin', 'moonshine', NULL, 'Okul Yöneticisi', '2025-08-07 23:12:52', '2025-08-07 23:37:25'),
(3, 'teacher', 'moonshine', NULL, 'Öğretmen', '2025-08-07 23:12:52', '2025-08-07 23:37:34'),
(4, 'student', 'moonshine', NULL, 'Öğrenci', '2025-08-07 23:12:52', '2025-08-07 23:37:43'),
(5, 'parent', 'moonshine', NULL, 'Ebeveyn', '2025-08-10 09:57:15', '2025-08-10 10:05:02'),
(6, 'region_user', 'moonshine', NULL, 'Bölge Kullanıcısı', '2025-08-22 10:37:57', '2025-08-22 10:37:57');

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(57, 3),
(58, 3),
(59, 3),
(60, 3),
(61, 3),
(62, 3),
(63, 3),
(64, 3);

INSERT INTO `schools` (`id`, `name`, `school_type_id`, `active`) VALUES
(1, 'Örnek İlkokulu', 1, 1),
(2, 'Örnek Ortaokulu', 2, 1);

INSERT INTO `school_classes` (`id`, `created_by`, `name`, `school_id`, `class_level_id`, `active`) VALUES
(1, NULL, '3-A', 1, 3, 1),
(2, NULL, '5-A', 2, 5, 1);

INSERT INTO `tasks` (`id`, `created_by`, `name`, `task_type_id`, `task_cycle_id`, `task_value`, `activity_id`, `active`) VALUES
(1, NULL, '100 sayfa kitap oku', 1, 1, 100, NULL, 1),
(2, NULL, 'Aylık 3 Adet Hikaye Oku', 2, 4, 5, NULL, 1),
(3, NULL, 'Haşmet kitabına yorum yaz', 8, 1, 1, 1, 1),
(4, NULL, 'Rozet-1 Kitap Bitir', 2, 1, 1, NULL, 1),
(5, NULL, 'Rozet-3 Kitap Bitir', 2, 1, 3, NULL, 1),
(11, NULL, '5 Kitap Oku', 2, 1, 5, NULL, 1),
(12, NULL, 'test görev', 1, 1, 5, NULL, 1),
(13, NULL, 'eeeee', 1, 1, 4, NULL, 1),
(14, NULL, 'aaaaaaaaa', 2, 1, 5, NULL, 1),
(15, NULL, 'ieaiaeei', 1, 1, 6, NULL, 1),
(16, NULL, 'uieaueiauia', 1, 1, 33, NULL, 1);

INSERT INTO `task_books` (`id`, `created_by`, `task_id`, `book_id`) VALUES
(1, NULL, 3, 1);

INSERT INTO `task_book_categories` (`id`, `created_by`, `task_id`, `category_id`) VALUES
(1, NULL, 2, 1);

INSERT INTO `teams` (`id`, `created_by`, `name`, `logo`, `leader_user_id`, `active`) VALUES
(1, NULL, 'Aslanlar', NULL, 9, 1);

INSERT INTO `team_rewards` (`id`, `created_by`, `team_id`, `reward_id`, `awarded_date`, `awarded_by`, `reading_log_id`, `user_activity_id`) VALUES
(5, NULL, 1, 1, '2025-09-01 01:42:04', NULL, 33, NULL),
(6, NULL, 1, 2, '2025-09-01 01:42:04', NULL, 33, NULL);

INSERT INTO `users` (`id`, `username`, `name`, `title`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`, `avatar`) VALUES
(1, 'admin', 'system_admin', 'System Admin', '<EMAIL>', '2025-08-07 23:16:57', '$2y$12$3LJG0CA.vQi0eagzScHoQulZ2sKK7ZPf4yBes9BljaV3H9tIArQDu', NULL, '2025-08-07 23:15:33', '2025-08-07 23:16:57', NULL),
(4, 'schooladmin', 'Ahmet Yılmaz', 'School Administrator', '<EMAIL>', '2025-08-07 23:16:57', '$2y$12$vkPUcmpjQ3bfho8hl.EUseSRRVFBmT0Vq2IGEk6OV6QpoTBzJ.TDC', NULL, '2025-08-07 23:16:57', '2025-08-07 23:16:57', NULL),
(5, 'teacher1', 'Fatma Demir', 'Turkish Language Teacher', '<EMAIL>', '2025-08-07 23:16:57', '$2y$12$9VEiT6m0zVOoXfJIakACmu3JjSXOeYMVHS4nnIcsn8i/ju.u4x9g2', 'Ikq51IDvbGlejBWYTHupetjfsMZBOw1SGtBI74xYhVFDof7cPoK5v5bcleir', '2025-08-07 23:16:57', '2025-08-07 23:16:57', NULL),
(6, 'teacher2', 'Mehmet Kaya', 'Literature Teacher', '<EMAIL>', '2025-08-07 23:16:58', '$2y$12$MhNGdqsgBN6p1Y3x2Wayb.nuBjhqTr.rAq/6H4sToTn3rYIAP/SGq', NULL, '2025-08-07 23:16:58', '2025-08-07 23:16:58', NULL),
(7, 'teacher3', 'Ayşe Özkan', 'Primary School Teacher', '<EMAIL>', '2025-08-07 23:16:58', '$2y$12$ErZJ5//hwa7..JHQojMfh.2w40bN.7lkhjKWhk/j7rr4Rqtn6ByEi', NULL, '2025-08-07 23:16:58', '2025-08-07 23:16:58', NULL),
(8, 'teacher4', 'Ali Çelik', 'Reading Specialist', '<EMAIL>', '2025-08-07 23:16:58', '$2y$12$KzCf3oAHEtGFZl8FfovjA.trgQgSYewEAunBlRTR4ZrHGvpec.VJW', NULL, '2025-08-07 23:16:58', '2025-08-07 23:16:58', NULL),
(9, 'student1', 'Beyza Akçay', 'Student', '<EMAIL>', '2025-08-07 23:16:58', '$2y$12$G.2MiazFpLudCHFtbQDUKenAFE3nynGpVkz4JJCqdswRLO1my4nwu', 'PPPOevoa8undk4opMYIIt5FOSWS4ce27KsWHUFPAYsJ2TyQNGz6iZ2M4SoA2', '2025-08-07 23:16:58', '2025-08-19 03:56:34', NULL),
(10, 'student2', 'Emre Yıldız', 'Student', '<EMAIL>', '2025-08-07 23:16:59', '$2y$12$6SyzuG6S0aQfO049s08aDe3jcyGag8W4PO4yBdH04gb3GmgGvIrRO', 't4630TgIIWK2HVe993kAcW7pFjZQRU1fekdqU9e1b3GKWDaWp4LOnMp8zvgu', '2025-08-07 23:16:59', '2025-08-07 23:16:59', NULL),
(11, 'student3', 'Selin Koç', 'Student', '<EMAIL>', '2025-08-07 23:16:59', '$2y$12$F8CHAisb/lVhDDX1FqYsKOCiTXg7Mm4Dc8TwHfLmiJo3EgeJ2F1qq', NULL, '2025-08-07 23:16:59', '2025-08-07 23:16:59', NULL),
(12, 'student4', 'Burak Şahin', 'Student', '<EMAIL>', '2025-08-07 23:16:59', '$2y$12$gqwB0OTK5BcbuPUPXNPzUes/qnVxD6SPHrEkT7wTZ1o0YBXdC/IFK', 'Uyczfo3RyskNRO4LNm39e84OJl2BlPq6uETWjzLtMyWxp92y7DNX17Bgrlu5', '2025-08-07 23:16:59', '2025-08-07 23:16:59', NULL),
(13, 'student5', 'Elif Arslan', 'Student', '<EMAIL>', '2025-08-07 23:16:59', '$2y$12$Uzqr4ELhy8EUryFv1I.9i.ictpc5vOscoUiRpCG8Tl1LoLBqslq72', NULL, '2025-08-07 23:16:59', '2025-08-07 23:16:59', NULL),
(14, 'student6', 'Cem Doğan', 'Student', '<EMAIL>', '2025-08-07 23:17:00', '$2y$12$sGxncfF4/.bczXkI3DySlOX0G52TxGP0YDSFSrlwvTKaP08VgVoEW', NULL, '2025-08-07 23:17:00', '2025-08-07 23:17:00', NULL),
(15, 'student7', 'Nisa Güler', 'Student', '<EMAIL>', '2025-08-07 23:17:00', '$2y$12$n291L7/q1SbDOl.O9NWsb.4BS63vDxCdmCztOat6mT71E7zQ9PZgy', NULL, '2025-08-07 23:17:00', '2025-08-07 23:17:00', NULL),
(16, 'student8', 'Kaan Özdemir', 'Student', '<EMAIL>', '2025-08-07 23:17:00', '$2y$12$HIDdKoPcFAi3QthNd2F5WONMPapHD7jgYfzTwfz2H9.3Da1D0jT12', NULL, '2025-08-07 23:17:00', '2025-08-07 23:17:00', NULL),
(17, 'student9', 'Dila Aydın', 'Student', '<EMAIL>', '2025-08-07 23:17:00', '$2y$12$B4yyFMVgpGt1HXIQzT.dB.oZkShAy2cnNtdzPH95x4BFtW3FSGw7C', NULL, '2025-08-07 23:17:00', '2025-08-07 23:17:00', NULL),
(18, 'student10', 'Arda Polat', 'Student', '<EMAIL>', '2025-08-07 23:17:01', '$2y$12$ekmNkDWXY.MBmBPUn56u3el00KG6wAmVxldHK1psfQJX2BCqqfUY2', NULL, '2025-08-07 23:17:01', '2025-08-07 23:17:01', NULL),
(19, 'student11', 'İrem Çakır', 'Student', '<EMAIL>', '2025-08-07 23:17:01', '$2y$12$dondv.7nSZGswpBpK2OCwuNBe1.rxuPVe.coMtO6zjpbeadrqxwzi', NULL, '2025-08-07 23:17:01', '2025-08-07 23:17:01', NULL),
(20, 'student12', 'Emir Tunç', 'Student', '<EMAIL>', '2025-08-07 23:17:01', '$2y$12$CLZ2bMeFVGdrAuRn3bfHaeZkVyfaZnhBzxVtAwB2R/nPmHdqNQeN.', NULL, '2025-08-07 23:17:01', '2025-08-07 23:17:01', NULL),
(21, 'student13', 'Lara Öztürk', 'Student', '<EMAIL>', '2025-08-07 23:17:01', '$2y$12$Od3rZrU7hT3p1pd6ujdsVeSMATmi9Bmeq2rwrTr71px.95M3EzZAq', NULL, '2025-08-07 23:17:01', '2025-08-07 23:17:01', NULL),
(22, 'student14', 'Berat Kılıç', 'Student', '<EMAIL>', '2025-08-07 23:17:02', '$2y$12$xobcS2Kx72AlENLnHN55D.VbEvCAmBI1kIN7RhVZcFamYNz3TkYDC', NULL, '2025-08-07 23:17:02', '2025-08-07 23:17:02', NULL),
(23, 'student15', 'Ela Yaman', 'Student', '<EMAIL>', '2025-08-07 23:17:02', '$2y$12$.MC6hccpSH4iI9EKirXP0eqQgqqg3eaJgvUSY2wBNJjteQjYytVMm', NULL, '2025-08-07 23:17:02', '2025-08-07 23:17:02', NULL),
(24, 'student16', 'Deniz Erdoğan', 'Student', '<EMAIL>', '2025-08-07 23:17:02', '$2y$12$27swCSIjSrYv6jaR9xmkAu9XjchafZ/NqH988tTkdPgZn2oTlMs6W', NULL, '2025-08-07 23:17:02', '2025-08-07 23:17:02', NULL),
(25, 'student17', 'Mira Başak', 'Student', '<EMAIL>', '2025-08-07 23:17:02', '$2y$12$7646tsKCZTNmmdsPy/f1wuJqsN0cUmtZogvC7PA0gETmuNFiJ4LFu', NULL, '2025-08-07 23:17:02', '2025-08-07 23:17:02', NULL),
(26, 'student18', 'Yusuf Karaca', 'Student', '<EMAIL>', '2025-08-07 23:17:03', '$2y$12$tiRtfFdGCoHkLlRIDB7OtuL5muY5.WRzdAvA6paRgVzevUe4yTawy', NULL, '2025-08-07 23:17:03', '2025-08-07 23:17:03', NULL),
(27, 'student19', 'Defne Çetin', 'Student', '<EMAIL>', '2025-08-07 23:17:03', '$2y$12$jXu4TlZsRgr.e84WHI22Pefo55wIJo/ZDXgGiZmkeM8r9d5eohQBK', NULL, '2025-08-07 23:17:03', '2025-08-07 23:17:03', NULL),
(28, 'student20', 'Eren Güneş', 'Student', '<EMAIL>', '2025-08-07 23:17:03', '$2y$12$2nr9XWAkkho73luCtagTHuHbf0406yKhnRz88V1dhwdlGa06ZUqse', NULL, '2025-08-07 23:17:03', '2025-08-07 23:17:03', NULL),
(30, 'student55', 'Ali Veli', NULL, '<EMAIL>', NULL, '$2y$12$.M0F3EbFepO3F33oSbbAB.UWOAz0bZx0qXXNzrzPaTaJLtvZcMRZ2', NULL, '2025-08-30 22:29:18', '2025-08-30 22:29:18', NULL);

INSERT INTO `user_activities` (`id`, `created_by`, `user_id`, `book_id`, `activity_id`, `activity_date`, `content`, `rating`, `media_url`, `status`, `goal_task_id`, `challenge_task_id`) VALUES
(1, NULL, 9, 2, 1, '2025-08-19 11:31:51', 'sence de bu kitap çok amatörce yazılmış değil mi arkadaşım?', NULL, NULL, 3, NULL, NULL),
(2, NULL, 9, 2, 2, '2025-08-18 15:00:00', 'nice', 7, NULL, 3, NULL, NULL),
(3, NULL, 9, 2, 3, '2025-08-19 10:28:55', '', NULL, 'user-activities/wfffID3d3eL3NqiFZAgUye8ZpgcPfYAR6CEECSqh.jpg', 0, NULL, NULL),
(4, NULL, 10, 1, 1, '2025-09-01 01:43:21', 'Çok güzel bir kitaptı. Arkadaşlarıma tavsiye edeceğim, onlar da okusunlar', NULL, NULL, 3, NULL, NULL),
(5, NULL, 10, 1, 2, '2025-09-01 01:43:54', 'bilmem', 8, NULL, 3, NULL, NULL),
(6, NULL, 10, 1, 3, '2025-09-01 01:44:35', 'çok uğraştım', NULL, 'user-activities/HFwB1in3Zm88kY27tGNCsoZA5xqI7uTnsN8OzPOy.png', 1, NULL, NULL);

INSERT INTO `user_activity_reviews` (`id`, `created_by`, `user_activity_id`, `review_date`, `reviewed_by`, `status`, `feedback`) VALUES
(1, NULL, 3, '2025-08-19', 1, 0, NULL),
(2, NULL, 6, '2025-09-01', 1, 1, NULL);

INSERT INTO `user_avatars` (`id`, `created_by`, `user_id`, `avatar_id`, `selected_at`) VALUES
(1, NULL, 9, 1, '2025-08-18 10:29:13'),
(2, NULL, 5, 2, '2025-08-23 23:41:12'),
(3, NULL, 10, 2, '2025-09-01 01:40:46');

INSERT INTO `user_books` (`id`, `created_by`, `user_id`, `book_id`, `start_date`, `end_date`, `goal_task_id`, `challenge_task_id`) VALUES
(1, NULL, 9, 1, '2025-08-19', '2025-08-19', NULL, NULL),
(2, NULL, 9, 2, '2025-08-19', '2025-08-19', NULL, NULL),
(4, NULL, 9, 3, '2025-08-21', NULL, NULL, NULL),
(5, NULL, 10, 1, '2025-09-01', '2025-09-01', NULL, NULL);

INSERT INTO `user_classes` (`id`, `created_by`, `user_id`, `class_id`, `school_id`, `active`, `default`) VALUES
(1, NULL, 9, 1, 1, 1, 1),
(2, NULL, 5, 1, 1, 1, 1),
(3, NULL, 30, 1, 1, 1, 1);

INSERT INTO `user_goals` (`id`, `created_by`, `goal_id`, `team_id`, `user_id`, `assigned_by`, `assign_date`, `comment`, `achieved`, `achieve_date`) VALUES
(1, NULL, 1, NULL, 9, 1, '2025-08-07 15:00:00', NULL, 0, NULL),
(3, NULL, 2, NULL, 9, 1, '2025-08-07 15:00:00', NULL, 0, NULL),
(4, NULL, 2, 1, NULL, 1, '2025-08-07 15:00:00', NULL, 0, NULL),
(5, NULL, 9, 1, NULL, 1, '2025-09-01 15:00:00', NULL, 0, NULL);

INSERT INTO `user_points` (`id`, `point_date`, `user_id`, `book_id`, `source_id`, `point_type`, `points`, `created_by`) VALUES
(1, '2025-08-19 02:59:26', 9, 1, 2, 1, 40, NULL),
(2, '2025-08-19 02:59:35', 9, 1, 3, 1, 40, NULL),
(3, '2025-08-19 03:17:20', 9, 1, 4, 1, 40, NULL),
(4, '2025-08-19 03:49:08', 9, 1, 5, 1, 20, NULL),
(5, '2025-08-19 03:49:18', 9, 1, 6, 1, 28, NULL),
(6, '2025-08-19 03:53:39', 9, 1, 7, 1, 28, NULL),
(7, '2025-08-19 04:46:53', 9, 2, 8, 1, 15, NULL),
(10, '2025-08-19 05:06:05', 9, 2, 11, 1, 15, NULL),
(11, '2025-08-19 05:25:26', 9, 2, 12, 1, 15, NULL),
(16, '2025-08-19 05:34:33', 9, 2, 17, 1, 15, NULL),
(17, '2025-08-19 05:36:07', 9, 2, 18, 1, 72, NULL),
(19, '2025-08-19 08:31:51', 9, 2, 1, 2, 10, NULL),
(20, '2025-08-19 08:35:57', 9, 2, 2, 2, 3, NULL),
(34, '2025-09-01 01:43:21', 10, 1, 4, 2, 10, NULL),
(35, '2025-09-01 01:43:54', 10, 1, 5, 2, 3, NULL),
(36, '2025-09-01 01:45:03', 10, 1, 6, 2, 10, NULL);

INSERT INTO `user_reading_logs` (`id`, `created_by`, `user_id`, `book_id`, `log_date`, `start_page`, `end_page`, `pages_read`, `reading_duration`, `book_completed`, `goal_task_id`, `challenge_task_id`) VALUES
(3, NULL, 9, 1, '2025-08-17 18:00:00', NULL, NULL, 20, 11, 0, NULL, NULL),
(4, NULL, 9, 1, '2025-08-17 18:00:00', NULL, NULL, 20, NULL, 0, NULL, NULL),
(5, NULL, 9, 1, '2025-08-17 18:00:00', NULL, NULL, 10, 5, 0, NULL, NULL),
(7, NULL, 9, 1, '2025-08-17 18:00:00', NULL, NULL, 14, 10, 1, NULL, NULL),
(8, NULL, 9, 2, '2025-08-17 18:00:00', NULL, NULL, 10, NULL, 0, NULL, NULL),
(11, NULL, 9, 2, '2025-08-17 18:00:00', NULL, NULL, 10, NULL, 0, NULL, NULL),
(12, NULL, 9, 2, '2025-08-17 18:00:00', NULL, NULL, 10, 15, 0, NULL, NULL),
(17, NULL, 9, 2, '2025-08-17 18:00:00', NULL, NULL, 10, 20, 0, NULL, NULL),
(18, NULL, 9, 2, '2025-08-17 18:00:00', NULL, NULL, 48, 50, 1, NULL, NULL),
(33, NULL, 10, 1, '2025-08-30 18:00:00', NULL, NULL, 25, NULL, 0, NULL, NULL),
(34, NULL, 10, 1, '2025-08-30 18:00:00', NULL, NULL, 39, NULL, 1, NULL, NULL);

INSERT INTO `user_rewards` (`id`, `created_by`, `user_id`, `reward_id`, `awarded_date`, `awarded_by`, `reading_log_id`, `user_activity_id`) VALUES
(5, NULL, 9, 1, '2025-08-31 03:51:51', 1, NULL, NULL),
(6, NULL, 10, 1, '2025-09-01 01:42:19', NULL, 34, NULL);

INSERT INTO `user_schools` (`id`, `created_by`, `user_id`, `school_id`, `role_id`, `active`, `default`) VALUES
(2, NULL, 9, 1, 4, 1, 1),
(3, NULL, 5, 1, 3, 1, 1),
(4, NULL, 30, 1, 4, 1, 1);

INSERT INTO `user_teams` (`id`, `created_by`, `team_id`, `user_id`) VALUES
(1, NULL, 1, 9),
(2, NULL, 1, 10),
(3, NULL, 1, 11);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
