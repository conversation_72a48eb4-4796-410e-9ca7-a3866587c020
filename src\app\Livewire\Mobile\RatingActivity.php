<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Book;
use App\Models\Activity;
use App\Models\UserActivity;


class RatingActivity extends Component
{
    public $book;
    public $activity;
    public $rating = 0;
    public $comment = '';
    public $isLoading = false;
    public $mode = 'create'; // 'create', 'view', 'edit'
    public $userActivity = null;

    public function mount($book, $activity, $mode = 'create')
    {
        $this->book = Book::with(['authors'])->findOrFail($book);
//        $user = Auth::user();
//        $this->activity = Activity::resolvedForUser($user)->findOrFail($activity);
        $this->activity = Activity::findOrFail($activity);
        $this->mode = $mode;

        // Check if activity is rating type
        if ($this->activity->activity_type !== Activity::ACTIVITY_TYPE_RATING) {
            abort(404);
        }

        // Get existing user activity if any
        $this->userActivity = UserActivity::where('user_id', Auth::id())
            ->where('book_id', $this->book->id)
            ->where('activity_id', $this->activity->id)
            ->first();

        // Handle different modes
        if ($this->mode === 'create' && $this->userActivity && $this->userActivity->status !== UserActivity::STATUS_REJECTED) {
            session()->flash('error', __('mobile.already_completed_activity'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        if (($this->mode === 'view' || $this->mode === 'edit') && !$this->userActivity) {
            session()->flash('error', __('mobile.activity_not_found'));
            return redirect()->route('mobile.books.activities', $this->book->id);
        }

        // Load existing content for view/edit modes
        if ($this->userActivity && ($this->mode === 'view' || $this->mode === 'edit')) {
            $this->rating = $this->userActivity->rating ?? 0;
            $this->comment = $this->userActivity->content ?? '';
        }
    }

    public function setRating($rating)
    {
        $this->rating = $rating;
    }

    public function submitActivity()
    {
        if ($this->mode === 'view') {
            return; // No submission in view mode
        }

        $minRating = $this->activity->min_rating ?? 1;
        $maxRating = $this->activity->max_rating ?? 5;

        $this->validate([
            'rating' => "required|integer|min:{$minRating}|max:{$maxRating}",
            'comment' => 'nullable|string|max:500',
        ]);

        $this->isLoading = true;

        try {
            // Get challenge task IDs from user_books
            $userBook = \App\Models\UserBook::where('user_id', Auth::id())
                ->where('book_id', $this->book->id)
                ->first();

            $activityData = [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'activity_id' => $this->activity->id,
                'content' => $this->comment,
                'rating' => $this->rating,
                'activity_date' => now(),
            ];

            if ($this->mode === 'edit' && $this->userActivity) {
                // Update existing activity
                $this->userActivity->update($activityData);
                $userActivity = $this->userActivity->fresh();
            } else {
                // Create new activity
                $userActivity = UserActivity::create($activityData);
            }

            $successMessage = __('mobile.thank_you_for_your_submission');
            if ($this->activity->need_approval) {
                $successMessage .= ' ' . __('mobile.your_submission_is_pending_teacher_approval');
            } else {
                // Check for rewards if activity doesn't need approval (immediate completion)
                $rewardRedirect = $this->checkForRewards($userActivity);
                if ($rewardRedirect) {
                    return $rewardRedirect;
                }
            }

            session()->flash('success', $successMessage);
            return redirect()->route('mobile.books.activities', $this->book->id);
        } catch (\Exception $e) {
            Log::error(__('mobile.failed_to_submit_rating_try_again'), [
                'user_id' => Auth::id(),
                'book_id' => $this->book->id,
                'activity_id' => $this->activity->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->isLoading = false;
            session()->flash('error', __('mobile.failed_to_submit_rating_try_again'));
        }
    }

    /**
     * Check for newly unlocked rewards after activity completion.
     */
    private function checkForRewards($userActivity)
    {
        $rewardService = app(\App\Services\MobileRewardDisplayService::class);

        // Check for newly unlocked rewards and levels
        $rewardResult = $rewardService->checkForRewards(null, $userActivity->id, false);

        if ($rewardResult && $rewardResult['redirect_to_celebration']) {
            // Set redirect back to activities page after celebration
            $rewardService->setRedirectRoute('mobile.books.activities', [$this->book->id]);

            return redirect()->route('mobile.badge-unlocked');
        }

        return null;
    }

    public function render()
    {
        return view('livewire.mobile.rating-activity', [
            'activity' => $this->activity, // Pass resolved activity with class-specific settings
        ]);
    }
}
