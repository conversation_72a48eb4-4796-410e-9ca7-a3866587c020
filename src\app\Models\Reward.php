<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Reward extends BaseModel
{
    // Reward type constants
    const TYPE_BADGE = 1;
    const TYPE_GIFT = 2;
    const TYPE_TROPHY = 3;
    const TYPE_CARD = 3;
    const TYPE_ITEM = 4;

    protected $fillable = [
        'reward_type',
        'name',
        'description',
        'image',
        'active',
        'repeatable',
        'created_by',
    ];

    protected $casts = [
        'reward_type' => 'integer',
        'active' => 'boolean',
        'repeatable' => 'boolean',
    ];

    // fill created_by field with current user if not provided and user role is not system admin
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($reward) {
            $reward->created_by = null;
            if (!auth()->user()->isSystemAdmin()) {
                $reward->created_by = auth()->id();
            }
        });
    }

    public function getIsSystemRewardAttribute(): bool
    {
        return $this->created_by === null;
    } 

    /**
     * Get the tasks associated with this reward.
     */
    public function tasks(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'reward_tasks');
    }

    /**
     * Get the reward tasks for this reward.
     */
    public function rewardTasks(): HasMany
    {
        return $this->hasMany(RewardTask::class);
    }

    /**
     * Get the user rewards for this reward.
     */
    public function userRewards(): HasMany
    {
        return $this->hasMany(UserReward::class);
    }

    /**
     * Get the team rewards for this reward.
     */
    public function teamRewards(): HasMany
    {
        return $this->hasMany(TeamReward::class);
    }

    /**
     * Get the user who created this reward.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the challenge tasks that award this reward.
     */
    public function challengeTasks(): HasMany
    {
        return $this->hasMany(ChallengeTask::class);
    }

    /**
     * Scope for active rewards.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope for rewards by type.
     */
    public function scopeByType($query, int $type)
    {
        return $query->where('reward_type', $type);
    }

    /**
     * Scope for badges.
     */
    public function scopeBadges($query)
    {
        return $query->where('reward_type', self::TYPE_BADGE);
    }

    /**
     * Scope for gifts.
     */
    public function scopeGifts($query)
    {
        return $query->where('reward_type', self::TYPE_GIFT);
    }

    /**
     * Scope for trophies.
     */
    public function scopeTrophies($query)
    {
        return $query->where('reward_type', self::TYPE_TROPHY);
    }

    /**
     * Scope for cards.
     */
    public function scopeCards($query)
    {
        return $query->where('reward_type', self::TYPE_CARD);
    }

    /**
     * Scope for items.
     */
    public function scopeItems($query)
    {
        return $query->where('reward_type', self::TYPE_ITEM);
    }

    /**
     * Scope for system rewards.
     */
    public function scopeSystemRewards($query)
    {
        return $query->whereNull('created_by');
    }

    /**
     * Scope for user rewards.
     */
    public function scopeUserRewards($query)
    {
        return $query->whereNotNull('created_by');
    }

    /**
     * Scope for repeatable rewards.
     */
    public function scopeRepeatable($query)
    {
        return $query->where('repeatable', true);
    }

    /**
     * Scope for non-repeatable rewards.
     */
    public function scopeNonRepeatable($query)
    {
        return $query->where('repeatable', false);
    }

    /**
     * Get reward type display name.
     */
    public function getRewardTypeDisplayAttribute(): string
    {
        return match($this->reward_type) {
            self::TYPE_BADGE => __('admin.badge'),
            self::TYPE_GIFT => __('admin.gift'),
            self::TYPE_TROPHY => __('admin.trophy'),
            self::TYPE_CARD => __('admin.card'),
            self::TYPE_ITEM => __('admin.item'),
            default => __('admin.unknown'),
        };
    }

    /**
     * Get reward type icon.
     */
    public function getRewardTypeIconAttribute(): string
    {
        return match($this->reward_type) {
            self::TYPE_BADGE => 'star',
            self::TYPE_GIFT => 'gift',
            self::TYPE_TROPHY => 'trophy',
            self::TYPE_CARD => 'credit-card',
            self::TYPE_ITEM => 'cube',
            default => 'question-mark',
        };
    }

    /**
     * Check if this reward has been earned by a user.
     */
    public function isEarnedByUser(int $userId): bool
    {
        return $this->userRewards()->where('user_id', $userId)->exists();
    }

    /**
     * Check if this reward has been earned by a team.
     */
    public function isEarnedByTeam(int $teamId): bool
    {
        return $this->teamRewards()->where('team_id', $teamId)->exists();
    }

    /**
     * Award this reward to a user.
     */
    public function awardToUser(int $userId, ?int $awardedBy = null, ?int $readingLogId = null, ?int $userActivityId = null): ?UserReward
    {
        // Check if user already has this reward (only for non-repeatable rewards)
        if (!$this->repeatable && $this->isEarnedByUser($userId)) {
            return null;
        }

        return UserReward::create([
            'user_id' => $userId,
            'reward_id' => $this->id,
            'awarded_date' => now(),
            'awarded_by' => $awardedBy,
            'reading_log_id' => $readingLogId,
            'user_activity_id' => $userActivityId,
        ]);
    }

    /**
     * Award this reward to a team.
     */
    public function awardToTeam(int $teamId, ?int $awardedBy = null, ?int $readingLogId = null, ?int $userActivityId = null): ?TeamReward
    {
        // Check if team already has this reward (only for non-repeatable rewards)
        if (!$this->repeatable && $this->isEarnedByTeam($teamId)) {
            return null;
        }

        return TeamReward::create([
            'team_id' => $teamId,
            'reward_id' => $this->id,
            'awarded_date' => now(),
            'awarded_by' => $awardedBy,
            'reading_log_id' => $readingLogId,
            'user_activity_id' => $userActivityId,
        ]);
    }

    /**
     * Get users count who earned this reward.
     */
    public function getUsersEarnedCountAttribute(): int
    {
        return $this->userRewards()->distinct('user_id')->count('user_id');
    }

    /**
     * Get teams count who earned this reward.
     */
    public function getTeamsEarnedCountAttribute(): int
    {
        return $this->teamRewards()->distinct('team_id')->count('team_id');
    }

    /**
     * Get tasks count associated with this reward.
     */
    public function getTasksCountAttribute(): int
    {
        return $this->tasks()->count();
    }

    /**
     * Get summary information.
     */
    public function getSummaryAttribute(): string
    {
        $usersCount = $this->users_earned_count;
        $teamsCount = $this->teams_earned_count;
        $tasksCount = $this->tasks_count;

        return "Users: {$usersCount}, Teams: {$teamsCount}, Tasks: {$tasksCount}";
    }

    /**
     * Get all reward types for forms.
     */
    public static function getRewardTypes(): array
    {
        return [
            self::TYPE_BADGE => __('admin.badge'),
            self::TYPE_GIFT => __('admin.gift'),
            self::TYPE_TROPHY => __('admin.trophy'),
            self::TYPE_CARD => __('admin.card'),
            self::TYPE_ITEM => __('admin.item'),
        ];
    }
    // filter reward types, only return badges, trophies and gifts
    public static function getVisibleRewardTypes(): array
    {
        return [
            self::TYPE_BADGE => __('admin.badge'),
            self::TYPE_GIFT => __('admin.gift'),
            self::TYPE_TROPHY => __('admin.trophy'),
        ];
    }

    // Get reward awarding type, if it has tasks associated with it, it's automatic, otherwise it's manual
    public function getAwardingTypeAttribute(): string
    {
        return $this->tasks->isNotEmpty() ? __('admin.automatic') : __('admin.manual');
    }
}
