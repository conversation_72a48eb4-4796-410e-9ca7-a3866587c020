<?php

namespace App\Livewire\Mobile;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\UserBook;

class Profile extends Component
{
    public $user;
    public $stats = [];
    public $readingStreak = [];
    public $motivationalMessage;
    public $recentRewards = [];
    public $hasReadingHistory = false;

    public function mount()
    {
        $this->user = Auth::user();
        $this->loadUserStats();
        $this->loadReadingStreak();
        $this->loadRecentRewards();
        $this->checkReadingHistory();
    }

    private function loadUserStats()
    {
        $this->stats = [
            'books_completed' => $this->user->getTotalBooksCompleted(),
            'page_points' => $this->user->getTotalPagePoints(),
            'rewards_earned' => $this->user->getTotalRewards(),
            'activity_points' => $this->user->getTotalActivityPoints(),
            'level_progress' => $this->user->getLevelProgress(),
        ];
    }

    private function loadReadingStreak()
    {
        // Get reading pattern analysis with motivational message
        $analysis = $this->user->getReadingPatternAnalysis();
        
        $this->readingStreak = $analysis['reading_data'];
        $this->motivationalMessage = $analysis['message'];
    }

    private function loadRecentRewards()
    {
        $this->recentRewards = $this->user->getRecentRewardsByLimit(5);
    }

    private function checkReadingHistory()
    {
        $userId = $this->user->id;
        
        $hasCurrentBooks = UserBook::where('user_id', $userId)
            ->inProgress()
            ->exists();
            
        $hasCompletedBooks = UserBook::where('user_id', $userId)
            ->completed()
            ->exists();

        $this->hasReadingHistory = $hasCurrentBooks || $hasCompletedBooks;
    }

    public function navigateToAvatarSelection()
    {
        return redirect()->route('mobile.choose-avatar');
    }

    public function navigateToMyRewards()
    {
        return redirect()->route('mobile.my-rewards');
    }

    public function render()
    {
        return view('livewire.mobile.profile');
    }
}
